# 绿盟漏扫接口文档

## 概述

本文档描述了绿盟漏洞扫描系统的API接口，提供漏洞详细评分列表和IP风险汇总结果的查询功能。

## 基础信息

- **基础URL**: `/vulnerabilityScannings`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "page": 1,
    "page_size": 10,
    "total_page": 5,
    "total_count": 45,
    "list": [
      // 数据列表
    ]
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功，-1表示失败 |
| msg | string | 响应消息 |
| data | object | 响应数据对象 |
| data.page | integer | 当前页码 |
| data.page_size | integer | 每页数据量 |
| data.total_page | integer | 总页数 |
| data.total_count | integer | 总记录数 |
| data.list | array | 数据列表 |

## 接口列表

### 1. 获取漏洞详细评分列表

#### 接口信息

- **URL**: `/vulnerabilityScannings/vulnDetailedScoresList`
- **方法**: GET
- **描述**: 获取漏洞详细评分数据，包含每个漏洞的详细信息和风险评分

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页数据量，范围1-1000 |

#### 请求示例

```bash
GET /vulnerabilityScannings/vulnDetailedScoresList?page=1&size=10
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "page": 1,
    "page_size": 10,
    "total_page": 15,
    "total_count": 150,
    "list": [
      {
        "plugin_id": "12345",
        "vuln_level": "high",
        "target_ip": "*************",
        "severity_score": 8.5,
        "i18n_name": "SQL注入漏洞",
        "cve_id": "CVE-2023-1234",
        "final_score": 9.2,
        "risk_reason": "高危漏洞，存在SQL注入风险",
        "base_score": 8.0,
        "temporal_score": 8.2,
        "environmental_score": 9.0,
        "asset_importance_score": 8.5,
        "vuln_density_score": 7.8,
        "dangerous_vuln_score": 9.5,
        "cve_relevance_score": 8.8,
        "scan_time": "2024-01-15 10:30:00",
        "port": 80,
        "protocol": "tcp",
        "service": "http"
      }
      // ... 更多数据
    ]
  }
}
```

#### 响应字段说明（data.list中的对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| plugin_id | string | 插件ID |
| vuln_level | string | 漏洞等级（high/medium/low） |
| target_ip | string | 目标IP地址 |
| severity_score | float | 严重性评分 |
| i18n_name | string | 漏洞名称（国际化） |
| cve_id | string | CVE编号 |
| final_score | float | 最终风险评分 |
| risk_reason | string | 风险原因描述 |
| base_score | float | CVSS基础评分 |
| temporal_score | float | CVSS时间评分 |
| environmental_score | float | CVSS环境评分 |
| asset_importance_score | float | 资产重要性评分 |
| vuln_density_score | float | 漏洞密度评分 |
| dangerous_vuln_score | float | 危险漏洞评分 |
| cve_relevance_score | float | CVE相关性评分 |
| scan_time | string | 扫描时间 |
| port | integer | 端口号 |
| protocol | string | 协议类型 |
| service | string | 服务类型 |

### 2. 获取漏洞IP风险汇总结果

#### 接口信息

- **URL**: `/vulnerabilityScannings/vulnIpRiskSummaryResults`
- **方法**: GET
- **描述**: 获取按IP地址汇总的风险评估结果，包含每个IP的整体风险状况

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页数据量，范围1-1000 |

#### 请求示例

```bash
GET /vulnerabilityScannings/vulnIpRiskSummaryResults?page=1&size=20
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "page": 1,
    "page_size": 20,
    "total_page": 8,
    "total_count": 156,
    "list": [
      {
        "target_ip": "*************",
        "total_vulns": 15,
        "high_vulns": 3,
        "medium_vulns": 8,
        "low_vulns": 4,
        "avg_score": 7.8,
        "max_score": 9.5,
        "min_score": 3.2,
        "risk_level": "high",
        "asset_importance": "critical",
        "is_internal": true,
        "vuln_density": 0.75,
        "dangerous_vuln_count": 2,
        "cve_count": 12,
        "last_scan_time": "2024-01-15 10:30:00",
        "risk_summary": "该IP存在多个高危漏洞，建议立即处理"
      }
      // ... 更多数据
    ]
  }
}
```

#### 响应字段说明（data.list中的对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| target_ip | string | 目标IP地址 |
| total_vulns | integer | 漏洞总数 |
| high_vulns | integer | 高危漏洞数量 |
| medium_vulns | integer | 中危漏洞数量 |
| low_vulns | integer | 低危漏洞数量 |
| avg_score | float | 平均风险评分 |
| max_score | float | 最高风险评分 |
| min_score | float | 最低风险评分 |
| risk_level | string | 整体风险等级（high/medium/low） |
| asset_importance | string | 资产重要性（critical/high/medium/low） |
| is_internal | boolean | 是否为内网IP |
| vuln_density | float | 漏洞密度 |
| dangerous_vuln_count | integer | 危险漏洞数量 |
| cve_count | integer | CVE漏洞数量 |
| last_scan_time | string | 最后扫描时间 |
| risk_summary | string | 风险汇总描述 |

## 错误处理

### 错误响应格式

当接口调用失败时，返回以下格式的错误响应：

```json
{
  "code": -1,
  "msg": "错误描述信息",
  "data": null
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 系统错误或业务逻辑错误 |

### 常见错误情况

1. **参数错误**: 当page或size参数不合法时，系统会自动使用默认值
2. **数据获取失败**: 当无法从数据库获取数据时，返回相应错误信息
3. **系统异常**: 当系统内部发生异常时，返回通用错误信息

## 使用示例

### Python示例

```python
import requests

# 获取漏洞详细评分列表
response = requests.get(
    'http://your-domain/vulnerabilityScannings/vulnDetailedScoresList',
    params={'page': 1, 'size': 20}
)
data = response.json()
print(f"获取到 {data['data']['total_count']} 条漏洞记录")

# 获取IP风险汇总结果
response = requests.get(
    'http://your-domain/vulnerabilityScannings/vulnIpRiskSummaryResults',
    params={'page': 1, 'size': 50}
)
data = response.json()
print(f"获取到 {data['data']['total_count']} 个IP的风险汇总")
```

### JavaScript示例

```javascript
// 获取漏洞详细评分列表
fetch('/vulnerabilityScannings/vulnDetailedScoresList?page=1&size=10')
  .then(response => response.json())
  .then(data => {
    console.log('漏洞详细评分数据:', data.data.list);
  })
  .catch(error => {
    console.error('获取数据失败:', error);
  });

// 获取IP风险汇总结果
fetch('/vulnerabilityScannings/vulnIpRiskSummaryResults?page=1&size=10')
  .then(response => response.json())
  .then(data => {
    console.log('IP风险汇总数据:', data.data.list);
  })
  .catch(error => {
    console.error('获取数据失败:', error);
  });
```

## 性能说明

- 建议每页数据量不超过1000条，以保证响应性能
- 数据实时从数据库获取并计算，首次调用可能需要较长时间
- 建议在业务层面实现适当的缓存机制以提升性能

## 版本信息

- **当前版本**: v1.0
- **最后更新**: 2024-01-15
- **维护人员**: 后端开发团队

## 联系方式

如有问题或建议，请联系开发团队。