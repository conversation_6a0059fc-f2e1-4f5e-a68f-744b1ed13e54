import json
import pandas as pd
import os
import configparser
import pymysql
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

def get_security_logs_from_db():
    try:
        # 读取配置文件
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config.ini')
        cf = configparser.ConfigParser()
        cf.read(config_path, encoding='utf-8')
        # 数据库配置
        DB_CONFIG = {
            'host': cf.get("mysql", "host"),
            'port': int(cf.get("mysql", "port")),
            'user': cf.get("mysql", "user"),
            'password': cf.get("mysql", "password"),
            'database': cf.get("mysql", "database"),
            'charset': 'utf8mb4'
        }

        print(f"连接数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")

        # 建立数据库连接
        conn = pymysql.connect(**DB_CONFIG)

        try:
            # 查询log_row_tbl表数据
            sql = """
                select raw_string
                from log_row_tbl where data_source_type ='api'
            """

            # 使用connection对象的cursor执行查询
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                # 创建DataFrame
                df = pd.DataFrame(result, columns=columns)
            return df

        except Exception as e:
            print(f"数据库查询错误: {e}")
            return pd.DataFrame()
        finally:
            conn.close()

    except Exception as e:
        print(f"数据库查询失败: {e}")
        print("无法从数据库获取数据")
        return pd.DataFrame()


def parse_vuln_data(data):
    """
    读取多个 JSON 对象拼接在一起的文件
    """
    records = []

    # 如果传入的是DataFrame，则提取raw_string列的内容
    if isinstance(data, pd.DataFrame):
        if data.empty or 'raw_string' not in data.columns:
            print("警告: DataFrame为空或不包含raw_string列")
            return pd.DataFrame()
        # 获取DataFrame中的JSON字符串数据
        json_strings = data['raw_string'].tolist()
    else:
        # 如果传入的是字符串，则直接使用
        json_strings = [data] if isinstance(data, str) else []

    # 遍历所有JSON字符串进行解析
    for json_str in json_strings:
        if not isinstance(json_str, str):
            continue
            
        decoder = json.JSONDecoder()
        idx = 0
        while idx < len(json_str):
            # 跳过空白字符
            while idx < len(json_str) and json_str[idx].isspace():
                idx += 1
                
            # 如果已经到达字符串末尾，退出循环
            if idx >= len(json_str):
                break
                
            try:
                obj, end = decoder.raw_decode(json_str, idx)
                idx += end
                # 提取漏洞信息
                vuln_list = obj.get("data", {}).get("vulns", [])
                for vuln in vuln_list:
                    records.append({
                        'plugin_id': vuln.get('plugin_id'),
                        'vuln_level': vuln.get('vuln_level'),
                        'date_found': vuln.get('date_found'),
                        'date_recorded': vuln.get('date_recorded'),
                        'exp_desc': vuln.get('exp_desc'),
                        'target_ip': vuln.get('target'),
                        'severity_score': vuln.get('severity_points'),
                        'scan_method': vuln.get('scan_method'),
                        'vul_confirmed': vuln.get('vul_confirmed'),
                        'is_dangerous': vuln.get('is_dangerous'),
                        'i18n_name': vuln.get('i18n_name'),
                        'cve_id': vuln.get('cve_id'),
                        'threat_level': vuln.get('threat_level')
                    })
            except json.JSONDecodeError:
                # 打印错误信息以便调试
                print(f"JSON解析错误: 位置 {idx}")
                # 尝试跳过一些字符继续解析
                idx += 1

    return pd.DataFrame(records)

if __name__ == "__main__":
    db = get_security_logs_from_db();
    df = parse_vuln_data(db)
    # 输出前10条完整的数据
    print(f"📊 共解析 {len(df)} 条漏洞记录")
