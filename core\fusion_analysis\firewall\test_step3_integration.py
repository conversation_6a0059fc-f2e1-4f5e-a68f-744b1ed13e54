#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试step3和step2集成的脚本
验证step3_business_analysis.py能够正确调用step2_flow_direction.py的方法
"""

import pandas as pd
from step3_business_analysis_fw import main as get_business_analysis_data

def test_step3_integration():
    """
    测试step3和step2的集成
    """
    print("开始测试step3和step2的集成...")
    
    # 测试step3的数据获取
    print("\n1. 测试从step3获取数据...")
    df = get_business_analysis_data()
    
    if df is None or df.empty:
        print("❌ 未获取到数据")
        return False
    
    print(f"✅ 成功获取到 {len(df)} 条记录")
    print(f"✅ 数据列: {list(df.columns)}")
    
    # 检查必需的列是否存在
    required_columns = ['srcAddress', 'destAddress', 'time', 'id', 'flow_direction', 
                       'is_business_related', 'user_agent', 'http_host']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必需列: {missing_columns}")
        return False
    
    print("✅ 所有必需列都存在")
    
    # 检查业务相关性统计
    print("\n2. 检查业务相关性统计...")
    business_counts = df['is_business_related'].value_counts()
    print("业务相关性统计:")
    for status, count in business_counts.items():
        status_text = "业务相关" if status else "非业务相关"
        print(f"  {status_text}: {count}条记录")
    
    # 检查流向统计
    print("\n3. 检查流向统计...")
    flow_counts = df['flow_direction'].value_counts()
    print("流向统计:")
    for direction, count in flow_counts.items():
        print(f"  {direction}: {count}条记录")
    
    # 检查HTTP信息是否生成
    print("\n4. 检查HTTP信息生成...")
    non_empty_user_agent = df['user_agent'].notna().sum()
    non_empty_http_host = df['http_host'].notna().sum()
    print(f"✅ 生成了 {non_empty_user_agent} 条User-Agent记录")
    print(f"✅ 生成了 {non_empty_http_host} 条HTTP Host记录")
    
    # 保存测试结果
    output_file = "test_step3_integration_result.csv"
    df.to_csv(output_file, index=False)
    print(f"\n✅ 测试结果已保存至 {output_file}")
    
    print("\n🎉 step3集成测试成功完成！")
    return True

if __name__ == "__main__":
    success = test_step3_integration()
    if success:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 测试失败")