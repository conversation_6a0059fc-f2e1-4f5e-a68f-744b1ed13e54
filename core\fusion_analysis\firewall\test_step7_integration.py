#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试step7_behavior_analysis.py与step4、step5、step6的集成
"""

import pandas as pd
from step7_behavior_analysis_fw import main as get_behavior_analysis_data

def test_step7_integration():
    print("=== 测试step7_behavior_analysis.py集成 ===")
    
    # 调用step7的main函数
    result_df = get_behavior_analysis_data()
    
    if result_df is None:
        print("错误: step7返回None")
        return False
    
    if result_df.empty:
        print("错误: step7返回空DataFrame")
        return False
    
    print(f"step7处理了 {len(result_df)} 条记录")
    print(f"step7输出列: {list(result_df.columns)}")
    
    # 检查关键列是否存在
    required_columns = ['hour_slot', 'srcAddress', 'threat_score', 'false_positive_flag']
    missing_columns = [col for col in required_columns if col not in result_df.columns]
    
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        return False
    
    # 显示一些统计信息
    print("\n=== 行为分析统计 ===")
    
    # 威胁分数统计
    if 'threat_score' in result_df.columns:
        print(f"威胁分数范围: {result_df['threat_score'].min():.1f} - {result_df['threat_score'].max():.1f}")
        print(f"平均威胁分数: {result_df['threat_score'].mean():.1f}")
    
    # 误报标记统计
    if 'false_positive_flag' in result_df.columns:
        fp_counts = result_df['false_positive_flag'].value_counts()
        print(f"误报标记统计: {dict(fp_counts)}")
    
    # 已知误报统计
    if 'known_false_positive' in result_df.columns:
        known_fp_counts = result_df['known_false_positive'].value_counts()
        print(f"已知误报统计: {dict(known_fp_counts)}")
    
    # 流向统计
    if 'flow_directions' in result_df.columns:
        print("\n流向类型分布:")
        flow_types = result_df['flow_directions'].value_counts().head(5)
        for flow_type, count in flow_types.items():
            print(f"  {flow_type}: {count}")
    
    # 事件频率统计
    if 'event_frequency' in result_df.columns:
        print(f"\n事件频率范围: {result_df['event_frequency'].min()} - {result_df['event_frequency'].max()}")
        print(f"平均事件频率: {result_df['event_frequency'].mean():.1f}")
    
    # 保存测试结果
    test_output_file = "test_step7_integration_result.csv"
    result_df.to_csv(test_output_file, index=False, encoding="utf-8-sig")
    print(f"\n测试结果已保存至: {test_output_file}")
    
    print("\n=== step7集成测试通过 ===")
    return True

if __name__ == "__main__":
    success = test_step7_integration()
    if success:
        print("\n✅ 所有测试通过！step7现在正确地从step4、step5、step6接收数据")
    else:
        print("\n❌ 测试失败！")