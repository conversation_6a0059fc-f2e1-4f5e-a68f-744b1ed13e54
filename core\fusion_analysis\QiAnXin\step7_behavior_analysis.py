import pandas as pd
import numpy as np
import os

# 导入前面步骤的模块
from step6_false_positive import get_false_positive_data, process_false_positives

TIME_COL_CANDS = ["hour_slot", "time", "timestamp", "event_time", "log_time"]

def _read_csv_try_parse_dates(path: str, encoding="utf-8-sig") -> pd.DataFrame:
    # 优先解析常见时间列
    try:
        return pd.read_csv(path, encoding=encoding, parse_dates=[c for c in TIME_COL_CANDS if c])
    except Exception:
        # 回退：不解析，防止读取失败
        return pd.read_csv(path, encoding=encoding)

def _coerce_bool(s):
    # 将 1/0/True/False/是/否/true/false 等转换为布尔
    if s.dtype == bool:
        return s
    return s.astype(str).str.strip().str.lower().map(
        {"1": True, "true": True, "yes": True, "是": True, "y": True,
         "0": False, "false": False, "no": False, "否": False, "n": False}
    ).fillna(False)

def _ensure_datetime_col(df: pd.DataFrame, col: str) -> pd.DataFrame:
    if col in df.columns:
        df[col] = pd.to_datetime(df[col], errors="coerce")
    return df

def analyze_behavior(df: pd.DataFrame) -> pd.DataFrame:
    """
    小时 × 源IP 聚合（行为画像）
    """
    print("正在进行行为分析...")

    # 统一时间列类型
    df = _ensure_datetime_col(df, "time")
    df = _ensure_datetime_col(df, "hour_slot")

    # 关键布尔列标准化
    if "is_business_related" in df.columns:
        df["is_business_related"] = _coerce_bool(df["is_business_related"])
    if "is_work_time" in df.columns:
        df["is_work_time"] = _coerce_bool(df["is_work_time"])

    # 分组聚合
    grouped = df.groupby(["hour_slot", "srcAddress"])

    result_df = grouped.agg(
        flow_directions=("flow_direction", lambda x: sorted(list(set(x)))),
        internal_to_internal_count=("flow_direction", lambda x: (x == "内对内").sum()),
        internal_to_external_count=("flow_direction", lambda x: (x == "内到外").sum()),
        external_to_internal_count=("flow_direction", lambda x: (x == "外到内").sum()),
        external_to_external_count=("flow_direction", lambda x: (x == "外对外").sum()),
        business_related_count=("is_business_related", lambda x: x.sum()),
        work_time_count=("is_work_time", lambda x: x.sum()),
        total_events=("id", "count"),
    ).reset_index()

    # 基础指标
    result_df["business_related_ratio"] = (
        result_df["business_related_count"] / result_df["total_events"]
    )
    result_df["work_time_ratio"] = (
        result_df["work_time_count"] / result_df["total_events"]
    )
    result_df["event_frequency"] = result_df["total_events"]

    # —— 持续性与活跃度指标 ——
    presence_df = df[["srcAddress", "hour_slot"]].drop_duplicates()
    presence_df = _ensure_datetime_col(presence_df, "hour_slot")
    presence_df = presence_df.sort_values(["srcAddress", "hour_slot"])

    # 下一次出现的时间差（小时）
    presence_df["next_hour_slot"] = presence_df.groupby("srcAddress")["hour_slot"].shift(-1)
    presence_df["hours_to_next"] = (
        (presence_df["next_hour_slot"] - presence_df["hour_slot"]).dt.total_seconds() / 3600
    )

    # 是否24小时内有下一次出现
    presence_df["has_next_24h"] = presence_df["hours_to_next"] <= 24

    # 连续小时游程标识
    presence_df["diff_hours"] = presence_df["hours_to_next"]
    # 第一行 diff_hours 为 NaN，应当视为断点
    is_break = presence_df["diff_hours"].ne(1) | presence_df["diff_hours"].isna()
    # 每遇到断点就开始新的 run（按同一 srcAddress 自然有分组排序）
    presence_df["run_id"] = is_break.cumsum()

    # 计算每段游程长度
    run_lengths = presence_df.groupby(["srcAddress", "run_id"]).size().reset_index(name="run_len")
    presence_df = pd.merge(presence_df, run_lengths, on=["srcAddress", "run_id"], how="left")

    # 当前/最大游程长度
    current_run = presence_df.groupby(["srcAddress", "hour_slot"])["run_len"].first().reset_index()
    max_run = presence_df.groupby("srcAddress")["run_len"].max().reset_index(name="max_run_hours")

    # 活跃度滚动窗口（以时间为索引）
    pp = presence_df.drop_duplicates(["srcAddress", "hour_slot"]).copy()
    pp["active"] = 1
    # 透视后 index=hour_slot（必须是 datetime），列=srcAddress，值=active
    presence_pivot = pp.pivot(index="hour_slot", columns="srcAddress", values="active").fillna(0)
    presence_pivot.index = pd.to_datetime(presence_pivot.index, errors="coerce")

    # 24h/72h 内最大活跃小时数（每列是某 IP 的时间序列）
    active_hours_24h = presence_pivot.rolling("24h").sum().max()
    active_hours_72h = presence_pivot.rolling("72h").sum().max()

    active_hours_24h_df = pd.DataFrame(
        {"srcAddress": active_hours_24h.index, "active_hours_24h": active_hours_24h.values}
    )
    active_hours_72h_df = pd.DataFrame(
        {"srcAddress": active_hours_72h.index, "active_hours_72h": active_hours_72h.values}
    )

    # —— 合并持续性指标到结果（确保 hour_slot 类型一致） ——
    result_df["hour_slot"] = pd.to_datetime(result_df["hour_slot"], errors="coerce")
    current_run["hour_slot"] = pd.to_datetime(current_run["hour_slot"], errors="coerce")

    # 如果有把 hour_slot 当索引的情况，恢复成列
    if current_run.index.name == "hour_slot":
        current_run = current_run.reset_index()

    result_df = pd.merge(
        result_df,
        current_run.rename(columns={"run_len": "current_run_hours"}),
        on=["srcAddress", "hour_slot"],
        how="left",
    )
    result_df = pd.merge(result_df, max_run, on="srcAddress", how="left")
    result_df = pd.merge(result_df, active_hours_24h_df, on="srcAddress", how="left")
    result_df = pd.merge(result_df, active_hours_72h_df, on="srcAddress", how="left")

    # 后续出现标记（has_next_24h）
    has_next = presence_df.groupby(["srcAddress", "hour_slot"])["has_next_24h"].first().reset_index()
    has_next["hour_slot"] = pd.to_datetime(has_next["hour_slot"], errors="coerce")
    result_df = pd.merge(result_df, has_next, on=["srcAddress", "hour_slot"], how="left")

    # 缺失填充
    result_df["current_run_hours"] = result_df["current_run_hours"].fillna(1)
    result_df["max_run_hours"] = result_df["max_run_hours"].fillna(1)
    result_df["active_hours_24h"] = result_df["active_hours_24h"].fillna(1)
    result_df["active_hours_72h"] = result_df["active_hours_72h"].fillna(1)
    result_df["has_next_24h"] = result_df["has_next_24h"].fillna(False)

    # 成簇强度：该小时内 event_frequency >= 5 的 IP 数
    high_activity_ips = result_df[result_df["event_frequency"] >= 5].copy()
    hour_peer_counts = high_activity_ips.groupby("hour_slot").size().reset_index(name="hour_peer_ip_count")
    result_df = pd.merge(result_df, hour_peer_counts, on="hour_slot", how="left")
    result_df["hour_peer_ip_count"] = result_df["hour_peer_ip_count"].fillna(0)

    # 混合模式计数
    result_df["mixed_flow_modes"] = result_df["flow_directions"].apply(len)

    return result_df

def get_behavior_analysis_data():
    """
    获取行为分析所需的数据，替代读取CSV文件的方式
    返回: (df, risk_df, fp_df, false_positive_ips)
    """
    print("正在获取行为分析数据...")
    
    # 调用step6的main函数获取误报分析结果
    from step6_false_positive import main as step6_main
    fp_result = step6_main()
    
    if fp_result is None:
        raise Exception("无法获取误报分析数据")
    
    # step6_main返回(fp_df, known_false_positive_ips)
    fp_df, false_positive_ips = fp_result
    
    # 获取原始工作时间数据和威胁评分数据
    work_time_df, threat_score_df = get_false_positive_data()
    
    # 使用工作时间数据作为基础数据
    df = work_time_df.copy()
    
    # 使用威胁评分数据
    risk_df = threat_score_df[['hour_slot', 'srcAddress', 'threat_score']].copy()
    
    # 确保误报数据包含必要的列
    if 'threat_score' not in fp_df.columns:
        fp_df = pd.merge(fp_df, risk_df, on=['hour_slot', 'srcAddress'], how='left')
    
    print(f"获取到 {len(df)} 条记录用于行为分析")
    print(f"威胁评分数据: {len(risk_df)} 条")
    print(f"误报数据: {len(fp_df)} 条")
    print(f"已知误报IP: {len(false_positive_ips)} 个")
    
    return df, risk_df, fp_df, false_positive_ips

def merge_external_info(result_df, risk_df, fp_df, false_positive_ips):
    """
    合并外部信息：威胁分、系统误报、已知误报
    """
    print("正在合并外部信息...")

    # 统一时间列
    for d in (risk_df, fp_df, result_df):
        d["hour_slot"] = pd.to_datetime(d["hour_slot"], errors="coerce")

    # 合并威胁分
    if "threat_score" in risk_df.columns:
        result_df = pd.merge(
            result_df,
            risk_df[["hour_slot", "srcAddress", "threat_score"]],
            on=["hour_slot", "srcAddress"],
            how="left",
        )
        result_df["threat_score"] = pd.to_numeric(result_df["threat_score"], errors="coerce").fillna(0)
    else:
        result_df["threat_score"] = 0

    # 合并系统误报
    if "false_positive_flag" in fp_df.columns:
        result_df = pd.merge(
            result_df,
            fp_df[["hour_slot", "srcAddress", "false_positive_flag"]],
            on=["hour_slot", "srcAddress"],
            how="left",
        )
        result_df["false_positive_flag"] = _coerce_bool(result_df["false_positive_flag"])
    else:
        result_df["false_positive_flag"] = False

    # 标记已知误报
    result_df["known_false_positive"] = result_df["srcAddress"].isin(false_positive_ips)

    return result_df

def main():
    """
    主函数：执行行为分析
    """
    try:
        df, risk_df, fp_df, false_positive_ips = get_behavior_analysis_data()
        
        # 校验输入表必需列
        required_columns = ["hour_slot", "srcAddress", "flow_direction", "is_business_related", "is_work_time", "id"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: 输入数据缺少必需列: {missing_columns}")
            print(f"当前列: {list(df.columns)}")
            return None
        
        # 行为分析
        result_df = analyze_behavior(df)
        
        # 合并外部信息
        result_df = merge_external_info(result_df, risk_df, fp_df, false_positive_ips)
        
        # print(f"\n行为分析完成，共处理 {len(result_df)} 条记录")
        #
        # # 统计分析结果
        # print("\n=== 行为分析统计 ===")
        # print(f"总记录数: {len(result_df)}")
        # print(f"唯一IP数: {result_df['srcAddress'].nunique()}")
        # print(f"时间跨度: {result_df['hour_slot'].min()} 到 {result_df['hour_slot'].max()}")
        #
        # # 流向统计
        # flow_stats = {}
        # for col in ['internal_to_internal_count', 'internal_to_external_count',
        #            'external_to_internal_count', 'external_to_external_count']:
        #     if col in result_df.columns:
        #         flow_stats[col] = result_df[col].sum()
        # print(f"流向统计: {flow_stats}")
        #
        # # 威胁分统计
        # if 'threat_score' in result_df.columns:
        #     threat_stats = {
        #         '平均威胁分': result_df['threat_score'].mean(),
        #         '最高威胁分': result_df['threat_score'].max(),
        #         '最低威胁分': result_df['threat_score'].min()
        #     }
        #     print(f"威胁分统计: {threat_stats}")
        #
        # # 误报统计
        # if 'false_positive_flag' in result_df.columns:
        #     fp_count = result_df['false_positive_flag'].sum()
        #     print(f"系统误报数: {fp_count}")
        #
        # if 'known_false_positive' in result_df.columns:
        #     known_fp_count = result_df['known_false_positive'].sum()
        #     print(f"已知误报数: {known_fp_count}")
        #  完整输出这两条数据
        # print(result_df.iloc[-1])
        return result_df
        
    except Exception as e:
        print(f"行为分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
