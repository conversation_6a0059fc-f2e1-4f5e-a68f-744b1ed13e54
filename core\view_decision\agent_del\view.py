from . import *
from flask import request, jsonify
import requests
import json

# 目标服务的基础URL - 参考test_client.py中的配置
TARGET_BASE_URL = "http://127.0.0.1:28123"
TARGET_CONVERSATIONS_URL = f"{TARGET_BASE_URL}/conversations"

@r.route("/del", methods=['DELETE'])
def agent_del():
    """
    删除指定对话接口
    转发请求到本机的其他服务 (http://127.0.0.1:28123/conversations/{conversation_id})
    """
    try:
        # 获取请求数据
        data = request.get_json() or {}
        conversation_id = data.get('conversation_id')
        
        if not conversation_id:
            return jsonify({
                "status": "error",
                "code": 400,
                "message": "缺少必要参数: conversation_id",
                "result": None
            }), 400
        
        # 构建目标URL
        target_url = f"{TARGET_CONVERSATIONS_URL}/{conversation_id}"
        
        # 转发请求到目标服务
        response = requests.delete(target_url, timeout=30)
        
        # 返回目标服务的响应
        if response.status_code == 200:
            result = response.json()
            return jsonify({
                "status": "success",
                "code": 200,
                "message": "删除对话成功",
                "result": result
            }), 200
        else:
            return jsonify({
                "status": "error",
                "code": response.status_code,
                "message": f"目标服务返回错误: {response.text}",
                "result": None
            }), response.status_code
            
    except requests.exceptions.RequestException as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"连接目标服务失败: {str(e)}",
            "result": None
        }), 500
    except Exception as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500

@r.route("/del/<conversation_id>", methods=['DELETE'])
def agent_del_by_path(conversation_id):
    """
    删除指定对话接口 (通过路径参数)
    转发请求到本机的其他服务 (http://127.0.0.1:28123/conversations/{conversation_id})
    """
    try:
        # 构建目标URL
        target_url = f"{TARGET_CONVERSATIONS_URL}/{conversation_id}"
        
        # 转发请求到目标服务
        response = requests.delete(target_url, timeout=30)
        
        # 返回目标服务的响应
        if response.status_code == 200:
            result = response.json()
            return jsonify({
                "status": "success",
                "code": 200,
                "message": "删除对话成功",
                "result": result
            }), 200
        else:
            return jsonify({
                "status": "error",
                "code": response.status_code,
                "message": f"目标服务返回错误: {response.text}",
                "result": None
            }), response.status_code
            
    except requests.exceptions.RequestException as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"连接目标服务失败: {str(e)}",
            "result": None
        }), 500
    except Exception as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500
