#!/usr/bin/env python
# encoding:utf-8
"""
测试step6_false_positive.py与step4_work_time.py和step5_ip_threat_score.py的集成
"""

import pandas as pd
from step6_false_positive_fw import main as get_false_positive_data

def test_step6_integration():
    """
    测试step6能否正确调用step4和step5的方法并处理数据
    """
    print("开始测试step6_false_positive.py集成...")
    
    try:
        # 调用step6的main函数
        result = get_false_positive_data()
        
        if result is not None and len(result) == 2:
            fp_df, known_false_positive_ips = result
            
            if fp_df is not None and not fp_df.empty:
                print(f"✓ step6成功处理了 {len(fp_df)} 条记录")
                
                # 检查是否包含误报标记列
                if 'false_positive_flag' in fp_df.columns:
                    fp_stats = fp_df['false_positive_flag'].value_counts()
                    print(f"✓ 误报统计: {fp_stats.to_dict()}")
                    
                    # 计算误报率
                    false_positive_rate = fp_stats.get(True, 0) / len(fp_df) * 100 if len(fp_df) > 0 else 0
                    print(f"✓ 误报率: {false_positive_rate:.2f}%")
                else:
                    print("✗ 缺少误报标记列")
                    return False
                
                # 检查是否包含威胁分数列
                if 'threat_score' in fp_df.columns:
                    threat_score_stats = fp_df['threat_score'].describe()
                    print(f"✓ 威胁分数统计: 最小值={threat_score_stats['min']:.1f}, 最大值={threat_score_stats['max']:.1f}, 平均值={threat_score_stats['mean']:.1f}")
                else:
                    print("✗ 缺少威胁分数列")
                    return False
                
                # 检查必需的基础列
                required_columns = ['hour_slot', 'srcAddress']
                missing_columns = [col for col in required_columns if col not in fp_df.columns]
                if missing_columns:
                    print(f"✗ 缺少必需列: {missing_columns}")
                    return False
                else:
                    print(f"✓ 包含所有必需列: {required_columns}")
                
                # 检查已知误报IP列表
                if known_false_positive_ips is not None:
                    print(f"✓ 已知误报IP数量: {len(known_false_positive_ips)}")
                    if len(known_false_positive_ips) > 0:
                        print(f"✓ 已知误报IP示例: {list(known_false_positive_ips)[:3]}")
                else:
                    print("✗ 未获取到已知误报IP列表")
                    return False
                
                # 保存测试结果
                fp_df.to_csv('test_step6_integration_result.csv', index=False)
                print("✓ 测试结果已保存至 test_step6_integration_result.csv")
                
                return True
            else:
                print("✗ step6未获取到误报分析数据")
                return False
        else:
            print("✗ step6返回结果格式不正确")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_step6_integration()
    if success:
        print("\n🎉 step6集成测试通过！")
    else:
        print("\n❌ step6集成测试失败！")