-- ****************************************************
-- 🔐 安全数据库结构同步脚本（自动生成）
-- 功能: 创建表（若不存在），添加字段（忽略错误），修改字段定义，添加索引
-- 注意: 可重复执行，不会报错
-- ****************************************************

-- 先创建所有表（如果不存在）

CREATE TABLE IF NOT EXISTS `apilog_source_tbl` (
  `id` bigint NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source_id` bigint NOT NULL,
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `params` varchar(510) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `dataset_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `tags` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `num_samples` int DEFAULT NULL,
  `format` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_schema` text COLLATE utf8mb4_general_ci,
  `storage_path` text COLLATE utf8mb4_general_ci NOT NULL,
  `download_url` text COLLATE utf8mb4_general_ci,
  `owner` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `dataset_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int DEFAULT NULL,
  `log_type` enum('create','update','delete','download') COLLATE utf8mb4_general_ci NOT NULL,
  `message` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=769 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `dataset_uploads` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int DEFAULT NULL,
  `uploader` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `upload_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `file_path` text COLLATE utf8mb4_general_ci NOT NULL,
  `file_size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('pending','success','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `decision_info` (
  `event_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `event_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `maintenance_method` text COLLATE utf8mb4_general_ci,
  `check_item` text COLLATE utf8mb4_general_ci,
  `device_software_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vendor_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `harm_name` text COLLATE utf8mb4_general_ci,
  `description` text COLLATE utf8mb4_general_ci,
  `prevention_measures` text COLLATE utf8mb4_general_ci,
  `attack_cause` text COLLATE utf8mb4_general_ci,
  `defective_device_software` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `configuration_solution` text COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`event_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `file_source_tbl` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `type` varchar(255) NOT NULL COMMENT '数据源类型',
  `name` varchar(255) NOT NULL COMMENT '数据源名称',
  `headers` text COMMENT 'CSV文件的表头',
  `host_index` int DEFAULT NULL COMMENT 'host索引',
  `time_index` int DEFAULT NULL COMMENT '时间索引',
  `level_index` int DEFAULT NULL COMMENT '日志级别索引',
  `type_index` int DEFAULT NULL COMMENT '类型索引',
  `source_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_source_id` (`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件源表';

CREATE TABLE IF NOT EXISTS `hardware_resources` (
  `id` int NOT NULL COMMENT '自增主键',
  `resource_category` varchar(255) NOT NULL COMMENT '资源分类',
  `resource_type` varchar(255) NOT NULL COMMENT '资源类型',
  `standard_full_name` varchar(255) NOT NULL COMMENT '标准全称',
  `manufacturer` varchar(255) NOT NULL COMMENT '制造商',
  `brand` varchar(255) NOT NULL COMMENT '品牌',
  `model` varchar(255) NOT NULL COMMENT '型号',
  `factory_serial_number` varchar(255) NOT NULL COMMENT '出厂序列号',
  `device_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备状态',
  `is_synced_to_erp` enum('是','否') NOT NULL COMMENT '是否同步给ERP',
  `operation_unit` varchar(255) NOT NULL COMMENT '运维单位',
  `purchase_date` date NOT NULL COMMENT '采购日期',
  `affiliated_network` varchar(20) NOT NULL COMMENT '所属网络',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物理机信息表';

CREATE TABLE IF NOT EXISTS `log_row_tbl` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'host',
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志级别',
  `data_source_id` bigint DEFAULT NULL COMMENT '数据源ID',
  `data_source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源类型',
  `source_id` bigint DEFAULT NULL COMMENT '源ID',
  `row_number` int DEFAULT NULL COMMENT '行号',
  `raw_string` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '原始字符串',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1958733866759700482 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='日志行表';

CREATE TABLE IF NOT EXISTS `source_tbl` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `service` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `syslog_source_tbl` (
  `id` bigint NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `source_id` bigint NOT NULL,
  `host` varchar(255) NOT NULL,
  `port` int DEFAULT NULL,
  `started` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_apikeys` (
  `user_id` int NOT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`api_key`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `user_bug` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `bug_description` text COLLATE utf8mb4_general_ci NOT NULL,
  `bug_image` longblob,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `user_chat` (
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` int NOT NULL,
  `histext` longtext COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `user_history` (
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `hismain` text COLLATE utf8mb4_general_ci,
  `histext` longtext COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_alert` (
  `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `source_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `destination_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `source_port` int DEFAULT NULL,
  `destination_port` int DEFAULT NULL,
  `protocol` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `attack_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `severity` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `signature` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `detection_system` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `correlation_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `detail` text COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息',
  `ip_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息',
  PRIMARY KEY (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_alert_analysis` (
  `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`,`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_alert_copy1` (
  `alert_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `source_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `destination_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `source_port` int DEFAULT NULL,
  `destination_port` int DEFAULT NULL,
  `protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `attack_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `detection_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `correlation_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息',
  `ip_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息',
  PRIMARY KEY (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_analysis` (
  `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `attack_summary` text COLLATE utf8mb4_general_ci,
  `affected_systems` text COLLATE utf8mb4_general_ci,
  `potential_risk` text COLLATE utf8mb4_general_ci,
  `recommended_actions` text COLLATE utf8mb4_general_ci,
  `notes` text COLLATE utf8mb4_general_ci,
  `status` int DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_audit` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `workflow_uuid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `only_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `user_id` int NOT NULL DEFAULT '0',
  `audit_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `start_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_login_history` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL DEFAULT '0',
  `login_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `only_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `app_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `app_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` mediumtext COLLATE utf8mb4_general_ci NOT NULL,
  `status` int NOT NULL DEFAULT '0',
  `html` mediumtext COLLATE utf8mb4_general_ci,
  `args` mediumtext COLLATE utf8mb4_general_ci,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=496 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_nav` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `icon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `is_menu` int NOT NULL DEFAULT '0',
  `up` int NOT NULL DEFAULT '0',
  `order` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_notification` (
  `notification_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `notification_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('read','unread') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unread',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `result_data` json DEFAULT NULL,
  `execution_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策：0-未决策，1-已决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理：0-未处理，1-已处理',
  `is_dispose` tinyint(1) DEFAULT '0' COMMENT '是否已处置：0-未处置，1-已处置',
  `dispose_msg` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`notification_id`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_source` (`source`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_is_decided` (`is_decided`),
  KEY `idx_is_processed` (`is_processed`),
  KEY `idx_is_dispose` (`is_dispose`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_operation_log` (
  `log_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志ID',
  `operator` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `operation_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型',
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operation_details` text COLLATE utf8mb4_general_ci COMMENT '操作详情',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `idx_rect_id` (`rect_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_rectification` (
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `message_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息ID',
  `serial_number` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号',
  `source_unit` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源单位（如市公司）',
  `source_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源编号或来源说明',
  `issue_date` date DEFAULT NULL COMMENT '下发时间',
  `rect_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单类型（二级单位整改单/内部整改单/自建系统整改单）',
  `description` text COLLATE utf8mb4_general_ci COMMENT '隐患描述',
  `vuln_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患名称',
  `vuln_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患类型',
  `vuln_level` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患级别（低/中/高危/严重）',
  `vuln_count` int DEFAULT NULL COMMENT '隐患个数',
  `ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患IP',
  `responsible_dept` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任单位/部门',
  `is_responded` tinyint(1) DEFAULT '0' COMMENT '是否反馈',
  `response_date` date DEFAULT NULL COMMENT '反馈时间',
  `is_fixed` tinyint(1) DEFAULT '0' COMMENT '是否整改',
  `fix_details` text COLLATE utf8mb4_general_ci COMMENT '整改详情',
  `retest_status` tinyint(1) DEFAULT '0' COMMENT '复测状态',
  `note` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `rect_deadline` date DEFAULT NULL,
  `remind_time` date DEFAULT NULL COMMENT '提醒时间',
  `last_rescan_time` date DEFAULT NULL COMMENT '上次复测时间',
  `is_excel_imported` tinyint(1) DEFAULT '0' COMMENT '是否通过Excel导入',
  `excel_filename` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入的Excel文件名',
  `excel_row_index` int DEFAULT NULL COMMENT '导入时的原始Excel行号',
  `import_batch_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入批次ID',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `inspection_content` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察内容',
  `inspection_overview` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察实施概况',
  `system_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统名称',
  `existing_hazard` text COLLATE utf8mb4_general_ci COMMENT '存在隐患',
  `rectification_suggestion` text COLLATE utf8mb4_general_ci COMMENT '整改建议',
  `rectification_time_requirement` text COLLATE utf8mb4_general_ci COMMENT '整改时间要求',
  `inspection_executor` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察执行负责人',
  `rescan_situation` text COLLATE utf8mb4_general_ci COMMENT '复查情况',
  `inspection_rescan_person` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察复查人员',
  PRIMARY KEY (`rect_id`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_report` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `report_no` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `workflow_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_rescan_record` (
  `rescan_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '复测记录ID',
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联整改单ID',
  `rescan_time` datetime DEFAULT NULL COMMENT '复测时间',
  `result` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '复测结果，如“通过/未通过/需复检”',
  `rescan_note` text COLLATE utf8mb4_general_ci COMMENT '复测备注',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`rescan_id`),
  KEY `idx_rect_id` (`rect_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_role` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_role_nav` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL DEFAULT '0',
  `nav_id` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_setting` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_key` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_timer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `timer_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `interval_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `time` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `start_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `end_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `jitter` int NOT NULL DEFAULT '0',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`timer_uuid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL DEFAULT '1',
  `name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_user_role` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL DEFAULT '0',
  `role_id` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `passwd` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `nick_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `avatar` text COLLATE utf8mb4_general_ci,
  `email` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `token` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_account` (`account`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_variablen` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type_id` int NOT NULL DEFAULT '0',
  `key` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index.key` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `w5_workflow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `user_id` int NOT NULL DEFAULT '0',
  `type_id` int NOT NULL DEFAULT '0',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `start_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `end_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `input_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `webhook_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `timer_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `for_list` mediumtext COLLATE utf8mb4_general_ci,
  `if_list` mediumtext COLLATE utf8mb4_general_ci,
  `audit_list` mediumtext COLLATE utf8mb4_general_ci,
  `flow_json` mediumtext COLLATE utf8mb4_general_ci,
  `flow_data` mediumtext COLLATE utf8mb4_general_ci,
  `controller_data` mediumtext COLLATE utf8mb4_general_ci,
  `local_var_data` mediumtext COLLATE utf8mb4_general_ci,
  `grid_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_marker` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_color` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_connector` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_router` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `thumbnail` longtext COLLATE utf8mb4_general_ci,
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ****************************************************
-- 第一步: 添加字段（如果不存在，忽略错误）
-- ****************************************************

-- 为表 `apilog_source_tbl` 添加字段（如果不存在）
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `id` bigint NOT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `source_id` bigint NOT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `apilog_source_tbl` ADD COLUMN `params` varchar(510) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;

-- 为表 `dataset_info` 添加字段（如果不存在）
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `id` int NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `description` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `tags` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `num_samples` int DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `format` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `data_schema` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `storage_path` text COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `download_url` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `owner` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active' */;
/*!999999 ALTER TABLE `dataset_info` ADD COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP */;

-- 为表 `dataset_logs` 添加字段（如果不存在）
/*!999999 ALTER TABLE `dataset_logs` ADD COLUMN `id` int NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `dataset_logs` ADD COLUMN `dataset_id` int DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_logs` ADD COLUMN `log_type` enum('create','update','delete','download') COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `dataset_logs` ADD COLUMN `message` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `dataset_logs` ADD COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP */;

-- 为表 `dataset_uploads` 添加字段（如果不存在）
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `id` int NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `dataset_id` int DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `uploader` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `upload_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `file_path` text COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `file_size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `dataset_uploads` ADD COLUMN `status` enum('pending','success','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending' */;

-- 为表 `decision_info` 添加字段（如果不存在）
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `event_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `event_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `maintenance_method` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `check_item` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `device_software_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `vendor_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `harm_name` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `description` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `prevention_measures` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `attack_cause` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `defective_device_software` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `decision_info` ADD COLUMN `configuration_solution` text COLLATE utf8mb4_general_ci */;

-- 为表 `file_source_tbl` 添加字段（如果不存在）
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `id` bigint NOT NULL COMMENT '主键ID' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `type` varchar(255) NOT NULL COMMENT '数据源类型' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `name` varchar(255) NOT NULL COMMENT '数据源名称' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `headers` text COMMENT 'CSV文件的表头' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `host_index` int DEFAULT NULL COMMENT 'host索引' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `time_index` int DEFAULT NULL COMMENT '时间索引' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `level_index` int DEFAULT NULL COMMENT '日志级别索引' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `type_index` int DEFAULT NULL COMMENT '类型索引' */;
/*!999999 ALTER TABLE `file_source_tbl` ADD COLUMN `source_id` bigint DEFAULT NULL */;

-- 为表 `hardware_resources` 添加字段（如果不存在）
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `id` int NOT NULL COMMENT '自增主键' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `resource_category` varchar(255) NOT NULL COMMENT '资源分类' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `resource_type` varchar(255) NOT NULL COMMENT '资源类型' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `standard_full_name` varchar(255) NOT NULL COMMENT '标准全称' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `manufacturer` varchar(255) NOT NULL COMMENT '制造商' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `brand` varchar(255) NOT NULL COMMENT '品牌' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `model` varchar(255) NOT NULL COMMENT '型号' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `factory_serial_number` varchar(255) NOT NULL COMMENT '出厂序列号' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `device_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备状态' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `is_synced_to_erp` enum('是','否') NOT NULL COMMENT '是否同步给ERP' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `operation_unit` varchar(255) NOT NULL COMMENT '运维单位' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `purchase_date` date NOT NULL COMMENT '采购日期' */;
/*!999999 ALTER TABLE `hardware_resources` ADD COLUMN `affiliated_network` varchar(20) NOT NULL COMMENT '所属网络' */;

-- 为表 `log_row_tbl` 添加字段（如果不存在）
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'host' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志级别' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `data_source_id` bigint DEFAULT NULL COMMENT '数据源ID' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `data_source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源类型' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `source_id` bigint DEFAULT NULL COMMENT '源ID' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `row_number` int DEFAULT NULL COMMENT '行号' */;
/*!999999 ALTER TABLE `log_row_tbl` ADD COLUMN `raw_string` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '原始字符串' */;

-- 为表 `source_tbl` 添加字段（如果不存在）
/*!999999 ALTER TABLE `source_tbl` ADD COLUMN `id` bigint NOT NULL COMMENT '主键ID' */;
/*!999999 ALTER TABLE `source_tbl` ADD COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称' */;
/*!999999 ALTER TABLE `source_tbl` ADD COLUMN `service` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务名称' */;

-- 为表 `syslog_source_tbl` 添加字段（如果不存在）
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `id` bigint NOT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `type` varchar(255) DEFAULT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `name` varchar(255) NOT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `source_id` bigint NOT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `host` varchar(255) NOT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `port` int DEFAULT NULL */;
/*!999999 ALTER TABLE `syslog_source_tbl` ADD COLUMN `started` tinyint(1) NOT NULL DEFAULT '0' */;

-- 为表 `user_apikeys` 添加字段（如果不存在）
/*!999999 ALTER TABLE `user_apikeys` ADD COLUMN `user_id` int NOT NULL */;
/*!999999 ALTER TABLE `user_apikeys` ADD COLUMN `api_key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `user_apikeys` ADD COLUMN `session_id` int NOT NULL */;

-- 为表 `user_bug` 添加字段（如果不存在）
/*!999999 ALTER TABLE `user_bug` ADD COLUMN `user_id` int NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `user_bug` ADD COLUMN `bug_description` text COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `user_bug` ADD COLUMN `bug_image` longblob */;

-- 为表 `user_chat` 添加字段（如果不存在）
/*!999999 ALTER TABLE `user_chat` ADD COLUMN `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `user_chat` ADD COLUMN `session_id` int NOT NULL */;
/*!999999 ALTER TABLE `user_chat` ADD COLUMN `histext` longtext COLLATE utf8mb4_general_ci */;

-- 为表 `user_history` 添加字段（如果不存在）
/*!999999 ALTER TABLE `user_history` ADD COLUMN `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `user_history` ADD COLUMN `session_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `user_history` ADD COLUMN `hismain` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `user_history` ADD COLUMN `histext` longtext COLLATE utf8mb4_general_ci */;

-- 为表 `w5_alert` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `timestamp` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `source_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `destination_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `source_port` int DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `destination_port` int DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `protocol` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `attack_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `severity` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `signature` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `detection_system` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `correlation_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `status` int DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策' */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理' */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `detail` text COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息' */;
/*!999999 ALTER TABLE `w5_alert` ADD COLUMN `ip_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息' */;

-- 为表 `w5_alert_analysis` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_alert_analysis` ADD COLUMN `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_alert_analysis` ADD COLUMN `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_alert_analysis` ADD COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_alert_analysis` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_alert_copy1` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `alert_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `timestamp` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `source_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `destination_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `source_port` int DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `destination_port` int DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `attack_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `detection_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `correlation_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `status` int DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策' */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理' */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息' */;
/*!999999 ALTER TABLE `w5_alert_copy1` ADD COLUMN `ip_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息' */;

-- 为表 `w5_analysis` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `timestamp` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `attack_summary` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `affected_systems` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `potential_risk` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `recommended_actions` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `notes` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `status` int DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_analysis` ADD COLUMN `update_time` datetime DEFAULT NULL */;

-- 为表 `w5_audit` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `workflow_uuid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `only_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `user_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `audit_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `start_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_audit` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_login_history` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_login_history` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_login_history` ADD COLUMN `user_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_login_history` ADD COLUMN `login_time` datetime NOT NULL */;

-- 为表 `w5_logs` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `only_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `app_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `app_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `result` mediumtext COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `html` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `args` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_logs` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_nav` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `id` int NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `icon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `is_menu` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `up` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `order` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_nav` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_notification` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `notification_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `notification_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `status` enum('read','unread') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unread' */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `created_at` datetime NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `updated_at` datetime NOT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `result_data` json DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `execution_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策：0-未决策，1-已决策' */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理：0-未处理，1-已处理' */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `is_dispose` tinyint(1) DEFAULT '0' COMMENT '是否已处置：0-未处置，1-已处置' */;
/*!999999 ALTER TABLE `w5_notification` ADD COLUMN `dispose_msg` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL */;

-- 为表 `w5_operation_log` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `log_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志ID' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `operator` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `operation_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `operation_time` datetime DEFAULT NULL COMMENT '操作时间' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `operation_details` text COLLATE utf8mb4_general_ci COMMENT '操作详情' */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_operation_log` ADD COLUMN `update_time` datetime DEFAULT NULL */;

-- 为表 `w5_rectification` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `message_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息ID' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `serial_number` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `source_unit` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源单位（如市公司）' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `source_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源编号或来源说明' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `issue_date` date DEFAULT NULL COMMENT '下发时间' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rect_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单类型（二级单位整改单/内部整改单/自建系统整改单）' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `description` text COLLATE utf8mb4_general_ci COMMENT '隐患描述' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `vuln_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患名称' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `vuln_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患类型' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `vuln_level` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患级别（低/中/高危/严重）' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `vuln_count` int DEFAULT NULL COMMENT '隐患个数' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患IP' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `responsible_dept` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任单位/部门' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `is_responded` tinyint(1) DEFAULT '0' COMMENT '是否反馈' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `response_date` date DEFAULT NULL COMMENT '反馈时间' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `is_fixed` tinyint(1) DEFAULT '0' COMMENT '是否整改' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `fix_details` text COLLATE utf8mb4_general_ci COMMENT '整改详情' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `retest_status` tinyint(1) DEFAULT '0' COMMENT '复测状态' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `note` text COLLATE utf8mb4_general_ci COMMENT '备注' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rect_deadline` date DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `remind_time` date DEFAULT NULL COMMENT '提醒时间' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `last_rescan_time` date DEFAULT NULL COMMENT '上次复测时间' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `is_excel_imported` tinyint(1) DEFAULT '0' COMMENT '是否通过Excel导入' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `excel_filename` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入的Excel文件名' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `excel_row_index` int DEFAULT NULL COMMENT '导入时的原始Excel行号' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `import_batch_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入批次ID' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `inspection_content` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察内容' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `inspection_overview` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察实施概况' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `system_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统名称' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `existing_hazard` text COLLATE utf8mb4_general_ci COMMENT '存在隐患' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rectification_suggestion` text COLLATE utf8mb4_general_ci COMMENT '整改建议' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rectification_time_requirement` text COLLATE utf8mb4_general_ci COMMENT '整改时间要求' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `inspection_executor` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察执行负责人' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `rescan_situation` text COLLATE utf8mb4_general_ci COMMENT '复查情况' */;
/*!999999 ALTER TABLE `w5_rectification` ADD COLUMN `inspection_rescan_person` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察复查人员' */;

-- 为表 `w5_report` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_report` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_report` ADD COLUMN `report_no` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_report` ADD COLUMN `workflow_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_report` ADD COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_report` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_rescan_record` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `rescan_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '复测记录ID' */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联整改单ID' */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `rescan_time` datetime DEFAULT NULL COMMENT '复测时间' */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `result` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '复测结果，如“通过/未通过/需复检”' */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `rescan_note` text COLLATE utf8mb4_general_ci COMMENT '复测备注' */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `create_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD COLUMN `update_time` datetime DEFAULT NULL */;

-- 为表 `w5_role` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_role` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_role` ADD COLUMN `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_role` ADD COLUMN `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_role` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_role` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_role_nav` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_role_nav` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_role_nav` ADD COLUMN `role_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_role_nav` ADD COLUMN `nav_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_role_nav` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_setting` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_setting` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_setting` ADD COLUMN `key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_setting` ADD COLUMN `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_setting` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_setting` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_timer` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `timer_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `interval_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `time` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `start_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `end_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `jitter` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_timer` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_type` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_type` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_type` ADD COLUMN `type` int NOT NULL DEFAULT '1' */;
/*!999999 ALTER TABLE `w5_type` ADD COLUMN `name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_type` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_type` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_user_role` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_user_role` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_user_role` ADD COLUMN `user_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_user_role` ADD COLUMN `role_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_user_role` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_users` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `account` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `passwd` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `nick_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `avatar` text COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `email` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `token` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_users` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_variablen` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `type_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `key` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_variablen` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- 为表 `w5_workflow` 添加字段（如果不存在）
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `user_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `type_id` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `status` int NOT NULL DEFAULT '0' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `start_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `end_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `input_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `webhook_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `timer_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `for_list` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `if_list` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `audit_list` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `flow_json` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `flow_data` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `controller_data` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `local_var_data` mediumtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `grid_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `edge_marker` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `edge_color` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `edge_connector` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `edge_router` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `thumbnail` longtext COLLATE utf8mb4_general_ci */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `update_time` datetime DEFAULT NULL */;
/*!999999 ALTER TABLE `w5_workflow` ADD COLUMN `create_time` datetime DEFAULT NULL */;

-- ****************************************************
-- 第二步: 修改字段定义（确保字段使用最新定义）
-- ****************************************************

-- 修改表 `apilog_source_tbl` 的字段定义
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `id` bigint NOT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `source_id` bigint NOT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `apilog_source_tbl` MODIFY COLUMN `params` varchar(510) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;

-- 修改表 `dataset_info` 的字段定义
ALTER TABLE `dataset_info` MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT;
ALTER TABLE `dataset_info` MODIFY COLUMN `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `description` text COLLATE utf8mb4_general_ci;
ALTER TABLE `dataset_info` MODIFY COLUMN `tags` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `num_samples` int DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `format` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `data_schema` text COLLATE utf8mb4_general_ci;
ALTER TABLE `dataset_info` MODIFY COLUMN `storage_path` text COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `download_url` text COLLATE utf8mb4_general_ci;
ALTER TABLE `dataset_info` MODIFY COLUMN `owner` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_info` MODIFY COLUMN `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active';
ALTER TABLE `dataset_info` MODIFY COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP;

-- 修改表 `dataset_logs` 的字段定义
ALTER TABLE `dataset_logs` MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT;
ALTER TABLE `dataset_logs` MODIFY COLUMN `dataset_id` int DEFAULT NULL;
ALTER TABLE `dataset_logs` MODIFY COLUMN `log_type` enum('create','update','delete','download') COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `dataset_logs` MODIFY COLUMN `message` text COLLATE utf8mb4_general_ci;
ALTER TABLE `dataset_logs` MODIFY COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP;

-- 修改表 `dataset_uploads` 的字段定义
ALTER TABLE `dataset_uploads` MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `dataset_id` int DEFAULT NULL;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `uploader` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `upload_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `file_path` text COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `file_size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `dataset_uploads` MODIFY COLUMN `status` enum('pending','success','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending';

-- 修改表 `decision_info` 的字段定义
ALTER TABLE `decision_info` MODIFY COLUMN `event_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `decision_info` MODIFY COLUMN `event_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `decision_info` MODIFY COLUMN `maintenance_method` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `check_item` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `device_software_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `decision_info` MODIFY COLUMN `vendor_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `decision_info` MODIFY COLUMN `harm_name` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `description` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `prevention_measures` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `attack_cause` text COLLATE utf8mb4_general_ci;
ALTER TABLE `decision_info` MODIFY COLUMN `defective_device_software` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `decision_info` MODIFY COLUMN `configuration_solution` text COLLATE utf8mb4_general_ci;

-- 修改表 `file_source_tbl` 的字段定义
ALTER TABLE `file_source_tbl` MODIFY COLUMN `id` bigint NOT NULL COMMENT '主键ID';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `type` varchar(255) NOT NULL COMMENT '数据源类型';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `name` varchar(255) NOT NULL COMMENT '数据源名称';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `headers` text COMMENT 'CSV文件的表头';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `host_index` int DEFAULT NULL COMMENT 'host索引';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `time_index` int DEFAULT NULL COMMENT '时间索引';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `level_index` int DEFAULT NULL COMMENT '日志级别索引';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `type_index` int DEFAULT NULL COMMENT '类型索引';
ALTER TABLE `file_source_tbl` MODIFY COLUMN `source_id` bigint DEFAULT NULL;

-- 修改表 `hardware_resources` 的字段定义
ALTER TABLE `hardware_resources` MODIFY COLUMN `id` int NOT NULL COMMENT '自增主键';
ALTER TABLE `hardware_resources` MODIFY COLUMN `resource_category` varchar(255) NOT NULL COMMENT '资源分类';
ALTER TABLE `hardware_resources` MODIFY COLUMN `resource_type` varchar(255) NOT NULL COMMENT '资源类型';
ALTER TABLE `hardware_resources` MODIFY COLUMN `standard_full_name` varchar(255) NOT NULL COMMENT '标准全称';
ALTER TABLE `hardware_resources` MODIFY COLUMN `manufacturer` varchar(255) NOT NULL COMMENT '制造商';
ALTER TABLE `hardware_resources` MODIFY COLUMN `brand` varchar(255) NOT NULL COMMENT '品牌';
ALTER TABLE `hardware_resources` MODIFY COLUMN `model` varchar(255) NOT NULL COMMENT '型号';
ALTER TABLE `hardware_resources` MODIFY COLUMN `factory_serial_number` varchar(255) NOT NULL COMMENT '出厂序列号';
ALTER TABLE `hardware_resources` MODIFY COLUMN `device_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备状态';
ALTER TABLE `hardware_resources` MODIFY COLUMN `is_synced_to_erp` enum('是','否') NOT NULL COMMENT '是否同步给ERP';
ALTER TABLE `hardware_resources` MODIFY COLUMN `operation_unit` varchar(255) NOT NULL COMMENT '运维单位';
ALTER TABLE `hardware_resources` MODIFY COLUMN `purchase_date` date NOT NULL COMMENT '采购日期';
ALTER TABLE `hardware_resources` MODIFY COLUMN `affiliated_network` varchar(20) NOT NULL COMMENT '所属网络';

-- 修改表 `log_row_tbl` 的字段定义
ALTER TABLE `log_row_tbl` MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'host';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志级别';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `data_source_id` bigint DEFAULT NULL COMMENT '数据源ID';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `data_source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源类型';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `source_id` bigint DEFAULT NULL COMMENT '源ID';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `row_number` int DEFAULT NULL COMMENT '行号';
ALTER TABLE `log_row_tbl` MODIFY COLUMN `raw_string` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '原始字符串';

-- 修改表 `source_tbl` 的字段定义
ALTER TABLE `source_tbl` MODIFY COLUMN `id` bigint NOT NULL COMMENT '主键ID';
ALTER TABLE `source_tbl` MODIFY COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称';
ALTER TABLE `source_tbl` MODIFY COLUMN `service` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务名称';

-- 修改表 `syslog_source_tbl` 的字段定义
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `id` bigint NOT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `type` varchar(255) DEFAULT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `name` varchar(255) NOT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `source_id` bigint NOT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `host` varchar(255) NOT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `port` int DEFAULT NULL;
ALTER TABLE `syslog_source_tbl` MODIFY COLUMN `started` tinyint(1) NOT NULL DEFAULT '0';

-- 修改表 `user_apikeys` 的字段定义
ALTER TABLE `user_apikeys` MODIFY COLUMN `user_id` int NOT NULL;
ALTER TABLE `user_apikeys` MODIFY COLUMN `api_key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `user_apikeys` MODIFY COLUMN `session_id` int NOT NULL;

-- 修改表 `user_bug` 的字段定义
ALTER TABLE `user_bug` MODIFY COLUMN `user_id` int NOT NULL AUTO_INCREMENT;
ALTER TABLE `user_bug` MODIFY COLUMN `bug_description` text COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `user_bug` MODIFY COLUMN `bug_image` longblob;

-- 修改表 `user_chat` 的字段定义
ALTER TABLE `user_chat` MODIFY COLUMN `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `user_chat` MODIFY COLUMN `session_id` int NOT NULL;
ALTER TABLE `user_chat` MODIFY COLUMN `histext` longtext COLLATE utf8mb4_general_ci;

-- 修改表 `user_history` 的字段定义
ALTER TABLE `user_history` MODIFY COLUMN `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `user_history` MODIFY COLUMN `session_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `user_history` MODIFY COLUMN `hismain` text COLLATE utf8mb4_general_ci;
ALTER TABLE `user_history` MODIFY COLUMN `histext` longtext COLLATE utf8mb4_general_ci;

-- 修改表 `w5_alert` 的字段定义
ALTER TABLE `w5_alert` MODIFY COLUMN `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `timestamp` datetime DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `source_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `destination_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `source_port` int DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `destination_port` int DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `protocol` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `attack_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `severity` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `signature` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `detection_system` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `correlation_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `status` int DEFAULT '0';
ALTER TABLE `w5_alert` MODIFY COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策';
ALTER TABLE `w5_alert` MODIFY COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理';
ALTER TABLE `w5_alert` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_alert` MODIFY COLUMN `detail` text COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息';
ALTER TABLE `w5_alert` MODIFY COLUMN `ip_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息';

-- 修改表 `w5_alert_analysis` 的字段定义
ALTER TABLE `w5_alert_analysis` MODIFY COLUMN `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_alert_analysis` MODIFY COLUMN `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_alert_analysis` MODIFY COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_alert_analysis` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_alert_copy1` 的字段定义
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `alert_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `timestamp` datetime DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `source_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `destination_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `source_port` int DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `destination_port` int DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `attack_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `detection_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `correlation_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `status` int DEFAULT '0';
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策';
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理';
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息';
ALTER TABLE `w5_alert_copy1` MODIFY COLUMN `ip_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息';

-- 修改表 `w5_analysis` 的字段定义
ALTER TABLE `w5_analysis` MODIFY COLUMN `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_analysis` MODIFY COLUMN `timestamp` datetime DEFAULT NULL;
ALTER TABLE `w5_analysis` MODIFY COLUMN `attack_summary` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_analysis` MODIFY COLUMN `affected_systems` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_analysis` MODIFY COLUMN `potential_risk` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_analysis` MODIFY COLUMN `recommended_actions` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_analysis` MODIFY COLUMN `notes` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_analysis` MODIFY COLUMN `status` int DEFAULT '0';
ALTER TABLE `w5_analysis` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_analysis` MODIFY COLUMN `update_time` datetime DEFAULT NULL;

-- 修改表 `w5_audit` 的字段定义
ALTER TABLE `w5_audit` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_audit` MODIFY COLUMN `workflow_uuid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_audit` MODIFY COLUMN `only_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_audit` MODIFY COLUMN `user_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_audit` MODIFY COLUMN `audit_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_audit` MODIFY COLUMN `start_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_audit` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_audit` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_audit` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_login_history` 的字段定义
ALTER TABLE `w5_login_history` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_login_history` MODIFY COLUMN `user_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_login_history` MODIFY COLUMN `login_time` datetime NOT NULL;

-- 修改表 `w5_logs` 的字段定义
ALTER TABLE `w5_logs` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_logs` MODIFY COLUMN `only_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_logs` MODIFY COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_logs` MODIFY COLUMN `app_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_logs` MODIFY COLUMN `app_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_logs` MODIFY COLUMN `result` mediumtext COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_logs` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_logs` MODIFY COLUMN `html` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_logs` MODIFY COLUMN `args` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_logs` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_nav` 的字段定义
ALTER TABLE `w5_nav` MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_nav` MODIFY COLUMN `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_nav` MODIFY COLUMN `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_nav` MODIFY COLUMN `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_nav` MODIFY COLUMN `icon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_nav` MODIFY COLUMN `is_menu` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_nav` MODIFY COLUMN `up` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_nav` MODIFY COLUMN `order` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_nav` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_notification` 的字段定义
ALTER TABLE `w5_notification` MODIFY COLUMN `notification_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `notification_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `status` enum('read','unread') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unread';
ALTER TABLE `w5_notification` MODIFY COLUMN `created_at` datetime NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `updated_at` datetime NOT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `result_data` json DEFAULT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `execution_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL;
ALTER TABLE `w5_notification` MODIFY COLUMN `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策：0-未决策，1-已决策';
ALTER TABLE `w5_notification` MODIFY COLUMN `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理：0-未处理，1-已处理';
ALTER TABLE `w5_notification` MODIFY COLUMN `is_dispose` tinyint(1) DEFAULT '0' COMMENT '是否已处置：0-未处置，1-已处置';
ALTER TABLE `w5_notification` MODIFY COLUMN `dispose_msg` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;

-- 修改表 `w5_operation_log` 的字段定义
ALTER TABLE `w5_operation_log` MODIFY COLUMN `log_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志ID';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `operator` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `operation_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `operation_time` datetime DEFAULT NULL COMMENT '操作时间';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `operation_details` text COLLATE utf8mb4_general_ci COMMENT '操作详情';
ALTER TABLE `w5_operation_log` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_operation_log` MODIFY COLUMN `update_time` datetime DEFAULT NULL;

-- 修改表 `w5_rectification` 的字段定义
ALTER TABLE `w5_rectification` MODIFY COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID';
ALTER TABLE `w5_rectification` MODIFY COLUMN `message_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息ID';
ALTER TABLE `w5_rectification` MODIFY COLUMN `serial_number` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号';
ALTER TABLE `w5_rectification` MODIFY COLUMN `source_unit` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源单位（如市公司）';
ALTER TABLE `w5_rectification` MODIFY COLUMN `source_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源编号或来源说明';
ALTER TABLE `w5_rectification` MODIFY COLUMN `issue_date` date DEFAULT NULL COMMENT '下发时间';
ALTER TABLE `w5_rectification` MODIFY COLUMN `rect_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单类型（二级单位整改单/内部整改单/自建系统整改单）';
ALTER TABLE `w5_rectification` MODIFY COLUMN `description` text COLLATE utf8mb4_general_ci COMMENT '隐患描述';
ALTER TABLE `w5_rectification` MODIFY COLUMN `vuln_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患名称';
ALTER TABLE `w5_rectification` MODIFY COLUMN `vuln_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患类型';
ALTER TABLE `w5_rectification` MODIFY COLUMN `vuln_level` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患级别（低/中/高危/严重）';
ALTER TABLE `w5_rectification` MODIFY COLUMN `vuln_count` int DEFAULT NULL COMMENT '隐患个数';
ALTER TABLE `w5_rectification` MODIFY COLUMN `ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患IP';
ALTER TABLE `w5_rectification` MODIFY COLUMN `responsible_dept` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任单位/部门';
ALTER TABLE `w5_rectification` MODIFY COLUMN `is_responded` tinyint(1) DEFAULT '0' COMMENT '是否反馈';
ALTER TABLE `w5_rectification` MODIFY COLUMN `response_date` date DEFAULT NULL COMMENT '反馈时间';
ALTER TABLE `w5_rectification` MODIFY COLUMN `is_fixed` tinyint(1) DEFAULT '0' COMMENT '是否整改';
ALTER TABLE `w5_rectification` MODIFY COLUMN `fix_details` text COLLATE utf8mb4_general_ci COMMENT '整改详情';
ALTER TABLE `w5_rectification` MODIFY COLUMN `retest_status` tinyint(1) DEFAULT '0' COMMENT '复测状态';
ALTER TABLE `w5_rectification` MODIFY COLUMN `note` text COLLATE utf8mb4_general_ci COMMENT '备注';
ALTER TABLE `w5_rectification` MODIFY COLUMN `rect_deadline` date DEFAULT NULL;
ALTER TABLE `w5_rectification` MODIFY COLUMN `remind_time` date DEFAULT NULL COMMENT '提醒时间';
ALTER TABLE `w5_rectification` MODIFY COLUMN `last_rescan_time` date DEFAULT NULL COMMENT '上次复测时间';
ALTER TABLE `w5_rectification` MODIFY COLUMN `is_excel_imported` tinyint(1) DEFAULT '0' COMMENT '是否通过Excel导入';
ALTER TABLE `w5_rectification` MODIFY COLUMN `excel_filename` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入的Excel文件名';
ALTER TABLE `w5_rectification` MODIFY COLUMN `excel_row_index` int DEFAULT NULL COMMENT '导入时的原始Excel行号';
ALTER TABLE `w5_rectification` MODIFY COLUMN `import_batch_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入批次ID';
ALTER TABLE `w5_rectification` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_rectification` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_rectification` MODIFY COLUMN `inspection_content` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察内容';
ALTER TABLE `w5_rectification` MODIFY COLUMN `inspection_overview` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察实施概况';
ALTER TABLE `w5_rectification` MODIFY COLUMN `system_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统名称';
ALTER TABLE `w5_rectification` MODIFY COLUMN `existing_hazard` text COLLATE utf8mb4_general_ci COMMENT '存在隐患';
ALTER TABLE `w5_rectification` MODIFY COLUMN `rectification_suggestion` text COLLATE utf8mb4_general_ci COMMENT '整改建议';
ALTER TABLE `w5_rectification` MODIFY COLUMN `rectification_time_requirement` text COLLATE utf8mb4_general_ci COMMENT '整改时间要求';
ALTER TABLE `w5_rectification` MODIFY COLUMN `inspection_executor` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察执行负责人';
ALTER TABLE `w5_rectification` MODIFY COLUMN `rescan_situation` text COLLATE utf8mb4_general_ci COMMENT '复查情况';
ALTER TABLE `w5_rectification` MODIFY COLUMN `inspection_rescan_person` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察复查人员';

-- 修改表 `w5_report` 的字段定义
ALTER TABLE `w5_report` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_report` MODIFY COLUMN `report_no` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_report` MODIFY COLUMN `workflow_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_report` MODIFY COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_report` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_rescan_record` 的字段定义
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `rescan_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '复测记录ID';
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联整改单ID';
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `rescan_time` datetime DEFAULT NULL COMMENT '复测时间';
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `result` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '复测结果，如“通过/未通过/需复检”';
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `rescan_note` text COLLATE utf8mb4_general_ci COMMENT '复测备注';
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `create_time` datetime DEFAULT NULL;
ALTER TABLE `w5_rescan_record` MODIFY COLUMN `update_time` datetime DEFAULT NULL;

-- 修改表 `w5_role` 的字段定义
ALTER TABLE `w5_role` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_role` MODIFY COLUMN `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_role` MODIFY COLUMN `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_role` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_role` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_role_nav` 的字段定义
ALTER TABLE `w5_role_nav` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_role_nav` MODIFY COLUMN `role_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_role_nav` MODIFY COLUMN `nav_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_role_nav` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_setting` 的字段定义
ALTER TABLE `w5_setting` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_setting` MODIFY COLUMN `key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_setting` MODIFY COLUMN `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_setting` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_setting` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_timer` 的字段定义
ALTER TABLE `w5_timer` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_timer` MODIFY COLUMN `timer_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `interval_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `time` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `start_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `end_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_timer` MODIFY COLUMN `jitter` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_timer` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_timer` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_timer` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_type` 的字段定义
ALTER TABLE `w5_type` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_type` MODIFY COLUMN `type` int NOT NULL DEFAULT '1';
ALTER TABLE `w5_type` MODIFY COLUMN `name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_type` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_type` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_user_role` 的字段定义
ALTER TABLE `w5_user_role` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_user_role` MODIFY COLUMN `user_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_user_role` MODIFY COLUMN `role_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_user_role` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_users` 的字段定义
ALTER TABLE `w5_users` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_users` MODIFY COLUMN `account` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_users` MODIFY COLUMN `passwd` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_users` MODIFY COLUMN `nick_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_users` MODIFY COLUMN `avatar` text COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_users` MODIFY COLUMN `email` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_users` MODIFY COLUMN `token` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_users` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_users` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_users` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_variablen` 的字段定义
ALTER TABLE `w5_variablen` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_variablen` MODIFY COLUMN `type_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_variablen` MODIFY COLUMN `key` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_variablen` MODIFY COLUMN `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_variablen` MODIFY COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_variablen` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_variablen` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_variablen` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- 修改表 `w5_workflow` 的字段定义
ALTER TABLE `w5_workflow` MODIFY COLUMN `id` int unsigned NOT NULL AUTO_INCREMENT;
ALTER TABLE `w5_workflow` MODIFY COLUMN `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `user_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_workflow` MODIFY COLUMN `type_id` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_workflow` MODIFY COLUMN `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `status` int NOT NULL DEFAULT '0';
ALTER TABLE `w5_workflow` MODIFY COLUMN `start_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `end_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `input_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `webhook_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `timer_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `for_list` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `if_list` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `audit_list` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `flow_json` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `flow_data` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `controller_data` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `local_var_data` mediumtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `grid_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `edge_marker` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `edge_color` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `edge_connector` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `edge_router` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '';
ALTER TABLE `w5_workflow` MODIFY COLUMN `thumbnail` longtext COLLATE utf8mb4_general_ci;
ALTER TABLE `w5_workflow` MODIFY COLUMN `update_time` datetime DEFAULT NULL;
ALTER TABLE `w5_workflow` MODIFY COLUMN `create_time` datetime DEFAULT NULL;

-- ****************************************************
-- 第三步: 添加索引（如果不存在，忽略错误）
-- ****************************************************

-- 为表 `apilog_source_tbl` 添加索引
/*!999999 ALTER TABLE `apilog_source_tbl` ADD PRIMARY KEY (`id`) */;

-- 为表 `dataset_info` 添加索引
/*!999999 ALTER TABLE `dataset_info` ADD PRIMARY KEY (`id`) */;

-- 为表 `dataset_logs` 添加索引
/*!999999 ALTER TABLE `dataset_logs` ADD PRIMARY KEY (`id`) */;

-- 为表 `dataset_uploads` 添加索引
/*!999999 ALTER TABLE `dataset_uploads` ADD PRIMARY KEY (`id`) */;

-- 为表 `decision_info` 添加索引
/*!999999 ALTER TABLE `decision_info` ADD PRIMARY KEY (`event_id`) */;

-- 为表 `file_source_tbl` 添加索引
/*!999999 ALTER TABLE `file_source_tbl` ADD PRIMARY KEY (`id`) */;
/*!999999 ALTER TABLE `file_source_tbl` ADD KEY `fk_source_id` (`source_id`) */;

-- 为表 `hardware_resources` 添加索引
/*!999999 ALTER TABLE `hardware_resources` ADD PRIMARY KEY (`id`) */;

-- 为表 `log_row_tbl` 添加索引
/*!999999 ALTER TABLE `log_row_tbl` ADD PRIMARY KEY (`id`) */;

-- 为表 `source_tbl` 添加索引
/*!999999 ALTER TABLE `source_tbl` ADD PRIMARY KEY (`id`) */;

-- 为表 `syslog_source_tbl` 添加索引
/*!999999 ALTER TABLE `syslog_source_tbl` ADD PRIMARY KEY (`id`) */;

-- 为表 `user_apikeys` 添加索引
/*!999999 ALTER TABLE `user_apikeys` ADD PRIMARY KEY (`user_id`,`api_key`,`session_id`) */;

-- 为表 `user_bug` 添加索引
/*!999999 ALTER TABLE `user_bug` ADD PRIMARY KEY (`user_id`) */;

-- 为表 `user_chat` 添加索引
/*!999999 ALTER TABLE `user_chat` ADD PRIMARY KEY (`user_id`,`session_id`) */;

-- 为表 `user_history` 添加索引
/*!999999 ALTER TABLE `user_history` ADD PRIMARY KEY (`user_id`,`session_id`) */;

-- 为表 `w5_alert` 添加索引
/*!999999 ALTER TABLE `w5_alert` ADD PRIMARY KEY (`alert_id`) */;

-- 为表 `w5_alert_analysis` 添加索引
/*!999999 ALTER TABLE `w5_alert_analysis` ADD PRIMARY KEY (`analysis_id`,`alert_id`) */;

-- 为表 `w5_alert_copy1` 添加索引
/*!999999 ALTER TABLE `w5_alert_copy1` ADD PRIMARY KEY (`alert_id`) */;

-- 为表 `w5_analysis` 添加索引
/*!999999 ALTER TABLE `w5_analysis` ADD PRIMARY KEY (`analysis_id`) */;

-- 为表 `w5_audit` 添加索引
/*!999999 ALTER TABLE `w5_audit` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_login_history` 添加索引
/*!999999 ALTER TABLE `w5_login_history` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_logs` 添加索引
/*!999999 ALTER TABLE `w5_logs` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_nav` 添加索引
/*!999999 ALTER TABLE `w5_nav` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_notification` 添加索引
/*!999999 ALTER TABLE `w5_notification` ADD PRIMARY KEY (`notification_id`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_user_status` (`user_id`,`status`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_created_at` (`created_at`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_source` (`source`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_notification_type` (`notification_type`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_is_decided` (`is_decided`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_is_processed` (`is_processed`) */;
/*!999999 ALTER TABLE `w5_notification` ADD KEY `idx_is_dispose` (`is_dispose`) */;

-- 为表 `w5_operation_log` 添加索引
/*!999999 ALTER TABLE `w5_operation_log` ADD PRIMARY KEY (`log_id`) */;
/*!999999 ALTER TABLE `w5_operation_log` ADD KEY `idx_rect_id` (`rect_id`) */;
/*!999999 ALTER TABLE `w5_operation_log` ADD KEY `idx_operation_time` (`operation_time`) */;

-- 为表 `w5_rectification` 添加索引
/*!999999 ALTER TABLE `w5_rectification` ADD PRIMARY KEY (`rect_id`) */;
/*!999999 ALTER TABLE `w5_rectification` ADD KEY `idx_message_id` (`message_id`) */;

-- 为表 `w5_report` 添加索引
/*!999999 ALTER TABLE `w5_report` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_rescan_record` 添加索引
/*!999999 ALTER TABLE `w5_rescan_record` ADD PRIMARY KEY (`rescan_id`) */;
/*!999999 ALTER TABLE `w5_rescan_record` ADD KEY `idx_rect_id` (`rect_id`) */;

-- 为表 `w5_role` 添加索引
/*!999999 ALTER TABLE `w5_role` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_role_nav` 添加索引
/*!999999 ALTER TABLE `w5_role_nav` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_setting` 添加索引
/*!999999 ALTER TABLE `w5_setting` ADD PRIMARY KEY (`id`) */;
/*!999999 ALTER TABLE `w5_setting` ADD UNIQUE KEY `index_key` (`key`) USING BTREE */;

-- 为表 `w5_timer` 添加索引
/*!999999 ALTER TABLE `w5_timer` ADD PRIMARY KEY (`id`) */;
/*!999999 ALTER TABLE `w5_timer` ADD UNIQUE KEY `uid` (`timer_uuid`) USING BTREE */;

-- 为表 `w5_type` 添加索引
/*!999999 ALTER TABLE `w5_type` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_user_role` 添加索引
/*!999999 ALTER TABLE `w5_user_role` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_users` 添加索引
/*!999999 ALTER TABLE `w5_users` ADD PRIMARY KEY (`id`) */;
/*!999999 ALTER TABLE `w5_users` ADD UNIQUE KEY `index_account` (`account`) USING BTREE */;

-- 为表 `w5_variablen` 添加索引
/*!999999 ALTER TABLE `w5_variablen` ADD PRIMARY KEY (`id`) */;

-- 为表 `w5_workflow` 添加索引
/*!999999 ALTER TABLE `w5_workflow` ADD PRIMARY KEY (`id`) */;
/*!999999 ALTER TABLE `w5_workflow` ADD UNIQUE KEY `index_uuid` (`uuid`) USING BTREE */;

-- ****************************************************
-- 脚本执行完成
-- ****************************************************