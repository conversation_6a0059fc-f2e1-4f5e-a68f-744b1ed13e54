# 绿盟漏洞数据分析系统

## 项目简介

本项目是一个基于CVSS标准的漏洞风险评分系统，用于分析绿盟安全设备导出的漏洞数据，计算风险评分，并生成相关报告。系统结合了国际标准化的CVSS评分与环境特定因素，提供科学、准确的漏洞风险评估。

## 项目结构

```
├── main.py                           # 主程序入口
├── step1_parse_vuln_data.py          # 步骤1: 数据解析模块
├── step2_vuln_risk_score.py          # 步骤2: 风险评分计算模块
├── step3_create_simplified_csv.py    # 步骤3: 报告生成模块
├── sourcedata.txt                    # 原始漏洞数据
├── 绿盟漏洞数据分析流程文档.md        # 详细流程文档
├── 绿盟漏洞数据分析流程图.md          # 流程可视化图
└── CVSS漏洞风险评分系统 - 理论依据与数学方法.md  # 理论依据文档
```

## 工作流程

1. **数据解析**: 解析原始漏洞数据文件
2. **风险评分**: 计算漏洞风险评分
3. **报告生成**: 创建简化版漏洞报告

## 使用方法

1. 准备原始漏洞数据文件 `sourcedata.txt`
2. 运行主程序:
   ```
   python main.py
   ```
3. 查看生成的报告文件:
   - `step1_parsed_vuln_data.csv`: 原始漏洞数据解析结果
   - `step2_vuln_detailed_scores.csv`: 详细风险评分数据
   - `step2_vuln_ip_risk_summary.csv`: IP风险汇总数据
   - `step3_vuln_detailed_scores_simplified.csv`: 简化版漏洞报告

## 评分模型

本项目采用混合评分模型，结合CVSS标准评分、传统评分方法和环境因素:

```
综合评分 = 0.6 × CVSS评分 + 0.3 × 传统评分 + 0.1 × 环境因素
```

## 文档说明

- **绿盟漏洞数据分析流程文档.md**: 详细描述了整个数据分析流程，包括各个步骤的输入、输出和处理逻辑
- **绿盟漏洞数据分析流程图.md**: 提供了可视化的流程图，直观展示整个数据分析流程
- **CVSS漏洞风险评分系统 - 理论依据与数学方法.md**: 说明了风险评分系统的理论依据和数学方法