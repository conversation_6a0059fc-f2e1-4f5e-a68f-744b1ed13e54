import pandas as pd
import json
import re
from datetime import datetime
import random
from datetime import datetime
import random
import sys
import os
import configparser
import pymysql

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))


def get_security_logs_from_db():
    try:
        # 读取配置文件
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config.ini')
        cf = configparser.ConfigParser()
        cf.read(config_path, encoding='utf-8')
        # 数据库配置
        DB_CONFIG = {
            'host': cf.get("mysql", "host"),
            'port': int(cf.get("mysql", "port")),
            'user': cf.get("mysql", "user"),
            'password': cf.get("mysql", "password"),
            'database': cf.get("mysql", "database"),
            'charset': 'utf8mb4'
        }

        print(f"连接数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")

        # 建立数据库连接
        conn = pymysql.connect(**DB_CONFIG)

        try:
            # 查询log_row_tbl表数据
            sql = """
            SELECT 
             `id` ,`host`, `time`, `type`, `level`, `data_source_id`,
            `data_source_type`, `source_id`, `row_number`, `raw_string`
            FROM `log_row_tbl` 
            WHERE `data_source_id` = 1950139452227772417
            ORDER BY `time` DESC
            """

            # 使用connection对象的cursor执行查询
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                # 创建DataFrame
                df = pd.DataFrame(result, columns=columns)
            return df

        except Exception as e:
            print(f"数据库查询错误: {e}")
            return pd.DataFrame()
        finally:
            conn.close()

    except Exception as e:
        print(f"数据库查询失败: {e}")
        print("无法从数据库获取数据")
        return pd.DataFrame()


def extract_parsed_infos(message_text):
    """
    从消息文本中提取parsedInfos JSON数据
    """
    try:
        # 查找JSON部分
        json_match = re.search(r'\{.*\}', message_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            # 修复双引号转义问题
            json_str = json_str.replace('""', '"')
            # 处理截断的JSON - 如果JSON不完整，尝试修复
            if not json_str.endswith('}'):
                # 找到最后一个完整的字段
                last_comma = json_str.rfind(',')
                if last_comma > 0:
                    json_str = json_str[:last_comma] + '}'
                else:
                    json_str += '}'
            data = json.loads(json_str)
            return data.get('parsedInfos', {})
    except (json.JSONDecodeError, AttributeError) as e:
        # 静默处理JSON解析错误，避免过多输出
        pass

    # 如果JSON解析失败，尝试从消息中直接提取关键信息
    try:
        # 提取时间信息
        time_match = re.search(r'([A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})', message_text)
        time_str = time_match.group(1) if time_match else ""

        # 尝试提取IP地址
        ip_matches = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', message_text)

        # 构建基本信息
        parsed_info = {}
        if time_str:
            parsed_info['deviceReceiptTime'] = f"2025-{time_str}"

        # 如果找到IP地址，假设第一个是源IP，第二个是目标IP
        if len(ip_matches) >= 1:
            parsed_info['srcAddress'] = ip_matches[0]
        if len(ip_matches) >= 2:
            parsed_info['destAddress'] = ip_matches[1]

        # 尝试提取端口信息
        port_matches = re.findall(r'port\s*(\d+)', message_text.lower())
        if len(port_matches) >= 1:
            parsed_info['srcPort'] = port_matches[0]
        if len(port_matches) >= 2:
            parsed_info['destPort'] = port_matches[1]

        return parsed_info
    except Exception:
        # 如果所有尝试都失败，返回空字典
        return {}


def convert_time_format(time_str):
    """
    转换时间格式
    """
    try:
        # 处理不同的时间格式
        # 完整年月日格式 (2025-MM-DD)
        if re.match(r'\d{4}-\d{2}-\d{2}', time_str):
            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        # 只有月日格式 (MM-DD)
        elif re.match(r'\d{2}-\d{2}', time_str):
            # 使用2025年，与数据集保持一致
            dt = datetime.strptime(f'2025-{time_str}', '%Y-%m-%d %H:%M:%S')
        # Jul 29 格式
        elif re.match(r'[A-Za-z]{3}\s+\d{1,2}', time_str):
            # 提取月份和日期
            month_day_match = re.search(r'([A-Za-z]{3})\s+(\d{1,2})', time_str)
            if month_day_match:
                month = month_day_match.group(1)
                day = month_day_match.group(2)
                # 提取时间
                time_match = re.search(r'\d{2}:\d{2}:\d{2}', time_str)
                time_part = time_match.group(0) if time_match else "00:00:00"
                # 使用2025年，与数据集保持一致
                try:
                    dt = datetime.strptime(f"2025 {month} {day} {time_part}", "%Y %b %d %H:%M:%S")
                    # 特别处理7月29日的格式
                    if month == 'Jul' and day == '29':
                        return f"2025/07/29 {time_part[:5]}"
                except ValueError:
                    return time_str
            else:
                return time_str
        # 处理2025-Jul 29格式
        elif '2025-Jul 29' in time_str:
            time_match = re.search(r'\d{2}:\d{2}:\d{2}', time_str)
            time_part = time_match.group(0) if time_match else "00:00:00"
            return f"2025/07/29 {time_part[:5]}"
        else:
            return time_str
        return dt.strftime('%Y/%m/%d %H:%M')
    except Exception as e:
        # print(f"时间转换错误: {time_str}, 错误: {e}")
        return time_str


def map_severity(dvc_severity):
    """
    映射严重性等级
    """
    severity_map = {
        '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 5,
        '7': 5, '8': 5, '9': 5, '10': 5
    }
    return severity_map.get(str(dvc_severity), 3)


def map_event_type(attack_result, event_type):
    """
    映射事件类型
    """
    if attack_result == '成功':
        return 1
    elif attack_result == '尝试':
        return 2
    elif event_type == '1':
        return 1
    else:
        return random.randint(1, 5)


def get_event_name(rule_id, device_cat, attack_result):
    """
    根据规则ID和设备类别生成事件名称
    """
    event_names = {
        '268572449': '未授权访问尝试',
        '268440041': '弱口令检测',
        '24459': 'SQL注入攻击',
        '268567871': 'HTTP方法滥用'
    }

    if rule_id in event_names:
        return event_names[rule_id]
    elif '/IDS/Network' in str(device_cat):
        return '网络入侵检测'
    elif attack_result == '成功':
        return '安全事件'
    else:
        return '网络监控事件'


def get_log_level(dvc_severity, attack_result):
    """
    根据严重性和攻击结果确定日志级别
    """
    severity = int(dvc_severity) if str(dvc_severity).isdigit() else 3

    if attack_result == '成功' or severity >= 8:
        return 'ERROR'
    elif attack_result == '尝试' or severity >= 5:
        return 'WARN'
    else:
        return 'INFO'


def convert_qianxin_to_enhanced(data):
    print(f"从数据库读取到 {len(data)} 条记录")
    # 定义列名（基于观察到的数据结构）
    columns = ['id', 'host', 'time', 'type', 'level', 'data_source_id',
               'data_source_type', 'source_id', 'row_number', 'raw_string']
    data.columns = columns[:len(data.columns)]
    converted_data = []

    for index, row in data.iterrows():
        try:
            # 跳过测试数据
            if 'TEST' in str(row.get('alert_type', '')):
                continue

            raw_string = str(row.get('raw_string', ''))
            if not raw_string or raw_string == 'nan':
                continue

            # 提取parsedInfos
            parsed_infos = extract_parsed_infos(raw_string)
            # 不再完全依赖parsed_infos是否为空来过滤数据
            # 即使parsed_infos为空或不完整，也尝试处理

            # 提取关键字段，为缺失字段提供默认值
            src_address = parsed_infos.get('srcAddress', '********')
            dest_address = parsed_infos.get('destAddress', '********')
            src_port = parsed_infos.get('srcPort', '1024')
            dest_port = parsed_infos.get('destPort', '80')
            send_host_address = parsed_infos.get('sendHostAddress', '127.0.0.1')
            device_name = parsed_infos.get('deviceName', '奇安信流量传感器')
            dvc_severity = parsed_infos.get('dvcSeverity', '3')
            attack_result = parsed_infos.get('attackResult', '尝试')
            event_type_raw = parsed_infos.get('eventType', '1')
            rule_id = parsed_infos.get('ruleId', '')
            device_cat = parsed_infos.get('deviceCat', '/IDS/Network')
            device_receipt_time = parsed_infos.get('deviceReceiptTime', '')
            dest_host_name = parsed_infos.get('destHostName', '')
            referer = parsed_infos.get('referer', '')

            host_value = ''
            if dest_host_name:
                host_value = dest_host_name
            elif referer:
                m = re.match(r'^[a-zA-Z]+://([^/]+)', referer)
                if m:
                    host_value = m.group(1)
            if not host_value:
                host_value = dest_address if dest_address else str(row.get('host', send_host_address))

            # 构建转换后的记录
            converted_record = {
                'id': str(row.get('id', '')),
                'host': host_value,  # 使用新的目标域名逻辑
                'time': convert_time_format(str(row.get('time', ''))) if '07-29' not in str(
                    row.get('time', '')) else '2025/07/29 ' + str(row.get('time', '')).split(' ')[-1] if ' ' in str(
                    row.get('time', '')) else '2025/07/29 00:00',
                'level': get_log_level(dvc_severity, attack_result),
                'data_source_id': str(row.get('data_source_id', '')),
                'data_source_type': 'syslog',
                'source_id': str(row.get('source_id', '')),
                'srcAddress': src_address,
                'destAddress': dest_address,
                'srcPort': src_port,
                'destPort': dest_port,
                'eventType': map_event_type(attack_result, event_type_raw),
                'severity': map_severity(dvc_severity),
                'eventName': get_event_name(rule_id, device_cat, attack_result),
                'deviceName': device_name,
                'rawEventTime': convert_time_format(device_receipt_time) if device_receipt_time else (
                    '2025/07/29 ' + str(row.get('time', '')).split(' ')[-1] if '07-29' in str(
                        row.get('time', '')) and ' ' in str(row.get('time', '')) else convert_time_format(
                        str(row.get('time', '')))),
                'deviceCat': device_cat,
                'attackResult': attack_result,
                'solution': parsed_infos.get('solution', ''),
                'detailInfo': parsed_infos.get('detailInfo', '')
            }

            converted_data.append(converted_record)

        except Exception as e:
            print(f"处理第 {index + 1} 行时出错: {e}")
            continue

    # 创建DataFrame并保存
    if converted_data:
        result_df = pd.DataFrame(converted_data)
        return result_df
        # result_df.to_csv(output_file, index=False, encoding='utf-8')
    else:
        print("没有找到有效的数据进行转换")
        return None


def main():
    df = get_security_logs_from_db()
    step1_result1 = convert_qianxin_to_enhanced(df)
    print(step1_result1)
    return step1_result1

if __name__ == '__main__':
    main()
