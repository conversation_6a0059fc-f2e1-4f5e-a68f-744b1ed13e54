#!/usr/bin/env python
# encoding:utf-8
"""
测试step4_work_time.py与step3_business_analysis.py的集成
"""

import pandas as pd
from step4_work_time_fw import main as get_work_time_data

def test_step4_integration():
    """
    测试step4能否正确调用step3的方法并处理数据
    """
    print("开始测试step4_work_time.py集成...")
    
    try:
        # 调用step4的main函数
        result_df = get_work_time_data()
        
        if result_df is not None and not result_df.empty:
            print(f"✓ step4成功处理了 {len(result_df)} 条记录")
            
            # 检查是否包含工作时间标记列
            if 'is_work_time' in result_df.columns:
                work_time_stats = result_df['is_work_time'].value_counts()
                print(f"✓ 工作时间统计: {work_time_stats.to_dict()}")
            else:
                print("✗ 缺少工作时间标记列")
                return False
            
            # 检查是否包含业务相关性列（来自step3）
            if 'is_business_related' in result_df.columns:
                business_stats = result_df['is_business_related'].value_counts()
                print(f"✓ 业务相关性统计: {business_stats.to_dict()}")
            else:
                print("✗ 缺少业务相关性列")
                return False
            
            # 检查是否包含流向信息（来自step2）
            if 'flow_direction' in result_df.columns:
                flow_stats = result_df['flow_direction'].value_counts()
                print(f"✓ 流向统计: {flow_stats.to_dict()}")
            else:
                print("✗ 缺少流向信息列")
                return False
            
            # 保存测试结果
            result_df.to_csv('test_step4_integration_result.csv', index=False)
            print("✓ 测试结果已保存至 test_step4_integration_result.csv")
            
            return True
        else:
            print("✗ step4未获取到数据")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_step4_integration()
    if success:
        print("\n🎉 step4集成测试通过！")
    else:
        print("\n❌ step4集成测试失败！")