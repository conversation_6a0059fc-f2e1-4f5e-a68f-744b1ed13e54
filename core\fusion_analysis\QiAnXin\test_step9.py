#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试step9_alert_analysis2.py模块的功能
"""

import step9_alert_analysis2

def test_step9():
    print("=== 测试step9_alert_analysis2模块 ===")
    
    try:
        # 调用step9的main函数
        result = step9_alert_analysis2.main()
        
        if result is None:
            print("错误: step9模块返回None")
            return
        
        df, statistics = result
        
        print(f"\n处理后的数据量: {len(df)}条")
        print(f"数据列: {list(df.columns)}")
        
        print(f"\n统计结果包含: {list(statistics.keys())}")
        
        # 显示攻击结果统计
        if 'attack_result_counts' in statistics:
            print("\n攻击结果统计:")
            for result, count in statistics['attack_result_counts'].items():
                print(f"  {result}: {count}条")
        
        # 显示置信度统计
        if 'confidence_counts' in statistics:
            print("\n置信度统计:")
            for conf, count in statistics['confidence_counts'].items():
                print(f"  {conf}: {count}条")
        
        print("\n=== step9模块测试成功 ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_step9()