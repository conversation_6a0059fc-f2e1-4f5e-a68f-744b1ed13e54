## APP 说明

> 整改截止时间查询

## 动作列表

### 查询超期记录

**功能：**
查询数据库中 `w5_rectification` 表格中当前时间超过 `rect_deadline` 的整改单记录

**参数：**

|  参数   | 类型  |  必填   |  默认值  |  备注  |
|  ----  | ----  |  ----  |  ----  |  ----  |
| **limit**  | number | `否` | 10 | 返回记录数量限制 |

**返回值：**

```json
{
  "status": 0,
  "result": {
    "overdue_count": 5,
    "records": [
      {
        "rect_id": "uuid",
        "serial_number": "整改单编号",
        "vuln_name": "隐患名称",
        "rect_deadline": "2025-01-01",
        "days_overdue": 30,
        "responsible_dept": "责任单位",
        "is_fixed": false
      }
    ]
  }
}
```

**说明：**
- 只返回未整改完成的记录（`is_fixed = false`）
- 按超期天数降序排列
- 超期天数 = 当前日期 - 整改截止日期 