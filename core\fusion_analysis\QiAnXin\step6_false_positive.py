import pandas as pd
import numpy as np
import os
import json

# 导入依赖模块
from step4_work_time import get_business_analysis_data, mark_work_time
from step5_temp import get_work_time_data, generate_threat_scores

# 已知误报IP列表文件路径
KNOWN_FALSE_POSITIVE_IPS_FILE = os.path.join(os.path.dirname(__file__), 'known_false_positive_ips.json')

# 可信来源白名单文件路径
TRUSTED_SOURCE_WHITELIST_FILE = os.path.join(os.path.dirname(__file__), 'trusted_source_whitelist.json')

# 默认已知误报IP列表
DEFAULT_KNOWN_FALSE_POSITIVE_IPS = [
    {'ip_address': '***********', 'description': '内部监控服务器', 'type': 'internal_monitor'},
    {'ip_address': '**************', 'description': '内部应用服务器', 'type': 'internal_app'},
    {'ip_address': '**************', 'description': '内部数据库服务器', 'type': 'internal_db'},
    {'ip_address': '**********', 'description': '可信外部服务', 'type': 'trusted_external'}
]

# 默认可信来源白名单
DEFAULT_TRUSTED_SOURCE_WHITELIST = [
    {'ip_address': '***********', 'description': '内部监控服务器', 'type': 'internal_monitor'},
    {'ip_address': '**************', 'description': '内部应用服务器', 'type': 'internal_app'},
    {'ip_address': '**************', 'description': '内部数据库服务器', 'type': 'internal_db'},
    {'ip_address': '*************', 'description': '内部网关', 'type': 'internal_gateway'},
    {'ip_address': '**********', 'description': '内部DNS服务器', 'type': 'internal_dns'}
]

def get_known_false_positive_ips():
    """
    获取已知误报IP列表
    返回IP地址的集合
    """
    try:
        if os.path.exists(KNOWN_FALSE_POSITIVE_IPS_FILE):
            with open(KNOWN_FALSE_POSITIVE_IPS_FILE, 'r', encoding='utf-8') as f:
                ip_list = json.load(f)
                return set(item['ip_address'] for item in ip_list)
        else:
            # 如果文件不存在，使用默认列表并创建文件
            save_known_false_positive_ips(DEFAULT_KNOWN_FALSE_POSITIVE_IPS)
            return set(item['ip_address'] for item in DEFAULT_KNOWN_FALSE_POSITIVE_IPS)
    except Exception as e:
        print(f"读取已知误报IP列表时发生错误: {str(e)}")
        return set(item['ip_address'] for item in DEFAULT_KNOWN_FALSE_POSITIVE_IPS)

def get_known_false_positive_list_with_details():
    """
    获取已知误报IP列表的详细信息
    返回包含IP地址、描述和类型的列表
    """
    try:
        if os.path.exists(KNOWN_FALSE_POSITIVE_IPS_FILE):
            with open(KNOWN_FALSE_POSITIVE_IPS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 如果文件不存在，使用默认列表并创建文件
            save_known_false_positive_ips(DEFAULT_KNOWN_FALSE_POSITIVE_IPS)
            return DEFAULT_KNOWN_FALSE_POSITIVE_IPS
    except Exception as e:
        print(f"读取已知误报IP列表详细信息时发生错误: {str(e)}")
        return DEFAULT_KNOWN_FALSE_POSITIVE_IPS

def save_known_false_positive_ips(ip_list):
    """
    保存已知误报IP列表到文件
    """
    try:
        with open(KNOWN_FALSE_POSITIVE_IPS_FILE, 'w', encoding='utf-8') as f:
            json.dump(ip_list, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存已知误报IP列表时发生错误: {str(e)}")
        return False

def get_trusted_source_whitelist():
    """
    获取可信来源白名单IP列表
    返回IP地址的集合
    """
    try:
        if os.path.exists(TRUSTED_SOURCE_WHITELIST_FILE):
            with open(TRUSTED_SOURCE_WHITELIST_FILE, 'r', encoding='utf-8') as f:
                ip_list = json.load(f)
                return set(item['ip_address'] for item in ip_list)
        else:
            # 如果文件不存在，使用默认列表并创建文件
            save_trusted_source_whitelist(DEFAULT_TRUSTED_SOURCE_WHITELIST)
            return set(item['ip_address'] for item in DEFAULT_TRUSTED_SOURCE_WHITELIST)
    except Exception as e:
        print(f"读取可信来源白名单时发生错误: {str(e)}")
        return set(item['ip_address'] for item in DEFAULT_TRUSTED_SOURCE_WHITELIST)

def get_trusted_source_whitelist_with_details():
    """
    获取可信来源白名单的详细信息
    返回包含IP地址、描述和类型的列表
    """
    try:
        if os.path.exists(TRUSTED_SOURCE_WHITELIST_FILE):
            with open(TRUSTED_SOURCE_WHITELIST_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 如果文件不存在，使用默认列表并创建文件
            save_trusted_source_whitelist(DEFAULT_TRUSTED_SOURCE_WHITELIST)
            return DEFAULT_TRUSTED_SOURCE_WHITELIST
    except Exception as e:
        print(f"读取可信来源白名单详细信息时发生错误: {str(e)}")
        return DEFAULT_TRUSTED_SOURCE_WHITELIST

def save_trusted_source_whitelist(ip_list):
    """
    保存可信来源白名单到文件
    """
    try:
        with open(TRUSTED_SOURCE_WHITELIST_FILE, 'w', encoding='utf-8') as f:
            json.dump(ip_list, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存可信来源白名单时发生错误: {str(e)}")
        return False

def update_trusted_source_whitelist(new_ip_addresses):
    """
    更新可信来源白名单
    参数: new_ip_addresses - 新的IP地址列表
    返回: 更新是否成功
    """
    try:
        # 获取当前列表
        current_list = get_trusted_source_whitelist_with_details()
        current_ips = set(item['ip_address'] for item in current_list)
        
        # 添加新的IP地址
        for ip in new_ip_addresses:
            if ip not in current_ips:
                current_list.append({
                    'ip_address': ip,
                    'description': f'用户添加的可信来源IP: {ip}',
                    'type': 'user_added'
                })
                current_ips.add(ip)
        
        # 保存更新后的列表
        return save_trusted_source_whitelist(current_list)
    except Exception as e:
        print(f"更新可信来源白名单时发生错误: {str(e)}")
        return False

def update_known_false_positive_ips(new_ip_addresses):
    """
    更新已知误报IP列表
    参数: new_ip_addresses - 新的IP地址列表
    返回: 更新是否成功
    """
    try:
        # 获取当前列表
        current_list = get_known_false_positive_list_with_details()
        current_ips = set(item['ip_address'] for item in current_list)
        
        # 添加新的IP地址
        for ip in new_ip_addresses:
            if ip not in current_ips:
                current_list.append({
                    'ip_address': ip,
                    'description': f'用户添加的误报IP: {ip}',
                    'type': 'user_added'
                })
                current_ips.add(ip)
        
        # 保存更新后的列表
        return save_known_false_positive_ips(current_list)
    except Exception as e:
        print(f"更新已知误报IP列表时发生错误: {str(e)}")
        return False

def delete_known_false_positive_ip(ip_address):
    """
    删除已知误报IP列表中的指定IP地址
    参数: ip_address - 要删除的IP地址
    返回: (是否成功, 删除的记录数)
    """
    try:
        # 获取当前列表
        current_list = get_known_false_positive_list_with_details()
        
        # 查找并删除指定IP
        original_count = len(current_list)
        updated_list = [item for item in current_list if item['ip_address'] != ip_address]
        deleted_count = original_count - len(updated_list)
        
        if deleted_count > 0:
            # 保存更新后的列表
            success = save_known_false_positive_ips(updated_list)
            return success, deleted_count
        else:
            # IP地址不存在于列表中
            return True, 0
    except Exception as e:
        print(f"删除已知误报IP时发生错误: {str(e)}")
        return False, 0

def delete_trusted_source_whitelist_ip(ip_address):
    """
    删除可信源白名单中的指定IP地址
    参数: ip_address - 要删除的IP地址
    返回: (是否成功, 删除的记录数)
    """
    try:
        # 获取当前列表
        current_list = get_trusted_source_whitelist_with_details()
        
        # 查找并删除指定IP
        original_count = len(current_list)
        updated_list = [item for item in current_list if item['ip_address'] != ip_address]
        deleted_count = original_count - len(updated_list)
        
        if deleted_count > 0:
            # 保存更新后的列表
            success = save_trusted_source_whitelist(updated_list)
            return success, deleted_count
        else:
            # IP地址不存在于列表中
            return True, 0
    except Exception as e:
        print(f"删除可信源白名单IP时发生错误: {str(e)}")
        return False, 0

def get_false_positive_data():
    """
    获取误报分析所需的数据
    返回工作时间分析数据和威胁评分数据
    """
    print("正在获取工作时间分析数据...")
    # 获取工作时间分析数据
    work_time_df = get_work_time_data()
    
    print("正在获取威胁评分数据...")
    # 获取威胁评分数据
    threat_score_df = generate_threat_scores(work_time_df)
    
    return work_time_df, threat_score_df

def process_false_positives(df, risk_df):
    """
    处理系统误报和已知误报IP
    """
    print("正在处理系统误报和已知误报IP...")
    
    # 合并数据以获取更多特征
    # 按小时和源IP分组获取聚合特征
    grouped = df.groupby(['hour_slot', 'srcAddress'])
    
    # 计算每个组的特征
    agg_data = []
    for (hour_slot, src_ip), group in grouped:
        # 计算工作时间占比
        work_time_ratio = group['is_work_time'].mean() if 'is_work_time' in group.columns else 0
        
        # 计算业务相关性占比
        business_related_ratio = group['is_business_related'].mean() if 'is_business_related' in group.columns else 0
        
        # 计算事件频率
        event_frequency = len(group)
        
        # 获取流向类型
        flow_directions = group['flow_direction'].unique() if 'flow_direction' in group.columns else []
        is_internal_only = all(fd == "内对内" for fd in flow_directions)
        is_internal_to_external = any(fd == "内到外" for fd in flow_directions)
        is_external_to_internal = any(fd == "外到内" for fd in flow_directions)
        is_mixed_flow = len(flow_directions) >= 2
        
        # 估计连续小时数（简化版）
        current_run_hours = 1  # 默认为1，实际应该从时间序列计算
        
        # 估计同小时同IP数量（简化版）
        hour_peer_ip_count = 1  # 默认为1，实际应该计算
        
        agg_data.append({
            'hour_slot': hour_slot,
            'srcAddress': src_ip,
            'work_time_ratio': work_time_ratio,
            'business_related_ratio': business_related_ratio,
            'event_frequency': event_frequency,
            'is_internal_only': is_internal_only,
            'is_internal_to_external': is_internal_to_external,
            'is_external_to_internal': is_external_to_internal,
            'is_mixed_flow': is_mixed_flow,
            'current_run_hours': current_run_hours,
            'hour_peer_ip_count': hour_peer_ip_count
        })
    
    # 创建聚合数据框
    agg_df = pd.DataFrame(agg_data)
    
    # 合并聚合数据到风险数据
    merged_df = pd.merge(risk_df, agg_df, on=['hour_slot', 'srcAddress'], how='left')
    
    # 填充缺失值
    merged_df = merged_df.fillna({
        'work_time_ratio': 0,
        'business_related_ratio': 0,
        'event_frequency': 1,
        'is_internal_only': False,
        'is_internal_to_external': False,
        'is_external_to_internal': False,
        'is_mixed_flow': False,
        'current_run_hours': 1,
        'hour_peer_ip_count': 1
    })
    
    # 获取可信来源白名单（使用动态白名单系统）
    trusted_sources = get_trusted_source_whitelist()
    
    # 创建系统误报标记
    # 基于规则判断可能的误报
    def determine_false_positive(row):
        # A) 仅内对内
        if row['is_internal_only']:
            # 若单次且工作时间占比高且无连续
            if row['event_frequency'] == 1 and row['work_time_ratio'] > 0.6 and row['current_run_hours'] == 1:
                return True  # "误报 - 工作时间内的单次内网事件"
            # 否则看调整分
            elif row['threat_score'] < 40:
                return True  # "误报 - 纯内网低风险流量"
            elif row['threat_score'] < 60:
                return True  # "可能误报 - 内网中等风险流量，建议复核"
        
        # B) 内到外
        elif row['is_internal_to_external'] and not row['is_external_to_internal']:
            # 若业务相关性高且工作时间为主
            if row['business_related_ratio'] > 0.7 and row['work_time_ratio'] > 0.6:
                if row['threat_score'] < 65 and row['current_run_hours'] < 3:
                    return True  # "误报 - 工作时间内的业务相关正常外联请求"
                else:
                    return True  # "可能误报 - 业务相关但分值/持续性较高，建议复核"
            # 若业务相关性高且非工作时间为主
            elif row['business_related_ratio'] > 0.7 and row['work_time_ratio'] <= 0.6:
                if not (row['event_frequency'] > 5 or row['current_run_hours'] >= 3):
                    return True  # "可能误报 - 非工作时间的业务相关请求，建议抽查"
            # 其他情况（看分）
            elif row['threat_score'] < 50:
                return True  # "低危 - 可能为监控/巡检等正常外联"
        
        # C) 外到内
        elif row['is_external_to_internal'] and not row['is_internal_to_external']:
            # 若源IP在可信来源白名单
            if row['srcAddress'] in trusted_sources:
                if row['threat_score'] < 70:
                    if row['work_time_ratio'] > 0.6:
                        return True  # "误报 - 工作时间内来自可信来源的流量"
                    else:
                        return True  # "误报 - 来自可信来源(省公司/电科院)的流量"
                else:
                    if not (row['event_frequency'] > 5 or row['current_run_hours'] >= 3):
                        return True  # "可能误报 - 可信来源但分值较高，建议复核"
            # 否则（非白名单）
            else:
                if row['threat_score'] < 60:
                    if row['event_frequency'] == 1 and row['current_run_hours'] == 1:
                        return True  # "低危 - 单次外部访问，疑似偶发，建议观察"
        
        # D) 混合流量
        elif row['is_mixed_flow']:
            if row['threat_score'] < 55:
                if row['work_time_ratio'] > 0.6 and row['event_frequency'] == 1 and row['current_run_hours'] == 1:
                    return True  # "低危 - 工作时间内单次低风险事件"
                else:
                    return True  # "低危 - 正常混合流量，可能为业务请求"
        
        # 默认不是误报
        return False
    
    # 应用误报判断规则
    merged_df['false_positive_flag'] = merged_df.apply(determine_false_positive, axis=1)
    
    # 获取已知误报IP列表（动态获取）
    known_false_positive_ips = get_known_false_positive_ips()
    
    # 将已知误报IP的记录标记为误报
    merged_df.loc[merged_df['srcAddress'].isin(known_false_positive_ips), 'false_positive_flag'] = True
    
    # 创建误报结果数据框（不保存到文件）
    fp_df = merged_df[['hour_slot', 'srcAddress', 'threat_score', 'false_positive_flag']]
    
    # 统计误报情况
    fp_counts = fp_df['false_positive_flag'].value_counts()
    print("\n误报统计:")
    print(f"误报: {fp_counts.get(True, 0)}条记录")
    print(f"非误报: {fp_counts.get(False, 0)}条记录")
    
    # 计算误报率
    false_positive_rate = fp_counts.get(True, 0) / len(fp_df) * 100 if len(fp_df) > 0 else 0
    print(f"误报率: {false_positive_rate:.2f}%")


    return fp_df, known_false_positive_ips

def main():
    # 直接获取数据，不再依赖CSV文件
    try:
        df, risk_df = get_false_positive_data()
        print("======================================================")
        print("功能完成。。。。。")
        print("======================================================")
        # 检查必需列是否存在
        required_columns = ['hour_slot', 'srcAddress']
        missing_columns = [col for col in required_columns if col not in risk_df.columns]
        if missing_columns:
            print(f"错误: 威胁分数数据缺少必需列: {missing_columns}")
            print(f"当前列: {list(risk_df.columns)}")
            return None
        
        # 处理误报
        fp_df, known_false_positive_ips = process_false_positives(df, risk_df)
        return fp_df, known_false_positive_ips
        
    except Exception as e:
        print(f"误报分析过程中发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    main()