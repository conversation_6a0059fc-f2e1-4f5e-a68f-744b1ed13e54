import pandas as pd
import os
# 导入step3模块中的函数
from step3_business_analysis import analyze_business_relevance
from step2_flow_direction import get_flow_direction_data

def mark_work_time(df):
    """
    标记工作时间
    工作日（周一~周五）08:30–17:30为工作时间
    """
    print("正在标记工作时间...")
    
    # 确保time列是datetime类型
    if not pd.api.types.is_datetime64_any_dtype(df['time']):
        df['time'] = pd.to_datetime(df['time'], errors='coerce')
    
    # 判断是否为工作时间
    def is_work_time(time):
        if pd.isna(time):
            return False
        
        # 判断是否为工作日（周一至周五）
        is_weekday = time.dayofweek < 5  # 0-4 表示周一至周五
        
        # 判断是否在工作时间段（8:30-17:30）
        hour, minute = time.hour, time.minute
        is_work_hours = (hour > 6 or (hour == 6 and minute >= 30)) and (hour < 20 or (hour == 20 and minute <= 30))
        
        return is_weekday and is_work_hours
    
    df['is_work_time'] = df['time'].apply(is_work_time)
    
    # 统计工作时间和非工作时间的记录数
    work_time_counts = df['is_work_time'].value_counts()
    print("\n工作时间统计:")
    print(f"工作时间: {work_time_counts.get(True, 0)}条记录")
    print(f"非工作时间: {work_time_counts.get(False, 0)}条记录")
    
    return df

def get_business_analysis_data():
    """
    获取业务相关性分析数据
    直接调用step3的处理逻辑，返回处理后的DataFrame
    """
    print("正在获取业务相关性分析数据...")
    
    # 调用step2获取流向分析数据
    df = get_flow_direction_data()
    if df is None:
        print("错误: 无法获取流向分析数据")
        return None
    
    print(f"成功获取 {len(df)} 条流向分析记录")
    
    # 检查必需列是否存在
    required_columns = ['srcAddress', 'destAddress', 'time', 'id', 'flow_direction']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return None
    
    # 处理时间列
    df['time'] = pd.to_datetime(df['time'], errors='coerce')
    # 丢弃时间为空的行
    df = df.dropna(subset=['time'])
    
    # 分析业务相关性
    df = analyze_business_relevance(df)
    
    return df

def main():
    # 直接调用step3的方法获取业务相关性分析数据
    df = get_business_analysis_data()
    if df is None:
        print("错误: 无法获取业务相关性分析数据")
        return
    
    print(f"成功获取 {len(df)} 条业务相关性分析记录")
    
    # 检查必需列是否存在
    required_columns = ['time', 'id']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return
    
    # 处理时间列
    df['time'] = pd.to_datetime(df['time'], errors='coerce')
    # 丢弃时间为空的行
    df = df.dropna(subset=['time'])
    
    # 标记工作时间
    df = mark_work_time(df)
    print(f"\n工作时间分析完成，共处理 {len(df)} 条记录")
    return df

if __name__ == "__main__":
    main()