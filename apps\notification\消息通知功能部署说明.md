# 消息通知功能部署说明

## 1. 文件结构确认

确保以下文件已正确创建：

```
core/view_soar/notification/
├── __init__.py
└── view.py

apps/notification/
├── app.json
├── icon.png
├── readme.md
└── main/
    ├── __init__.py
    └── run.py

消息通知功能体设计文档.md
test_notification.py
消息通知功能部署说明.md
```

## 2. 代码集成确认

### 2.1 路由注册
确认 `core/__init__.py` 中已添加：
```python
from core.view_soar.notification import r as r_notification

soar_route_list = [..., r_notification]
```

### 2.2 依赖检查
确认以下依赖已安装：
- Flask
- Redis
- loguru
- requests
- uuid (Python内置)
- json (Python内置)
- datetime (Python内置)

## 3. 配置设置

### 3.1 Redis配置
确保Redis服务正常运行，并在 `config.ini` 中正确配置：
```ini
[redis]
host = localhost
port = 6379
database = 0
password = 
```

### 3.2 TOKEN配置
APP支持两种TOKEN使用方式：

#### 方式1：用户提供TOKEN（推荐）
在剧本中配置APP时，在`token`参数中提供有效的TOKEN。

#### 方式2：自动获取系统TOKEN
如果用户不提供TOKEN，APP会自动从数据库获取系统TOKEN：
1. 使用`Users.where('id', 1).first()`从数据库获取ID为1的用户
2. 验证token在Redis中是否有效
3. 如果有效则直接使用，如果失效则自动生成新token
4. 如果获取失败，返回错误信息

### 3.3 数据库用户确认
确保数据库中存在ID为1的用户：
```sql
SELECT id, account, token FROM w5_users WHERE id = 1;
```

如果不存在，需要创建系统用户：
```sql
INSERT INTO w5_users (id, account, passwd, nick_name, email, status, create_time, update_time)
VALUES (1, 'system', MD5('system123'), '系统用户', '<EMAIL>', 0, NOW(), NOW());
```

## 4. 数据库初始化（可选）

如需持久化存储通知历史，可创建数据库表：
```sql
CREATE TABLE notification_history (
    id VARCHAR(36) PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    source VARCHAR(100) NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    count INT NOT NULL,
    status ENUM('read', 'unread') DEFAULT 'unread',
    result_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 5. 启动服务

### 5.1 启动主服务
```bash
python run.py
```

### 5.2 验证服务状态
访问 `http://localhost:8888` 确认服务正常启动。

## 6. 功能测试

### 6.1 接口测试
使用提供的测试脚本：
```bash
# 测试TOKEN获取和APP功能
python test_simple_token.py
```

### 6.2 手动测试
使用curl命令测试接口：

**测试消息处理接口：**
```bash
curl -X POST http://localhost:8888/api/v1/soar/notification/process \
-H "Content-Type: application/json" \
-H "token: W5_TOKEN_XXX" \
-d '{
  "script_type": "s60000_pending",
  "result": {"processed_count": 5}
}'
```

**测试通知列表查询：**
```bash
curl -X POST http://localhost:8888/api/v1/soar/notification/list \
-H "Content-Type: application/json" \
-H "token: W5_TOKEN_XXX" \
-d '{
  "page": 1,
  "limit": 10,
  "status": "all"
}'
```

## 7. 剧本集成

### 7.1 在现有剧本中添加通知APP
1. 在剧本编辑器中，在业务APP后添加"消息通知处理"APP
2. 配置参数：
   - `script_type`: 选择对应的剧本类型
   - `result_data`: 使用前一个APP的输出结果
   - `execution_id`: 可选，使用剧本执行ID

### 7.2 参数配置示例

#### 使用用户提供的TOKEN（推荐）
```json
{
  "script_type": "s60000_pending",
  "result_data": "{{previous_app_output}}",
  "execution_id": "{{workflow_execution_id}}",
  "token": "{{user_token}}"
}
```

#### 使用系统自动获取的TOKEN
```json
{
  "script_type": "s60000_pending",
  "result_data": "{{previous_app_output}}",
  "execution_id": "{{workflow_execution_id}}"
}
```

## 8. 前端集成（待开发）

### 8.1 WebSocket监听
前端需要监听通知事件：
```javascript
socket.on('notification', function(data) {
    showNotificationPopup(data);
});
```

### 8.2 通知弹窗组件
实现通知弹窗组件，包含：
- 通知标题显示
- 点击查看详情
- 标记已读功能
- 生成对话入口

## 9. 监控和日志

### 9.1 日志查看
通知相关日志会输出到系统日志中，可通过以下方式查看：
```bash
# 查看应用日志
tail -f logs/app.log | grep "消息通知"

# 查看错误日志
tail -f logs/error.log | grep "notification"
```

### 9.2 Redis监控
监控Redis中的通知数据：
```bash
# 连接Redis
redis-cli

# 查看用户通知队列
LLEN notification:queue:USER_ID

# 查看用户未读计数
GET notification:unread:USER_ID

# 查看通知详情
HGETALL notification:detail:NOTIFICATION_ID
```

## 10. 故障排除

### 10.1 常见问题

**问题1：接口返回TOKEN失效**
- 检查TOKEN是否正确
- 检查Redis中TOKEN是否存在
- 确认TOKEN未过期

**问题2：APP调用失败**
- 检查网络连接
- 确认服务端口正确
- 检查系统TOKEN配置

**问题3：通知未生成**
- 检查判断条件是否满足
- 确认输入数据格式正确
- 查看日志中的错误信息

### 10.2 调试方法
1. 启用详细日志输出
2. 使用测试脚本验证功能
3. 检查Redis数据状态
4. 查看网络请求日志

## 11. 性能优化建议

### 11.1 Redis优化
- 设置合理的过期时间
- 定期清理过期数据
- 监控内存使用情况

### 11.2 接口优化
- 添加请求频率限制
- 实现批量处理功能
- 优化数据库查询

### 11.3 扩展性考虑
- 支持分布式部署
- 实现消息队列机制
- 添加缓存层

## 12. 安全注意事项

### 12.1 权限控制
- 确保TOKEN验证正确
- 实现用户权限检查
- 防止越权访问

### 12.2 数据安全
- 敏感数据加密存储
- 实现数据备份机制
- 定期安全审计

这个部署说明提供了完整的部署和运维指导，确保消息通知功能能够正确集成到现有系统中。
