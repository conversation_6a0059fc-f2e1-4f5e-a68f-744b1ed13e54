#!/usr/bin/env python
# encoding:utf-8

import argparse
import time
import os
import json
import sys
from loguru import logger
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from datetime import datetime, timedelta

# --- 全局配置区域 ---

# --- 新版标准化输出JSON格式说明 ---
# 每个任务函数返回的列表中，每个字典都遵循以下结构。
# 未被任务使用的字段将以空字符串 "" 作为默认值填充。
# {
#     "info_code": "",             # 编号 (任务 1,2,3,5) - 表格
#     "title": "",                 # 标题 (所有任务) - 表格
#     "publish_time": "",          # 发布时间 (所有任务) - 表格
#     "end_time": "",              # 截止时间 (任务 2,3) - 表格
#     "state": "",                 # 状态 (所有任务) - 表格
#     "vulnlevel": "",             # 风险等级 (任务 4) - 表格
#     "feedback": "",              # 反馈信息 (任务 4) - 表格
#     "feedbackendtime": "",       # 排查反馈时间 (任务 4) - 表格
#     "toBeCorrectedTime": "",     # 应整改反馈时间 (任务 4) - 表格
#     "dealstate": "",             # 销号情况 (任务 4) - 表格
#     "file_path": "",             # 附件 (所有任务) - 下载获得
#     "detail_title": "",          # 查看.标题 (所有任务) - 弹窗
#     "detail_context": "",        # 查看.情报内容 (任务 1,4,5) - 弹窗
#     "detail_create_time": "",    # 查看.录入时间 (任务 1,2,3) - 弹窗
#     "detail_vulnlevel": "",      # 查看.风险等级 (任务 4) - 弹窗
#     "detail_author": "",         # 查看.下发人员 (任务 4) - 弹窗
#     "detail_recordtime": "",     # 查看.下发时间 (任务 4) - 弹窗
#     "detail_contactnumber": "",  # 查看.录入人联系方式 (任务 4) - 弹窗
#     "detail_feedbackendtime": "",# 查看.反馈截止时间 (任务 4) - 弹窗
#     "detail_feedbackrequire": "",# 查看.反馈要求 (任务 4) - 弹窗
#     "detail_Handleadvice": "",   # 查看.处置建议 (任务 4) - 弹窗
#     "detail_vulnproduct": ""     # 查看.预警范围 (任务 4) - 弹窗
# }

# --- S6000系统登录与导航配置 ---
LOGIN_USERNAME_XPATH = "//input[@name='username']"
LOGIN_PASSWORD_XPATH = "//input[@name='password']"
LOGIN_BUTTON_XPATH = "//input[@id='submit_login']"
CONTENT_IFRAME_XPATH = "//div[@id='prefecture_City_linkage']/iframe"
YJD_IFRAME_XPATH = "//div[@id='area_vulnfeedback']/iframe"
YJTG_IFRAME_XPATH = "//div[@id='ws-announce-jump_area']/iframe"
MENU_YJXY_XPATH = "//li[div/span[contains(text(), '应急响应')]]"
MENU_QSYJ_XPATH = "//li[div/span[contains(text(), '全省预警')]]"
MENU_ZGDGL_XPATH = "//li[span[contains(text(), '预警单反馈')]]"
MENU_QSYJTG_XPATH = "//li[div/span[contains(text(), '全省预警通告')]]"
MENU_DSYJTG_XPATH = "//li[span[contains(text(), '地市预警通告')]]"
NEXT_PAGE_BUTTON_XPATH = "//button[contains(@class, 'btn-next') and not(@disabled)]"

# --- 表格字段 XPATH ---
# 提醒: 需要抓取的每个新字段提供准确的XPATH
# 格式为: .//td[列号]/div 或 .//td[列号]//span 等
TABLE_INFO_CODE_XPATH = ".//td[2]//div"         # 预留: 表格中的“编号”
TABLE_PUBLISH_TIME_XPATH = ".//td[5]//div"      # 预留: 表格中的“发布时间”
TABLE_END_TIME_XPATH = ".//td[6]//div"          # 预留: 表格中的“截止时间”



# --- 任务 1,2,3 (工作通知/任务/联系单) XPATH ---
NOTICE_TABLE_BODY_XPATH = "//table[contains(@class, 'el-table__body')]/tbody"
NOTICE_TITLE_XPATH = ".//td[3]//div[contains(@class, 'newsTitle')]"
NOTICE_PROCESSED_ICON_XPATH = ".//td[6]//i[contains(@class, 'el-icon-success')]" # 任务1专用
NOTICE_VIEW_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '查看')]]"
NOTICE_DOWNLOAD_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '下载')]]"
TASK_STATUS_TEXT_XPATH = ".//td[7]/div" # 任务2,3专用
TASK_VIEW_BUTTON_XPATH = ".//td[8]//button[span[contains(text(), '查看')]]" # 任务2,3专用
TASK_DOWNLOAD_BUTTON_XPATH = ".//td[8]//button[span[contains(text(), '下载')]]" # 任务2,3专用
POPUP_ATTACHMENT_INDICATOR_XPATH = "//*[@id='viewDialog']/div/div/div[2]/form/div[5]/div/div/label"
POPUP_DETAIL_TITLE_XPATH = "//*[@id='viewDialog']/div/div/div[2]/form/div[2]/div/div/div/div/input" # 预留: 任务1,2,3,5弹窗标题
POPUP_DETAIL_CONTEXT_XPATH = "//*[@id='renderContentHtml']" #预留：任务1,2,3正文内容
POPUP_DETAIL_CREATE_TIME_XPATH = "//*[@id='viewDialog']//div[contains(text(), '录入时间')]/following-sibling::div" # 预留: 任务1,2,3弹窗录入时间
TASK_TAB_XPATH = "//*[@id='tab-2']" # 工作任务Tab
CONTACT_TAB_XPATH = "//*[@id='tab-3']" # 工作联系单Tab

# --- 任务 4 (预警单管理) XPATH ---
YJD_TABLE_BODY_XPATH = "//table[contains(@class, 'el-table__body')]/tbody"
TABLE_YJD_PUBLISH_TIME_XPATH = ".//td[8]//div"  # 预留: 任务4表格中的“发布时间”
TABLE_VULNLEVEL_XPATH = ".//td[5]//div"         # 预留: 任务4表格中的“风险等级”
TABLE_FEEDBACK_XPATH = ".//td[6]//div"         # 预留: 任务4表格中的“反馈信息”
TABLE_FEEDBACKENDTIME_XPATH = ".//td[9]//div"   # 预留: 任务4表格中的“排查反馈时间”
TABLE_TOBECORRECTEDTIME_XPATH = ".//td[10]//div" # 预留: 任务4表格中的“应整改反馈时间”
TABLE_DEALSTATE_XPATH = ".//td[12]//div"        # 预留: 任务4表格中的“销号情况”
YJD_TITLE_XPATH = ".//td[3]//span"
YJD_STATE_XPATH = ".//td[11]//span"
YJD_VIEW_BUTTON_XPATH = ".//td[14]//button[contains(span, '查看')]"
POPUP_YJD_DETAIL_CONTEXT_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[6]/div/div/div/div/textarea"
POPUP_YJD_ATTACHMENT_LINK_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[9]/div/div/div/div/a"
POPUP_YJD_DETAIL_TITLE_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[1]/div[2]/div/div/div/input" # 预留: 任务4弹窗标题
POPUP_YJD_DETAIL_AUTHOR_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[3]/div[1]/div/div/div/input" # 预留
POPUP_YJD_DETAIL_RECORDTIME_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[3]/div[2]/div/div/div/input" # 预留
POPUP_YJD_DETAIL_CONTACTNUMBER_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[4]/div[1]/div/div/div/input" # 预留
POPUP_YJD_DETAIL_FEEDBACKREQUIRE_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[5]/div/div/div/div/textarea" # 预留
POPUP_YJD_DETAIL_HANDLEADVICE_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[7]/div/div/div/div/textarea" # 预留
POPUP_YJD_DETAIL_VULNPRODUCT_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[8]/div/div/div/div/textarea" # 预留

# --- 任务 5 (地市预警通告) XPATH ---
YJTG_TABLE_BODY_XPATH = "//table[contains(@class, 'el-table__body')]/tbody"
YJTG_PROCESSED_ICON_XPATH = ".//td[6]//i[contains(@class, 'el-icon-success')]"
YJTG_INFO_CODE_XPATH = ".//td[1]//div"
YJTG_TITLE_XPATH = ".//td[2]//div[contains(@class, 'newsTitle')]"
YJTG_PUBLISH_TIME_XPATH = ".//td[4]//div"
YJTG_VIEW_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '查看')]]"
YJTG_DOWNLOAD_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '下载')]]"

S6000_USERNAME = "qinsihang"
S6000_PASSWORD = "Hbdl@0529"
S6000_URL = "http://10.228.255.88:28088/Portal/mainAdmin/main"

# --- 辅助函数 ---

def get_new_item_template():
    """返回一个包含所有标准字段的空字典模板"""
    return {
        "info_code": "", "title": "", "publish_time": "", "end_time": "", "state": "",
        "vulnlevel": "", "feedback": "", "feedbackendtime": "", "toBeCorrectedTime": "", "dealstate": "",
        "file_path": "", "detail_title": "", "detail_context": "", "detail_create_time": "",
        "detail_vulnlevel": "", "detail_author": "", "detail_recordtime": "", "detail_contactnumber": "",
        "detail_feedbackendtime": "", "detail_feedbackrequire": "", "detail_Handleadvice": "", "detail_vulnproduct": ""
    }

def safe_get_text(element: WebElement, xpath: str):
    """安全地从元素中根据XPATH获取文本，失败则返回空字符串"""
    try:
        return element.find_element(By.XPATH, xpath).text.strip().replace('\xa0',' ')
    except NoSuchElementException:
        return ""

def safe_get_attribute(element: WebElement, xpath: str, attribute: str):
    """安全地从元素中根据XPATH获取属性值，失败则返回空字符串"""
    try:
        return element.find_element(By.XPATH, xpath).get_attribute(attribute).strip().replace('\xa0',' ')
    except NoSuchElementException:
        return ""

def is_within_deadline_window(deadline_str: str) -> bool:
    """
    检查当前时间是否位于截止时间的特定提醒窗口内，或已超过截止时间。
    提醒窗口为：7天, 5天, 3天, 12小时, 3小时, 1小时 (每个时间点前后1小时内)。

    :param deadline_str: 从网页上获取的截止时间字符串 (例如 "2025-08-10 15:30:00")。
    :return: 如果在窗口内或已过期，返回True，否则返回False。
    """
    if not deadline_str:
        return False
    
    try:
        deadline_dt = datetime.strptime(deadline_str, "%Y-%m-%d %H:%M:%S")
        now_dt = datetime.now()

        # 如果当前时间已超过截止时间，同样需要提醒
        # 测试时注释3行
        #if now_dt > deadline_dt:
        #    logger.info(f"截止时间 '{deadline_str}' 已过期，需要提醒。")
         #   return True

        # 计算剩余时间（以小时为单位）
        # 测试时取负值(-1)*
        time_remaining_hours = (-1) *(deadline_dt - now_dt).total_seconds() / 3600

        # 定义提醒的时间点（单位：小时）
        # 测试时自行调整窗口
        checkpoints_in_hours = [7 * 24, 5 * 24, 3 * 24, 12, 3, 1,2*24]

        # 检查是否落在任何一个时间点的±1小时窗口内
        for checkpoint in checkpoints_in_hours:
            lower_bound = checkpoint - 1
            upper_bound = checkpoint + 1
            if lower_bound <= time_remaining_hours <= upper_bound:
                logger.info(f"截止时间 '{deadline_str}' 满足 {checkpoint} 小时提醒窗口，剩余 {time_remaining_hours:.2f} 小时。")
                return True
        
        return False
    except ValueError:
        logger.warning(f"无法解析时间字符串: '{deadline_str}'")
        return False

def wait_for_download_complete(directory, initial_states, start_timeout=15, finish_timeout=120):
    """
    【三重监控】能处理慢速、快速和瞬时覆盖下载。

    :param directory: 下载目录。
    :param initial_states: 一个字典，记录了下载前 {文件名: 修改时间} 的状态。
    :param start_timeout: 等待下载开始的最长时间（秒）。
    :param finish_timeout: 等待下载完成的最长时间（秒）。
    :return: 下载成功则返回True，否则返回False。
    """
    # --- 第一阶段: 等待下载开始 (使用三重监控) ---
    logger.info(f"监控下载开始... (最长等待 {start_timeout} 秒)")
    start_time = time.time()
    download_started = False
    while time.time() - start_time < start_timeout:
        # 监控点1: 检查是否有 .crdownload 文件出现 (适用于慢速/普通下载)
        current_filenames_in_dir = os.listdir(directory)
        if any(f.endswith('.crdownload') for f in current_filenames_in_dir):
            logger.info("检测到 .crdownload 文件，下载已开始！")
            download_started = True
            break
        
        # 监控点2 & 3: 检查是否有新文件或文件被覆盖 (适用于快速/瞬时下载)
        # 这可以避免因文件太小、下载太快而来不及生成.crdownload文件的情况
        for filename in current_filenames_in_dir:
            if filename.endswith(('.crdownload', '.tmp')):
                continue
            current_path = os.path.join(directory, filename)
            try:
                current_mtime = os.path.getmtime(current_path)
                # 如果是新文件，或者现有文件的修改时间变了，都算下载开始
                if filename not in initial_states or current_mtime > initial_states.get(filename, 0):
                    logger.info(f"检测到新文件或文件被覆盖 ('{filename}')，下载已开始！")
                    download_started = True
                    break # 跳出 for 循环
            except FileNotFoundError:
                continue # 文件在列出和检查之间被删除，忽略

        if download_started:
            break # 跳出 while 循环
        
        time.sleep(0.5)

    if not download_started:
        logger.error(f"在 {start_timeout} 秒内未检测到任何下载活动。")
        return False

    # --- 第二阶段: 等待下载完成 (此部分逻辑不变) ---
    logger.info(f"监控下载完成... (最长等待 {finish_timeout} 秒)")
    finish_start_time = time.time()
    while time.time() - finish_start_time < finish_timeout:
        if not any(f.endswith('.crdownload') for f in os.listdir(directory)):
            logger.info("所有 .crdownload 文件已消失，下载完成。")
            time.sleep(2)
            return True
        time.sleep(1)

    logger.error(f"在 {finish_timeout} 秒内下载未完成。")
    return False

def find_newly_downloaded_file(directory, initial_states):
    """
    【新版】通过比较文件修改时间来查找最新下载的文件。
    这能正确处理文件覆盖（overwrite）的情况。
    
    :param directory: 下载目录。
    :param initial_states: 一个字典，记录了下载前 {文件名: 修改时间} 的状态。
    :return: 最新下载文件的完整路径，如果找不到则返回空字符串。
    """
    time.sleep(1) # 等待文件系统响应
    
    changed_files = []
    try:
        # 遍历当前目录中的所有文件
        for filename in os.listdir(directory):
            # 忽略临时下载文件
            if filename.endswith(('.crdownload', '.tmp')):
                continue

            current_path = os.path.join(directory, filename)
            current_mtime = os.path.getmtime(current_path)

            # 检查文件是否是新文件，或者是否被更新过
            if filename not in initial_states or current_mtime > initial_states.get(filename, 0):
                changed_files.append((current_path, current_mtime))
        
        if not changed_files:
            logger.warning("未能检测到任何新下载或被覆盖的文件。")
            return ""
        
        # 在所有变化的文件中，返回修改时间最新的那一个
        latest_file_path, _ = max(changed_files, key=lambda item: item[1])
        logger.info(f"成功定位到最新下载的文件: {os.path.basename(latest_file_path)}")
        return latest_file_path

    except FileNotFoundError:
        logger.warning(f"下载目录 '{directory}' 查找失败，可能已被删除。")
        return ""

def login(driver, wait, url, username, password):
    """负责登录S6000系统"""
    try:
        logger.info(f"[S6000系统] 访问登录页面: {url}")
        driver.get(url)
        wait.until(EC.visibility_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH))).send_keys(username)
        driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH).send_keys(password)
        driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH).click()
        wait.until(EC.presence_of_element_located((By.XPATH, CONTENT_IFRAME_XPATH)))
        logger.info("[S6000系统] 登录成功。")
        return True
    except Exception as e:
        logger.error(f"[S6000系统] 登录过程中发生错误: {e}", exc_info=True)
        return False

def navigate_to_warning_notices(driver, wait, k):
    """导航到“预警单管理”或“地市预警通告”页面。"""
    try:
        actions = ActionChains(driver)
        emergency_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_YJXY_XPATH)))
        actions.move_to_element(emergency_menu).perform()
        time.sleep(0.5)
        
        if k == 4:
            warning_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_QSYJ_XPATH)))
            actions.move_to_element(warning_menu).perform()
            time.sleep(0.5)
            wait.until(EC.element_to_be_clickable((By.XPATH, MENU_ZGDGL_XPATH))).click()
            logger.info("已导航到'预警单管理'页面。")
            return True
        elif k == 5:
            warning_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_QSYJTG_XPATH)))
            actions.move_to_element(warning_menu).perform()
            time.sleep(0.5)
            wait.until(EC.element_to_be_clickable((By.XPATH, MENU_DSYJTG_XPATH))).click()
            logger.info("已导航到'地市预警通告'页面。")
            return True
        return False
    except Exception as e:
        logger.error(f"导航时出错: {e}", exc_info=True)
        return False

# --- 核心业务函数 (已重构) ---

def process_work_notices(driver, wait, download_dir):
    """【任务一：工作通知】"""
    processed_items = []
    logger.info("--- 开始处理'工作通知'模块 ---")
    try:
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        while True:
            time.sleep(1.5)
            rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")))
            for i in range(len(rows)):
                item_data = get_new_item_template()
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    # 检查是否已处理，如果是则终止
                    current_row.find_element(By.XPATH, NOTICE_PROCESSED_ICON_XPATH)
                    logger.info("发现已处理通知，模块处理完毕。")
                    driver.switch_to.default_content()
                    return processed_items
                    # 测试时注释上下两行，使用三条数据
                except NoSuchElementException:
                    # 从表格中提取信息
                    item_data['info_code'] = safe_get_text(current_row, TABLE_INFO_CODE_XPATH)
                    item_data['title'] = safe_get_text(current_row, NOTICE_TITLE_XPATH)
                    item_data['publish_time'] = safe_get_text(current_row, TABLE_PUBLISH_TIME_XPATH)
                    item_data['state'] = "待处理"

                    # 点击“查看”按钮，提取弹窗信息
                    current_row.find_element(By.XPATH, NOTICE_VIEW_BUTTON_XPATH).click()
                    time.sleep(1)
                    item_data['detail_title'] = safe_get_attribute(driver, POPUP_DETAIL_TITLE_XPATH, 'value')
                    item_data['detail_context'] = safe_get_attribute(driver, POPUP_DETAIL_CONTEXT_XPATH, 'textContent')
                    item_data['detail_create_time'] = item_data['publish_time']
                    
                    has_attachment = bool(driver.find_elements(By.XPATH, POPUP_ATTACHMENT_INDICATOR_XPATH))
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)

                    # 如果有附件，则下载
                    if has_attachment:
                        # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                        initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                        current_row.find_element(By.XPATH, NOTICE_DOWNLOAD_BUTTON_XPATH).click()
                        if wait_for_download_complete(download_dir, initial_states_dict):
                        # 调用新函数
                            item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                        logger.info(f"处理: '{item_data['title']}', 已下载附件。")
                    else:
                        logger.info(f"处理: '{item_data['title']}', 无附件。")
                        
                    processed_items.append(item_data)
                    # 测试
                    #if len(processed_items) >= 3:
                    #    driver.switch_to.default_content()
                    #    return processed_items          
                #except NoSuchElementException:
                    #continue
            # 翻页
            try:
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
            except NoSuchElementException:
                logger.info("已是最后一页。")
                break
    finally:
        driver.switch_to.default_content()
    return processed_items

def process_task_common(driver, wait, download_dir, task_name, tab_xpath):
    """通用处理逻辑，用于工作任务(2)和工作联系单(3)"""
    processed_items = []
    logger.info(f"--- 开始处理'{task_name}'模块 ---")
    try:
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        driver.find_element(By.XPATH, tab_xpath).click()
        time.sleep(3)

        while True:
            time.sleep(1.5)
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")
            for i in range(len(rows)):
                try:
                    item_data = get_new_item_template()
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")

                    # 提取状态并判断是否需要终止
                    status_text = current_row.find_element(By.XPATH, TASK_STATUS_TEXT_XPATH).text.strip()
                    # 测试时!=，功能==
                    if status_text != "已反馈":
                        logger.info(f"发现已反馈项，'{task_name}'模块处理完毕。")
                        driver.switch_to.default_content()
                        return processed_items

                    item_data['state'] = status_text
                    # 从表格提取信息
                    item_data['info_code'] = safe_get_text(current_row, TABLE_INFO_CODE_XPATH)
                    item_data['title'] = safe_get_text(current_row, NOTICE_TITLE_XPATH)
                    item_data['publish_time'] = safe_get_text(current_row, TABLE_PUBLISH_TIME_XPATH)
                    item_data['end_time'] = safe_get_text(current_row, TABLE_END_TIME_XPATH)

                    # 点击查看获取弹窗信息
                    current_row.find_element(By.XPATH, TASK_VIEW_BUTTON_XPATH).click()
                    time.sleep(1)
                    item_data['detail_title'] =safe_get_attribute(driver, POPUP_DETAIL_TITLE_XPATH, 'value')
                    item_data['detail_context'] = safe_get_attribute(driver, POPUP_DETAIL_CONTEXT_XPATH, 'textContent')
                    item_data['detail_create_time'] = item_data['publish_time']
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)
                
                    # 下载附件
                    # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                    initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                    current_row.find_element(By.XPATH, TASK_DOWNLOAD_BUTTON_XPATH).click()
                    if wait_for_download_complete(download_dir, initial_states_dict):
                        item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                    logger.info(f"处理: '{item_data['title']}', 已下载附件。")
                    
                    processed_items.append(item_data)
                
                    # 测试3行
                    if len(processed_items) >= 2:
                        driver.switch_to.default_content()
                        return processed_items 
                        
                except NoSuchElementException:
                    continue
            # 翻页
            try:
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
            except NoSuchElementException:
                logger.info("已是最后一页。")
                break
    except Exception as e:
        logger.error(f"处理工作任务时发生错误: {e}", exc_info=True)
    finally:
        driver.switch_to.default_content()
    return processed_items

def process_work_tasks(driver, wait, download_dir):
    return process_task_common(driver, wait, download_dir, "工作任务", TASK_TAB_XPATH)

def process_work_contact_sheets(driver, wait, download_dir):
    return process_task_common(driver, wait, download_dir, "工作联系单", CONTACT_TAB_XPATH)

def process_task_warning_notices(driver, wait, download_dir):
    """【任务四：预警单管理】"""
    processed_items = []
    logger.info("--- 开始处理'预警单管理'模块 ---")
    if not navigate_to_warning_notices(driver, wait, k=4): return processed_items
    try:
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, YJD_IFRAME_XPATH)))
        rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, f"{YJD_TABLE_BODY_XPATH}/tr")))
        for i in range(len(rows)):
            item_data = get_new_item_template()
            current_row = driver.find_element(By.XPATH, f"({YJD_TABLE_BODY_XPATH}/tr)[{i+1}]")
            
            item_data['state'] = safe_get_text(current_row, YJD_STATE_XPATH)
            if item_data['state'] == "已完成":
                logger.info(f"发现已完成项，'{task_name}'模块处理完毕。")
                driver.switch_to.default_content()
                return processed_items

            # 从表格提取信息
            item_data['title'] = safe_get_text(current_row, YJD_TITLE_XPATH)
            item_data['publish_time'] = safe_get_text(current_row, TABLE_YJD_PUBLISH_TIME_XPATH)
            item_data['vulnlevel'] = safe_get_text(current_row, TABLE_VULNLEVEL_XPATH)
            item_data['feedback'] = safe_get_text(current_row, TABLE_FEEDBACK_XPATH)
            item_data['feedbackendtime'] = safe_get_text(current_row, TABLE_FEEDBACKENDTIME_XPATH)
            item_data['toBeCorrectedTime'] = safe_get_text(current_row, TABLE_TOBECORRECTEDTIME_XPATH)
            item_data['dealstate'] = safe_get_text(current_row, TABLE_DEALSTATE_XPATH)

            # 点击查看，提取弹窗信息
            current_row.find_element(By.XPATH, YJD_VIEW_BUTTON_XPATH).click()
            time.sleep(1)
            item_data['detail_context'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_CONTEXT_XPATH, 'value')
            item_data['detail_title'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_TITLE_XPATH, 'value')
            item_data['detail_vulnlevel'] = item_data['vulnlevel'] # 值与表格一致
            item_data['detail_feedbackendtime'] = item_data['feedbackendtime'] # 值与表格一致
            # 提取其他弹窗信息
            item_data['detail_author'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_AUTHOR_XPATH, 'value')
            item_data['detail_recordtime'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_RECORDTIME_XPATH, 'value')
            item_data['detail_contactnumber'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_CONTACTNUMBER_XPATH, 'value')
            item_data['detail_feedbackrequire'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_FEEDBACKREQUIRE_XPATH, 'value')
            item_data['detail_Handleadvice'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_HANDLEADVICE_XPATH, 'value')
            item_data['detail_vulnproduct'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_VULNPRODUCT_XPATH, 'value')
            
            # 提取附件
            try:
                attachment_link = wait.until(EC.element_to_be_clickable((By.XPATH, POPUP_YJD_ATTACHMENT_LINK_XPATH)))
                # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                attachment_link.click()
                time.sleep(1)
                if wait_for_download_complete(download_dir, initial_states_dict):
                    item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                logger.info(f"处理: '{item_data['title'][:30]}...', 已下载附件。")
            except TimeoutException:
                logger.info(f"处理: '{item_data['title'][:30]}...', 无附件。")
        
            ActionChains(driver).send_keys(Keys.ESCAPE).perform()
            time.sleep(1)
            processed_items.append(item_data)
            
            # 测试
            #if len(processed_items) >= 3:
            #    driver.switch_to.default_content()
            #    return processed_items 
    finally:
        driver.switch_to.default_content()
    return processed_items

def process_task_alert_announcement(driver, wait, download_dir):
    """【任务五：地市预警通告】"""
    processed_items = []
    logger.info("--- 开始处理'地市预警通告'模块 ---")
    if not navigate_to_warning_notices(driver, wait, k=5): return processed_items
    try:
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, YJTG_IFRAME_XPATH)))
        while True:
            time.sleep(1.5)
            rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, f"{YJTG_TABLE_BODY_XPATH}/tr")))
            for i in range(len(rows)):
                item_data = get_new_item_template()
                try:
                    current_row = driver.find_element(By.XPATH, f"({YJTG_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    # 检查是否已处理，如果是则终止
                    current_row.find_element(By.XPATH, YJTG_PROCESSED_ICON_XPATH)
                    logger.info("发现已处理通告，模块处理完毕。")
                    driver.switch_to.default_content()
                    return processed_items
                except NoSuchElementException:
                    # 从表格提取信息
                    item_data['info_code'] = safe_get_text(current_row, YJTG_INFO_CODE_XPATH)
                    item_data['title'] = safe_get_text(current_row, YJTG_TITLE_XPATH)
                    item_data['publish_time'] = safe_get_text(current_row, YJTG_PUBLISH_TIME_XPATH)
                    item_data['state'] = "待处理"

                    # 点击查看，提取弹窗信息
                    current_row.find_element(By.XPATH, YJTG_VIEW_BUTTON_XPATH).click()
                    time.sleep(1)
                    item_data['detail_title'] = safe_get_attribute(driver, POPUP_DETAIL_TITLE_XPATH, 'value')
                    item_data['detail_context'] = safe_get_attribute(driver, POPUP_DETAIL_CONTEXT_XPATH, 'textContent')
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)
                    
                    # 下载附件
                    # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                    initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                    current_row.find_element(By.XPATH, YJTG_DOWNLOAD_BUTTON_XPATH).click()
                    if wait_for_download_complete(download_dir, initial_states_dict):
                        item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                    logger.info(f"处理: '{item_data['title']}', 已下载附件。")
                    processed_items.append(item_data)
            try:
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
            except NoSuchElementException:
                logger.info("已是最后一页。")
                break
    finally:
        driver.switch_to.default_content()
    return processed_items

def process_deadline_check(driver, wait, download_dir):
    """【任务六：截止时间检测 - 已调整】"""
    logger.info("--- 开始执行'截止时间检测'模块 (仅处理当前页) ---")
    
    final_results = {
        "工作任务": [],
        "工作联系单": [],
        "预警单管理": []
    }
    
    # --- Part 1 & 2: 检查工作任务和工作联系单 ---
    try:
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        
        for task_name, tab_xpath, deadline_xpath in [
            ("工作任务", TASK_TAB_XPATH, TABLE_END_TIME_XPATH), 
            ("工作联系单", CONTACT_TAB_XPATH, TABLE_END_TIME_XPATH)
        ]:
            logger.info(f"开始检测模块: {task_name}")
            driver.find_element(By.XPATH, tab_xpath).click()
            time.sleep(3)
            
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                
                    status_text = current_row.find_element(By.XPATH, TASK_STATUS_TEXT_XPATH).text.strip()
                    # --- 【核心修改点】---
                    # 条件1：如果状态是“已反馈”，则立即结束对当前模块的检测
                    # 测试时!=，功能==
                    if status_text != "已反馈":
                        logger.info(f"发现已反馈条目，结束对 {task_name} 模块的检测。")
                        break # 退出 for 循环

                    deadline_str = safe_get_text(current_row, TABLE_END_TIME_XPATH)
                    # 条件2：检查时间是否满足要求
                    if not is_within_deadline_window(deadline_str):
                        continue # 如果时间不满足，则跳过此条目

                    # --- 如果两个条件都满足，则执行完整的抓取操作 ---
                    title = safe_get_text(current_row, NOTICE_TITLE_XPATH)
                    logger.info(f"发现满足条件的条目: '{title}'")
                    item_data = get_new_item_template()
                
                    item_data['state'] = status_text
                    # 从表格提取信息
                    item_data['info_code'] = safe_get_text(current_row, TABLE_INFO_CODE_XPATH)
                    item_data['title'] = title
                    item_data['publish_time'] = safe_get_text(current_row, TABLE_PUBLISH_TIME_XPATH)
                    item_data['end_time'] = deadline_str

                    # 点击查看获取弹窗信息
                    current_row.find_element(By.XPATH, TASK_VIEW_BUTTON_XPATH).click()
                    time.sleep(1)
                    item_data['detail_title'] =safe_get_attribute(driver, POPUP_DETAIL_TITLE_XPATH, 'value')
                    item_data['detail_context'] = safe_get_attribute(driver, POPUP_DETAIL_CONTEXT_XPATH, 'textContent')
                    item_data['detail_create_time'] = item_data['publish_time']
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)
               
                    # 下载附件
                    # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                    initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                    current_row.find_element(By.XPATH, TASK_DOWNLOAD_BUTTON_XPATH).click()
                    if wait_for_download_complete(download_dir, initial_states_dict):
                        item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                    logger.info(f"处理: '{item_data['title']}', 已下载附件。")
                
                    final_results[task_name].append(item_data)
                
                except NoSuchElementException:
                    continue
    
    finally:
        driver.switch_to.default_content()

    # --- Part 3: 检查预警单管理 ---
    if navigate_to_warning_notices(driver, wait, k=4):
        try:
            wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, YJD_IFRAME_XPATH)))
            logger.info("开始检测模块: 预警单管理")
            
            wait.until(EC.presence_of_element_located((By.XPATH, YJD_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{YJD_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                current_row = driver.find_element(By.XPATH, f"({YJD_TABLE_BODY_XPATH}/tr)[{i+1}]")
                
                state_text = safe_get_text(current_row, YJD_STATE_XPATH)
                # --- 【核心修改点】---
                # 条件1：如果状态是“已完成”，则立即结束检测
                if state_text == "已完成":
                    logger.info("发现已完成条目，结束对 预警单管理 模块的检测。")
                    break # 退出 for 循环

                deadline_str = safe_get_text(current_row, TABLE_TOBECORRECTEDTIME_XPATH)
                # 条件2：检查时间是否满足要求
                if not is_within_deadline_window(deadline_str):
                    continue

                # --- 如果两个条件都满足，则执行完整的抓取操作 ---
                title = safe_get_text(current_row, YJD_TITLE_XPATH)
                logger.info(f"发现满足条件的条目: '{title}'")
                item_data = get_new_item_template()


                item_data['state'] = state_text
                item_data['title'] = title
                item_data['toBeCorrectedTime'] = deadline_str
                item_data['publish_time'] = safe_get_text(current_row, TABLE_YJD_PUBLISH_TIME_XPATH)
                item_data['vulnlevel'] = safe_get_text(current_row, TABLE_VULNLEVEL_XPATH)
                item_data['feedback'] = safe_get_text(current_row, TABLE_FEEDBACK_XPATH)
                item_data['feedbackendtime'] = safe_get_text(current_row, TABLE_FEEDBACKENDTIME_XPATH)
                item_data['dealstate'] = safe_get_text(current_row, TABLE_DEALSTATE_XPATH)

                # 点击查看，提取弹窗信息
                current_row.find_element(By.XPATH, YJD_VIEW_BUTTON_XPATH).click()
                time.sleep(1)
                item_data['detail_context'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_CONTEXT_XPATH, 'value')
                item_data['detail_title'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_TITLE_XPATH, 'value')
                item_data['detail_vulnlevel'] = item_data['vulnlevel'] # 值与表格一致
                item_data['detail_feedbackendtime'] = item_data['feedbackendtime'] # 值与表格一致
                # 提取其他弹窗信息
                item_data['detail_author'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_AUTHOR_XPATH, 'value')
                item_data['detail_recordtime'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_RECORDTIME_XPATH, 'value')
                item_data['detail_contactnumber'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_CONTACTNUMBER_XPATH, 'value')
                item_data['detail_feedbackrequire'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_FEEDBACKREQUIRE_XPATH, 'value')
                item_data['detail_Handleadvice'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_HANDLEADVICE_XPATH, 'value')
                item_data['detail_vulnproduct'] = safe_get_attribute(driver, POPUP_YJD_DETAIL_VULNPRODUCT_XPATH, 'value')
            
                # 提取附件
                try:
                    attachment_link = wait.until(EC.element_to_be_clickable((By.XPATH, POPUP_YJD_ATTACHMENT_LINK_XPATH)))
                    # 1. 在点击前，获取文件状态字典。这个字典将同时用于“等待”和“查找”。
                    initial_states_dict = {f: os.path.getmtime(os.path.join(download_dir, f)) for f in os.listdir(download_dir) if not f.endswith(('.crdownload', '.tmp'))}
                    attachment_link.click()
                    time.sleep(1)
                    if wait_for_download_complete(download_dir, initial_states_dict):
                        item_data['file_path'] = find_newly_downloaded_file(download_dir, initial_states_dict)
                    logger.info(f"处理: '{item_data['title'][:30]}...', 已下载附件。")
                except TimeoutException:
                    logger.info(f"处理: '{item_data['title'][:30]}...', 无附件。")
        
                ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                time.sleep(1)
                final_results["预警单管理"].append(item_data)
        finally:
            driver.switch_to.default_content()
            
    return final_results

# --- 主程序入口 ---
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="S6000 自动化任务机器人")
    parser.add_argument('-n', '--notice', action='store_true', help='执行"工作通知"处理任务')
    parser.add_argument('-t', '--task', action='store_true', help='执行"工作任务"处理任务')
    parser.add_argument('-c', '--contact_sheet', action='store_true', help='执行"工作联系单"处理任务')
    parser.add_argument('-w', '--warning_notice', action='store_true', help='执行"预警单管理"处理任务')
    parser.add_argument('-a', '--alert_announcement', action='store_true', help='执行"地市预警通告"处理任务')
    parser.add_argument('-d', '--deadline_check', action='store_true', help='执行"反馈截止检测"处理任务')
    args = parser.parse_args()

    download_dir = r"C:\Users\<USER>\Desktop\s60000_2.0\download"
    if not os.path.exists(download_dir): os.makedirs(download_dir)

    logger.remove()
    logger.add(sys.stderr, level="INFO")
    logger.add("s6000_robot.log", level="DEBUG", rotation="10 MB", encoding="utf-8")
    
    final_output = {"success": False, "processed_count": 0, "results": {}, "timestamp": ""}
    driver = None
    try:
        edge_options = EdgeOptions()
        edge_options.add_argument('--headless')
        edge_options.add_argument('--window-size=1920,1080')
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        edge_options.add_argument('--disable-gpu')
        edge_options.add_argument('--ignore-certificate-errors')
        edge_options.add_argument("--log-level=3")
        edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        prefs = {"download.default_directory": download_dir, "download.prompt_for_download": False}
        edge_options.add_experimental_option("prefs", prefs)
        
        service = EdgeService(log_output='msedgedriver.log')
        driver = webdriver.Edge(service=service, options=edge_options)
        driver.execute_cdp_cmd("Page.setDownloadBehavior", {"behavior": "allow", "downloadPath": download_dir})
        wait = WebDriverWait(driver, 20)

        if login(driver, wait, S6000_URL, S6000_USERNAME, S6000_PASSWORD):
            task_mapping = {
                'notice': ("工作通知", process_work_notices),
                'task': ("工作任务", process_work_tasks),
                'contact_sheet': ("工作联系单", process_work_contact_sheets),
                'warning_notice': ("预警单管理", process_task_warning_notices),
                'alert_announcement': ("地市预警通告", process_task_alert_announcement),
                'deadline_check': ("反馈截止检测", process_deadline_check)
            }
            
            any_task_run = False
            for task_key, (task_name, task_func) in task_mapping.items():
                if getattr(args, task_key):
                    any_task_run = True
                    task_results = task_func(driver, wait, download_dir)
                    final_output["results"][task_name] = task_results
                    
                    # 时间任务，计算字典中所有列表长度和
                    if task_key == 'deadline_check':
                        count = sum(len(v) for v in task_results.values())
                        final_output["processed_count"] += count
                    # 其他任务，计数列表
                    else:
                        final_output["processed_count"] += len(task_results)
            
            if not any_task_run:
                logger.warning("没有指定任何任务来执行。")
            final_output["success"] = True
        else:
            final_output["error"] = "S6000 登录失败"
    except Exception as e:
        logger.error(f"主程序发生致命错误: {str(e)}", exc_info=True)
        final_output["error"] = f"主程序发生致命错误: {str(e)}"
    finally:
        if driver:
            driver.quit()
    
    final_output["timestamp"] = time.strftime("%Y-%m-%d %H:%M:%S")
    # 打印最终的JSON结果
    sys.stdout.buffer.write(json.dumps(final_output, ensure_ascii=False, indent=4).encode('utf-8'))