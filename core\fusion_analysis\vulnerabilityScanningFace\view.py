from . import r
from flask import request, jsonify
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import ipaddress
import json
import math

# 添加LouSao目录到系统路径
louSao_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'Lou<PERSON>ao')
if louSao_path not in sys.path:
    sys.path.append(louSao_path)

# 导入step2函数和step3函数
try:
    from step2_vuln_risk_score import step2
    from step3_create_simplified_csv import create_simplified_csv
    from ..LouSao.step2_vuln_risk_score import update_core_business_networks, update_important_business_networks, get_core_business_networks, get_important_business_networks, delete_core_business_network, delete_important_business_network
except ImportError as e:
    print(f"导入模块失败: {e}")
    step2 = None
    create_simplified_csv = None

@r.route('/vulnerabilityScannings/vulnDetailedScoresList', methods=['GET'])
def get_vuln_detailed_scores_list():
    """
    获取漏洞详细评分列表
    ---
    tags:
      - 绿盟漏扫
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 页码
      - name: size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页数量
    responses:
      200:
        description: 成功返回漏洞详细评分列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            msg:
              type: string
              example: "Success"
            data:
              type: object
              properties:
                page:
                  type: integer
                page_size:
                  type: integer
                total_page:
                  type: integer
                total_count:
                  type: integer
                list:
                  type: array
                  items:
                    type: object
    """
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 1000:
            size = 10
            
        # 获取漏洞详细评分数据
        df_scored, _ = step2()
        
        # 转换为字典列表
        data_list = df_scored.to_dict('records')
        
        # 计算分页信息
        total_count = len(data_list)
        total_page = math.ceil(total_count / size)
        
        # 分页处理
        start_index = (page - 1) * size
        end_index = start_index + size
        paginated_data = data_list[start_index:end_index]
        
        # 返回结果
        result = {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": paginated_data
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"获取漏洞详细评分列表失败: {str(e)}",
            "data": None
        })

@r.route('/vulnerabilityScannings/vulnDetailedScoresSimpleList', methods=['GET'])
def get_vuln_detailed_scores_simple_list():
    """
    获取简化版漏洞详细评分列表
    ---
    tags:
      - 绿盟漏扫
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 页码
      - name: size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页数量
    responses:
      200:
        description: 成功返回简化版漏洞详细评分列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            msg:
              type: string
              example: "Success"
            data:
              type: object
              properties:
                page:
                  type: integer
                  description: 当前页码
                page_size:
                  type: integer
                  description: 每页数量
                total_page:
                  type: integer
                  description: 总页数
                total_count:
                  type: integer
                  description: 总记录数
                list:
                  type: array
                  description: 数据列表
                  items:
                    type: object
                    properties:
                      plugin_id:
                        type: string
                        description: 插件ID
                      vuln_level:
                        type: string
                        description: 漏洞级别
                      target_ip:
                        type: string
                        description: 目标IP
                      severity_score:
                        type: number
                        description: 严重程度评分
                      i18n_name:
                        type: string
                        description: 漏洞名称
                      cve_id:
                        type: string
                        description: CVE编号
                      final_score:
                        type: number
                        description: 最终评分
                      risk_reason:
                        type: string
                        description: 风险原因
      500:
        description: 服务器内部错误
    """
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 1000:
            size = 10
        
        # 检查函数是否可用
        if create_simplified_csv is None:
            return jsonify({
                "code": 500,
                "msg": "简化数据处理模块不可用",
                "data": None
            })
        
        # 调用step3获取简化数据
        df_simplified = create_simplified_csv()
        
        if df_simplified is None or df_simplified.empty:
            return jsonify({
                "code": 500,
                "msg": "获取简化漏洞数据失败",
                "data": None
            })
        
        # 计算分页信息
        total_count = len(df_simplified)
        total_page = math.ceil(total_count / size)
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 获取当前页数据
        page_data = df_simplified.iloc[start_index:end_index]
        
        # 转换为字典列表
        data_list = page_data.to_dict('records')
        
        return jsonify({
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": data_list
            }
        })
        
    except ValueError as e:
        return jsonify({
            "code": 400,
            "msg": f"参数错误: {str(e)}",
            "data": None
        })
    except Exception as e:
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        })

@r.route('/vulnerabilityScannings/updateCoreBusinessNetworkSegment', methods=['PUT'])
def update_core_business_network_segment():
    """
    更新核心业务网段列表
    ---
    tags:
      - 漏洞扫描接口
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: array
          items:
            type: string
          example: ["*************", "10.0.0.0/24", "***********"]
    responses:
      200:
        description: 成功更新核心业务网段列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 9001
            msg:
              type: string
              example: "Success"
            data:
              type: object
              example: {}
      400:
        description: 请求参数错误
      500:
        description: 服务器内部错误
    """
    try:
        # 获取请求数据
        ip_addresses = request.get_json()
        
        # 验证请求数据格式
        if not isinstance(ip_addresses, list):
            return {
                "code": 4000,
                "msg": "请求参数必须是IP地址数组",
                "data": {}
            }
        
        if not ip_addresses:
            return {
                "code": 4000,
                "msg": "IP地址列表不能为空",
                "data": {}
            }
        
        # 调用更新函数
        success = update_core_business_networks(ip_addresses)
        
        if success:
            return {
                "code": 9001,
                "msg": "Success",
                "data": {}
            }
        else:
            return {
                "code": 5000,
                "msg": "更新核心业务网段失败",
                "data": {}
            }
            
    except Exception as e:
        return {
            "code": 5000,
            "msg": f"更新核心业务网段失败: {str(e)}",
            "data": {}
        }

@r.route('/vulnerabilityScannings/updateImportantBusinessNetworkSegment', methods=['PUT'])
def update_important_business_network_segment():
    """
    更新重要业务网段列表
    ---
    tags:
      - 漏洞扫描接口
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: array
          items:
            type: string
          example: ["*************", "********/24", "***********"]
    responses:
      200:
        description: 成功更新重要业务网段列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 9001
            msg:
              type: string
              example: "Success"
            data:
              type: object
              example: {}
      400:
        description: 请求参数错误
      500:
        description: 服务器内部错误
    """
    try:
        # 获取请求数据
        ip_addresses = request.get_json()
        
        # 验证请求数据格式
        if not isinstance(ip_addresses, list):
            return {
                "code": 4000,
                "msg": "请求参数必须是IP地址数组",
                "data": {}
            }
        
        if not ip_addresses:
            return {
                "code": 4000,
                "msg": "IP地址列表不能为空",
                "data": {}
            }
        
        # 调用更新函数
        success = update_important_business_networks(ip_addresses)
        
        if success:
            return {
                "code": 9001,
                "msg": "Success",
                "data": {}
            }
        else:
            return {
                "code": 5000,
                "msg": "更新重要业务网段失败",
                "data": {}
            }
            
    except Exception as e:
        return {
            "code": 5000,
            "msg": f"更新重要业务网段失败: {str(e)}",
            "data": {}
        }

@r.route('/vulnerabilityScannings/getCoreBusinessNetworkSegment', methods=['GET'])
def get_core_business_network_segment():
    """
    获取核心业务网段列表
    ---
    tags:
      - 漏洞扫描接口
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 页码
      - name: size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页数量
    responses:
      200:
        description: 成功获取核心业务网段列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            msg:
              type: string
              example: "Success"
            data:
              type: object
              properties:
                page:
                  type: integer
                  example: 1
                page_size:
                  type: integer
                  example: 10
                total_page:
                  type: integer
                  example: 5
                total_count:
                  type: integer
                  example: 45
                list:
                  type: array
                  items:
                    type: string
                  example: ["***********/24", "********/24"]
    """
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)
        
        # 验证分页参数
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 10
        
        # 获取核心业务网段列表
        all_networks = get_core_business_networks()
        
        # 计算总数和总页数
        total_count = len(all_networks)
        total_page = math.ceil(total_count / size) if total_count > 0 else 1
        
        # 实现分页逻辑
        start_index = (page - 1) * size
        end_index = start_index + size
        page_data = all_networks[start_index:end_index]
        
        return jsonify({
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        })
    
    except Exception as e:
        return jsonify({
            "code": 5000,
            "msg": f"服务器内部错误: {str(e)}",
            "data": {}
        })

@r.route('/vulnerabilityScannings/getImportantBusinessNetworkSegment', methods=['GET'])
def get_important_business_network_segment():
    """
    获取重要业务网段列表
    ---
    tags:
      - 漏洞扫描接口
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 页码
      - name: size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页数量
    responses:
      200:
        description: 成功获取重要业务网段列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            msg:
              type: string
              example: "Success"
            data:
              type: object
              properties:
                page:
                  type: integer
                  example: 1
                page_size:
                  type: integer
                  example: 10
                total_page:
                  type: integer
                  example: 5
                total_count:
                  type: integer
                  example: 45
                list:
                  type: array
                  items:
                    type: string
                  example: ["***********/24", "********/24"]
    """
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)
        
        # 验证分页参数
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 10
        
        # 获取重要业务网段列表
        all_networks = get_important_business_networks()
        
        # 计算总数和总页数
        total_count = len(all_networks)
        total_page = math.ceil(total_count / size) if total_count > 0 else 1
        
        # 实现分页逻辑
        start_index = (page - 1) * size
        end_index = start_index + size
        page_data = all_networks[start_index:end_index]
        
        return jsonify({
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        })
    
    except Exception as e:
        return jsonify({
            "code": 5000,
            "msg": f"服务器内部错误: {str(e)}",
            "data": {}
        })

@r.route('/vulnerabilityScannings/vulnIpRiskSummaryResults', methods=['GET'])
def get_vuln_ip_risk_summary_results():
    """
    获取漏洞IP风险汇总结果
    ---
    tags:
      - 绿盟漏扫
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 页码
      - name: size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页数量
    responses:
      200:
        description: 成功返回漏洞IP风险汇总结果
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            msg:
              type: string
              example: "Success"
            data:
              type: object
              properties:
                page:
                  type: integer
                page_size:
                  type: integer
                total_page:
                  type: integer
                total_count:
                  type: integer
                list:
                  type: array
                  items:
                    type: object
    """
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 1000:
            size = 10
            
        # 获取IP风险汇总数据
        _, ip_risk_summary = step2()
        
        # 转换为字典列表
        data_list = ip_risk_summary.to_dict('records')
        
        # 计算分页信息
        total_count = len(data_list)
        total_page = math.ceil(total_count / size)
        
        # 分页处理
        start_index = (page - 1) * size
        end_index = start_index + size
        paginated_data = data_list[start_index:end_index]
        
        # 返回结果
        result = {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": paginated_data
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"获取漏洞IP风险汇总结果失败: {str(e)}",
            "data": None
        })

@r.route('/vulnerabilityScannings/deleteCoreBusinessNetworkSegment', methods=['DELETE'])
def delete_core_business_network_segment():
    """
    删除核心业务网段
    ---
    tags:
      - 绿盟漏扫
    parameters:
      - name: ip
        in: query
        type: string
        required: true
        description: 要删除的IP地址
    responses:
      200:
        description: 成功删除核心业务网段
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 9001
            msg:
              type: string
              example: "Success"
            data:
              type: object
    """
    try:
        # 获取IP参数
        ip_address = request.args.get('ip', '').strip()
        
        if not ip_address:
            return jsonify({
                "code": -1,
                "msg": "IP地址参数不能为空",
                "data": {}
            })
        
        # 调用删除方法
        success = delete_core_business_network(ip_address)
        
        if success:
            return jsonify({
                "code": 9001,
                "msg": "Success",
                "data": {}
            })
        else:
            return jsonify({
                "code": -1,
                "msg": f"删除核心业务网段失败，未找到包含IP地址 {ip_address} 的网段",
                "data": {}
            })
            
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"删除核心业务网段失败: {str(e)}",
            "data": {}
        })

@r.route('/vulnerabilityScannings/deleteImportantBusinessNetworkSegment', methods=['DELETE'])
def delete_important_business_network_segment():
    """
    删除重要业务网段
    ---
    tags:
      - 绿盟漏扫
    parameters:
      - name: ip
        in: query
        type: string
        required: true
        description: 要删除的IP地址
    responses:
      200:
        description: 成功删除重要业务网段
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 9001
            msg:
              type: string
              example: "Success"
            data:
              type: object
    """
    try:
        # 获取IP参数
        ip_address = request.args.get('ip', '').strip()
        
        if not ip_address:
            return jsonify({
                "code": -1,
                "msg": "IP地址参数不能为空",
                "data": {}
            })
        
        # 调用删除方法
        success = delete_important_business_network(ip_address)
        
        if success:
            return jsonify({
                "code": 9001,
                "msg": "Success",
                "data": {}
            })
        else:
            return jsonify({
                "code": -1,
                "msg": f"删除重要业务网段失败，未找到包含IP地址 {ip_address} 的网段",
                "data": {}
            })
            
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"删除重要业务网段失败: {str(e)}",
            "data": {}
        })