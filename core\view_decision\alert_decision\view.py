import pymysql
from flask import request, jsonify
from datetime import datetime
from . import *
import threading
import time
import re
import json

DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

def call_deepseek_model(user_prompt):
    import requests
    system_prompt = "你是一位网络安全事件决策专家，能够根据我提供的安全事件和可选的程序与程序行为，给出且只给出格式化后的应对策略"
    url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Authorization": dp_apikey,
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "stream": False,
        "temperature": 0.1,  # 降低随机性，加快响应

    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]
    except requests.Timeout:
        raise Exception("DeepSeek API调用超时")
    except requests.RequestException as e:
        raise Exception(f"DeepSeek API调用失败: {e}")

def generate_combined_prompt(event_name, event_description):
    action_list = """base64 加密,base64 解密,dingding 钉钉通知,email 邮件发送,feishu 飞书通知,h3c 添加黑名单,ip IP查询,linux 执行命令,md5 加密,mysql 查询,mysql 增删改,nmap 端口扫描,redis GET,redis SET,redis DEL,redis 清空数据-单个DB,redis 清空数据-全部DB,url 生成,windows 执行命令"""
    
    base_prompt = f"安全事件:{event_name}|描述:{event_description}\n"
    base_prompt += f"可用操作:{action_list}\n"
    
    base_prompt += "请严格按照以下格式返回：\n\n"
    base_prompt += "【自然语言决策】\n"
    base_prompt += "一. 事件分析\n"
    base_prompt += "（此处换行）\n"
    base_prompt += "先分析该安全事件的潜在影响和风险\n\n"

    base_prompt += "二. 应对策略\n"
    base_prompt += "（此处换行）\n"
    base_prompt += "然后给出完整的应对流程\n\n"
    base_prompt += "应对策略必须按照以下格式：\n"
    base_prompt += "1.操作与预期效果说明\n"
    base_prompt += "2.操作与预期效果说明\n"
    base_prompt += "...\n\n"
    
    base_prompt += "三. 最优解推荐最后总结推荐最优解决方案\n\n"
    base_prompt += "（此处换行）\n"
    base_prompt += "然后给出完整的应对流程\n\n"

    base_prompt += "【结构化决策】\n"
    base_prompt += "请只返回最优解的决策链，严格使用以下格式：\n"
    base_prompt += "事件名称->开始->程序 程序行为->程序 程序行为->...->结束\n"
    base_prompt += "不要包含任何分析或解释，只返回决策链本身\n"

    # 添加约束条件
    base_prompt += "\n重要约束：\n"
    base_prompt += "1. 必须严格按照要求的格式返回\n"
    base_prompt += "2. 应对策略部分必须使用编号列表\n"
    base_prompt += "3. 结构化决策部分必须严格遵循指定格式\n"
    
    # if required_steps:
    #     base_prompt += f"必需步骤:{required_steps}\n"
    # if complexity_level >= 3:
    #     base_prompt += "请进行深度分析\n"

    return base_prompt


def extract_structured_chain(text):
    # 匹配任意字符开头，以“->开始->...->结束”结尾的链
    # 允许中文、英文、空格、符号
    pattern = r'([^\s\n\r\f\->][^->\n\r\f]*?->开始->(?:[^->\n\r\f]+->)+?结束)'
    match = re.search(pattern, text)
    if match:
        return match.group(1).strip()
    return None



@r.route("/alert/decision", methods=['POST'])
def alert_decision():
    connection = None
    try:
        data = request.json or {}
        notification_id = data.get("notification_id")
        # complexity_level = int(data.get("complexity_level", 1))
        # required_steps = data.get("required_steps", []) or []

        if not notification_id:
            return jsonify({"code": 400, "msg": "notification_id为必填", "data": None})

        connection = get_db_connection()

        with connection.cursor() as cursor:


            cursor.execute("SELECT * FROM w5_notification WHERE notification_id = %s", (notification_id,))
            notification_row = cursor.fetchone()
            if not notification_row:
                return jsonify({"code": 404, "msg": "notification_id不存在", "data": None})


            # 从数据库取出后
            result_data_json_str = notification_row["result_data"]  # 这是字符串

            # 关键一步：解析 JSON 字符串
            try:
                result_data = json.loads(result_data_json_str)
            except (json.JSONDecodeError, TypeError) as e:
                # 处理解析失败的情况（数据损坏、NULL 值等）
                logger.error(f"无法解析 result_data: {result_data_json_str}, 错误: {e}")
                result_data = {}

            # 安全地获取值
            event_name = result_data.get("rule_name", "")
            attck_desc = result_data.get("attck_desc", "")
            hazard_level = result_data.get("hazard_level", "High")
            event_description = f"严重性: {hazard_level}, 签名: {attck_desc}"

        # ✅ 1. 生成合并提示词（使用所有参数）
        prompt = generate_combined_prompt(
            event_name=event_name,
            event_description=event_description,
            # complexity_level=complexity_level,
            # required_steps=required_steps,

        )

        # ✅ 2. 只调用一次 DeepSeek
        response = call_deepseek_model(prompt)

        # ✅ 解析响应：去除标题，只保留内容
        response_natural = ""
        response_structured = ""

        # 分割两个部分
        natural_start = response.find("【自然语言决策】")
        structured_start = response.find("【结构化决策】")
        end = len(response)

        # === 1. 提取【自然语言决策】内容 ===
        if natural_start != -1:
            # 确定结束位置：下一个标题或结尾
            next_section = structured_start if structured_start != -1 else end
            raw_natural = response[natural_start:next_section].strip()
            
            # 按行处理，跳过标题行
            lines = raw_natural.splitlines()
            content_lines = []
            found_header = False
            for line in lines:
                line = line.strip()
                # 跳过【自然语言决策】标题行
                if "【自然语言决策】" in line:
                    found_header = True
                    continue
                # 收集标题之后的内容
                if found_header:
                    content_lines.append(line)
                # 如果还没找到标题，但当前行不是标题，也保留（兼容格式异常）
                elif "【自然语言决策】" not in line:
                    content_lines.append(line)
            
            response_natural = "\n".join(content_lines).strip()
        else:
            # 回退：返回完整响应
            response_natural = response.strip()

        # === 2. 提取【结构化决策】中的决策链（无正则）===
        response_structured = "事件名称->开始->分析事件->结束"  # 默认兜底

        # 优先在【结构化决策】部分查找
        if structured_start != -1:
            raw_text = response[structured_start:end]
        else:
            raw_text = response  # 全文查找

        # 按行处理
        lines = raw_text.splitlines()
        start_keyword = "->开始->"
        end_keyword = "->结束"

        for line in lines:
            line = line.strip()
            
            # 跳过空行和无关说明
            if not line or len(line) < 10:
                continue
            if "【结构化决策】" in line or "请只返回" in line or "格式" in line or "不要包含" in line:
                continue
            
            # 核心判断：是否包含 "->开始->" 和 "->结束"
            if start_keyword in line and end_keyword in line:
                # 清理：去除行首尾的 *、-、>、空格等
                clean_line = line.strip(' *-＞> \t\n')
                # 可选：替换中文箭头
                clean_line = clean_line.replace('＞', '>').replace('－', '-').replace('→', '->')
                
                # 验证基本格式：至少有两个 "->"
                if clean_line.count("->") >= 2:
                    response_structured = clean_line
                    break  # 找到第一个就返回，避免后续干扰

        # ✅ 4. 更新 w5_notification.is_decided
        with connection.cursor() as cursor:
            cursor.execute("UPDATE w5_notification SET is_decided = 1 WHERE notification_id = %s", (notification_id,))
            cursor.execute("SELECT * FROM w5_notification WHERE notification_id = %s", (notification_id,))
            updated_alert = cursor.fetchone()
            # 从数据库取出后
            result_data_json_str = updated_alert["result_data"]  # 这是字符串

            # 关键一步：解析 JSON 字符串
            try:
                result = json.loads(result_data_json_str)
            except (json.JSONDecodeError, TypeError) as e:
                # 处理解析失败的情况（数据损坏、NULL 值等）
                logger.error(f"无法解析, 错误: {e}")

        # ✅ 5. 删除旧记录并插入新分析
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM w5_analysis WHERE analysis_id = %s", (notification_id,))
            cursor.execute("DELETE FROM w5_alert_analysis WHERE analysis_id = %s OR alert_id = %s", (notification_id, notification_id))

            insert_analysis_sql = """
            INSERT INTO w5_analysis 
            (analysis_id, timestamp, attack_summary, affected_systems, potential_risk, recommended_actions, notes, status, create_time, update_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_analysis_sql, (
                notification_id, now, '', '', '', response_structured, response_natural, 0, now, now
            ))

            insert_alert_analysis_sql = "INSERT INTO w5_alert_analysis (analysis_id, alert_id, create_time) VALUES (%s, %s, %s)"
            cursor.execute(insert_alert_analysis_sql, (notification_id, notification_id,now))
            connection.commit()

        # ✅ 6. 返回结果
        result_data = {
            "natural_language_decision": response_natural,
            "structured_decision": response_structured,
            "updated_alert": result
        }
        return jsonify({"code": 200, "msg": "决策生成并已记录", "data": result_data})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})
    except Exception as e:
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})
    finally:
        try:
            if connection and connection.open:
                connection.close()
        except:
            pass

# @r.route("/alert/decision", methods=['POST'])
# def alert_decision():
#     connection = None
#     try:
#         data = request.json or {}
#         alert_id = data.get("alert_id")
#         # complexity_level = int(data.get("complexity_level", 1))
#         # required_steps = data.get("required_steps", []) or []

#         if not alert_id:
#             return jsonify({"code": 400, "msg": "alert_id为必填", "data": None})

#         connection = get_db_connection()
#         with connection.cursor() as cursor:
#             cursor.execute("SELECT * FROM w5_alert WHERE alert_id = %s", (alert_id,))
#             alert_row = cursor.fetchone()
#             if not alert_row:
#                 return jsonify({"code": 404, "msg": "alert_id不存在", "data": None})

#             event_name = alert_row["attack_type"]
#             event_description = f"严重性: {alert_row['severity']}, 签名: {alert_row['signature']}"

#         # ✅ 1. 生成合并提示词（使用所有参数）
#         prompt = generate_combined_prompt(
#             event_name=event_name,
#             event_description=event_description,
#             # complexity_level=complexity_level,
#             # required_steps=required_steps,

#         )

#         # ✅ 2. 只调用一次 DeepSeek
#         response = call_deepseek_model(prompt)


#         # ✅ 解析响应：去除标题，只保留内容
#         response_natural = ""
#         response_structured = ""

#         # 分割两个部分
#         natural_start = response.find("【自然语言决策】")
#         structured_start = response.find("【结构化决策】")
#         end = len(response)

#         if natural_start != -1:
#             # 提取自然语言内容（跳过标题本身）
#             next_section = structured_start if structured_start != -1 else end
#             raw_natural = response[natural_start:next_section].strip()
#             # 去掉标题行，只保留内容
#             lines = raw_natural.splitlines()
#             # 找到第一个非标题行（跳过“【自然语言决策】”这行）
#             content_lines = []
#             found_header = False
#             for line in lines:
#                 if "【自然语言决策】" in line:
#                     found_header = True
#                     continue
#                 if found_header or "【自然语言决策】" not in line:
#                     content_lines.append(line)
#             response_natural = "\n".join(content_lines).strip()
#         else:
#             response_natural = response.strip()  # 回退

#         # === 提取结构化决策链（支持任意事件名称）===
#         response_structured = "事件名称->开始->分析事件->结束"  # 默认兜底

#         # 优先在【结构化决策】部分查找
#         if structured_start != -1:
#             raw_structured = response[structured_start:end]
#         else:
#             raw_structured = response  # 全文查找

#         # 匹配：任意非空白开头 -> 开始 -> ... -> 结束
#         # 支持中文、英文、空格、特殊字符（除了 ->）
#         match = re.search(
#             r'([^->\n\r\f\[\s][^->\n\r\f]*?->开始->(?:[^->\n\r\f]+->)+?结束)',
#             raw_structured
#         )
#         if match:
#             response_structured = match.group(1).strip()

#         # ✅ 4. 更新 w5_alert.is_decided
#         with connection.cursor() as cursor:
#             cursor.execute("UPDATE w5_alert SET is_decided = 1 WHERE alert_id = %s", (alert_id,))
#             cursor.execute("SELECT * FROM w5_alert WHERE alert_id = %s", (alert_id,))
#             updated_alert = cursor.fetchone()

#         # ✅ 5. 删除旧记录并插入新分析
#         now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#         with connection.cursor() as cursor:
#             cursor.execute("DELETE FROM w5_analysis WHERE analysis_id = %s", (alert_id,))
#             cursor.execute("DELETE FROM w5_alert_analysis WHERE analysis_id = %s OR alert_id = %s", (alert_id, alert_id))

#             insert_analysis_sql = """
#             INSERT INTO w5_analysis 
#             (analysis_id, timestamp, attack_summary, affected_systems, potential_risk, recommended_actions, notes, status, create_time, update_time)
#             VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
#             """
#             cursor.execute(insert_analysis_sql, (
#                 alert_id, now, '', '', '', response_structured, response_natural, 0, now, now
#             ))

#             insert_alert_analysis_sql = "INSERT INTO w5_alert_analysis (analysis_id, alert_id, create_time) VALUES (%s, %s, %s)"
#             cursor.execute(insert_alert_analysis_sql, (alert_id, alert_id, now))
#             connection.commit()

#         # ✅ 6. 返回结果
#         result_data = {
#             "natural_language_decision": response_natural,
#             "structured_decision": response_structured,
#             "updated_alert": updated_alert
#         }
#         return jsonify({"code": 200, "msg": "决策生成并已记录", "data": result_data})

#     except pymysql.MySQLError as e:
#         return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})
#     except Exception as e:
#         return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})
#     finally:
#         try:
#             if connection and connection.open:
#                 connection.close()
#         except:
#             pass
