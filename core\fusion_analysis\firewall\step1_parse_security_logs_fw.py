# -*- coding: utf-8 -*-
import pandas as pd
import json
import re
import os
from typing import Any, Dict, List, Union
import random
import sys
import os
import configparser
import pymysql
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# INPUT_FILE = "奇安信天眼流量传感器.csv"   # 奇安信原始CSV
# OUTPUT_MAIN = "enhanced_security_logs.csv"   # 提取后的核心字段
# OUTPUT_FLAT = "step1_parsed_raw_flat.csv"  # 扁平化后的全部字段（用于排查）

# 可能存放JSON的列（优先顺序）
CANDIDATE_JSON_COLUMNS = [
    "raw_string",
    "id",
    "host",
    "time",
    "type",
    "level",
    "data_source_id",
    "data_source_type",
    "source_id",
    "row_number",
]

def get_security_logs_from_db():
    try:
        # 读取配置文件
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config.ini')
        cf = configparser.ConfigParser()
        cf.read(config_path, encoding='utf-8')
        # 数据库配置
        DB_CONFIG = {
            'host': cf.get("mysql", "host"),
            'port': int(cf.get("mysql", "port")),
            'user': cf.get("mysql", "user"),
            'password': cf.get("mysql", "password"),
            'database': cf.get("mysql", "database"),
            'charset': 'utf8mb4'
        }

        print(f"连接数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")

        # 建立数据库连接
        conn = pymysql.connect(**DB_CONFIG)

        try:
            # 查询log_row_tbl表数据 (针对奇安信流量传感器数据)
            sql = """
            SELECT 
             `id` ,`host`, `time`, `type`, `level`, `data_source_id`,
            `data_source_type`, `source_id`, `row_number`, `raw_string`
            FROM `log_row_tbl` 
            WHERE `data_source_id` = 1950139339589738498
            ORDER BY `time` DESC
            """

            # 使用connection对象的cursor执行查询
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                # 创建DataFrame
                df = pd.DataFrame(result, columns=columns)
            return df

        except Exception as e:
            print(f"数据库查询错误: {e}")
            return pd.DataFrame()
        finally:
            conn.close()

    except Exception as e:
        print(f"数据库查询失败: {e}")
        print("无法从数据库获取数据")
        return pd.DataFrame()



def read_csv_robust(path: str) -> pd.DataFrame:
    """尝试多种常见编码读取CSV。"""
    attempts = ["utf-8-sig", "utf-8", "gbk", "cp936"]
    last_err = None
    for enc in attempts:
        try:
            return pd.read_csv(path, encoding=enc, low_memory=False)
        except Exception as e:
            last_err = e
            continue
    raise RuntimeError(f"读取CSV失败（尝试编码 {attempts}）：{last_err}")

def guess_json_column(df: pd.DataFrame) -> str:
    """优先使用已知列名；否则自动在所有object列里找包含'{'的列。"""
    for col in CANDIDATE_JSON_COLUMNS:
        if col in df.columns:
            return col
    # 自动探测
    obj_cols = df.select_dtypes(include=["object"]).columns
    best_col, best_hits = None, -1
    for col in obj_cols:
        s = df[col].astype(str)
        hits = s.str.contains(r"\{", regex=True, na=False).sum()
        if hits > best_hits:
            best_col, best_hits = col, hits
    if best_col is None:
        raise RuntimeError("未找到可能包含JSON的列。请检查CSV。")
    return best_col

def extract_first_json(text: str) -> Union[Dict[str, Any], None]:
    """从文本中提取第一个 {...} 并解析成 dict。"""
    if not isinstance(text, str):
        return None
    # 处理奇安信日志格式，提取大括号内的JSON
    m = re.search(r"\{.*\}", text)
    if not m:
        return None
    try:
        return json.loads(m.group(0))
    except Exception:
        return None

def flatten(obj: Any, prefix: str = "", out: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    递归扁平化字典/列表：
      - dict: key 用 'a.b' 连接
      - list: 用下标 'a.0', 'a.1'
    """
    if out is None:
        out = {}
    if isinstance(obj, dict):
        for k, v in obj.items():
            key = f"{prefix}.{k}" if prefix else str(k)
            flatten(v, key, out)
    elif isinstance(obj, list):
        for i, v in enumerate(obj):
            key = f"{prefix}.{i}" if prefix else str(i)
            flatten(v, key, out)
    else:
        out[prefix] = obj
    return out

def get_parsed_infos(json_root: Dict[str, Any]) -> Union[Dict[str, Any], None]:
    """
    在JSON根中寻找 parsedInfos：
      - 若是 dict，直接使用
      - 若是 list（多个kv片段），尝试合并到一个dict
      - 若不存在，返回 None
    """
    if not isinstance(json_root, dict):
        return None
    if "parsedInfos" not in json_root:
        # 对于奇安信日志，直接返回根对象
        return json_root
    pi = json_root["parsedInfos"]
    if isinstance(pi, dict):
        return pi
    if isinstance(pi, list):
        merged = {}
        for item in pi:
            if isinstance(item, dict):
                merged.update(item)
        return merged if merged else None
    # 其他类型，尝试强转字典
    try:
        return dict(pi)
    except Exception:
        return None

def extract_qianxin_fields(row_data: Dict[str, Any], parsed_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    从解析后的奇安信日志中提取指定字段
    """
    result = {}
    
    # 基础字段直接从数据库行数据中获取
    result["id"] = row_data.get("id", "")
    result["host"] = row_data.get("host", "")
    result["time"] = row_data.get("time", "")
    result["level"] = row_data.get("level", "")
    result["data_source_id"] = row_data.get("data_source_id", "")
    result["data_source_type"] = row_data.get("data_source_type", "")
    result["source_id"] = row_data.get("source_id", "")
    
    # 从parsedInfos中提取网络相关字段
    # 奇安信字段映射
    field_mapping = {
        "srcAddress": ["srcAddress", "src_address", "src_ip", "src"],
        "destAddress": ["destAddress", "dstAddress", "dst_address", "dst_ip", "dst"],
        "srcPort": ["srcPort", "src_port", "sport"],
        "destPort": ["destPort", "dstPort", "dst_port", "dport"],
        "eventType": ["eventType", "event_type", "type"],
        "severity": ["severity", "level", "event_level"],
        "eventName": ["eventName", "event_name", "name", "signature"],
        "deviceName": ["deviceName", "devName", "device_name"],
        "rawEventTime": ["rawEventTime", "deviceReceiptTime", "startTime", "endTime", "collectorReceiptTime", "timestamp", "event_time"],
        "deviceCat": ["deviceCat", "device_cat", "devicetype"],
        "attackResult": ["attackResult", "attack_result", "result"],
        "solution": ["solution", "repair_suggestion"],
        "detailInfo": ["detailInfo", "detail_info", "details", "msg", "message"]
    }
    
    # 尝试从parsed_info中提取字段
    for target_field, possible_names in field_mapping.items():
        value = ""
        # 首先尝试从parsedInfos中获取
        if isinstance(parsed_info.get("parsedInfos"), dict):
            for name in possible_names:
                if name in parsed_info["parsedInfos"]:
                    value = parsed_info["parsedInfos"][name]
                    break
        
        # 如果parsedInfos中没有，尝试从根对象获取
        if not value:
            for name in possible_names:
                if name in parsed_info:
                    value = parsed_info[name]
                    break
                    
        result[target_field] = value
            
    return result

def get_processed_security_logs():
    """
    获取并处理安全日志数据，返回处理后的DataFrame
    这个函数可以被其他模块调用来获取处理后的数据
    """
    df = get_security_logs_from_db()
    if df.empty:
        print("[WARNING] 从数据库获取的数据为空")
        return pd.DataFrame()
        
    json_col = guess_json_column(df)
    print(f"[INFO] 识别到JSON列：{json_col}")

    # 提取并解析JSON
    json_objs = df[json_col].apply(extract_first_json)

    # 收集解析后的数据
    extracted_rows = []

    for i, obj in enumerate(json_objs):
        if not isinstance(obj, dict):
            continue
            
        # 获取对应的原始行数据
        row_data = df.iloc[i].to_dict()
            
        # 提取奇安信特定字段
        extracted = extract_qianxin_fields(row_data, obj)
        extracted_rows.append(extracted)

    if not extracted_rows:
        print("[WARNING] 未解析到任何JSON记录")
        return pd.DataFrame()

    # 使用提取的字段创建最终输出
    df_out = pd.DataFrame(extracted_rows)

    # 数据清洗
    # 字符串去空白
    for c in df_out.select_dtypes(include=["object"]).columns:
        df_out[c] = df_out[c].astype(str).str.strip()
        df_out[c] = df_out[c].replace({"": pd.NA, "nan": pd.NA, "None": pd.NA})
    
    print(f"[OK] 数据处理完成，共{len(df_out)}条记录")
    print(f"[OK] 重要字段列：{list(df_out.columns)}")
    
    return df_out

def main():
    """
    主函数，用于直接运行此脚本时的处理逻辑
    """
    df_out = get_processed_security_logs()
    
    if df_out.empty:
        raise SystemExit("错误：未获取到任何有效数据")
    
    # 保存到文件（可选）
    # df_out.to_csv(OUTPUT_MAIN, index=False, encoding="utf-8-sig")
    # print(f"[OK] 提取完成：{OUTPUT_MAIN}")
    
    # 显示前几行结果
    print("\n[结果预览]:")
    print(df_out.head(5).to_string(index=False))
    
    # 统计非空率
    non_null_rate = df_out.notna().mean().sort_values(ascending=False).round(3)
    print("\n[Summary] 非空率（便于确认字段是否映射成功）:")
    print(non_null_rate.to_string())

if __name__ == "__main__":
    main()