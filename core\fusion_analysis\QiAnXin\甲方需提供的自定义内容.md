# 绿盟漏洞数据分析系统 - 甲方需提供的自定义内容

## 概述

本文档列出了绿盟漏洞数据分析系统中需要甲方根据自身环境和需求进行自定义配置的内容。这些配置对于系统能够准确反映甲方特定环境下的漏洞风险状况至关重要。

## 资产重要性评估中的IP网段划分

在`step2_vuln_risk_score.py`文件中的`asset_importance`函数中，需要甲方提供自己的业务网段划分：

```python
# 核心业务网段（权重3）
core_networks = [
    ipaddress.ip_network('************/24'),  # 核心服务器网段
    ipaddress.ip_network('************/24'),  # 数据库网段
]

# 重要业务网段（权重2）
important_networks = [
    ipaddress.ip_network('**********/16'),    # 内网主要网段
    ipaddress.ip_network('**********/16'),    # 办公网段
]
```

**甲方需提供**：
- 核心业务网段列表（最重要的服务器、数据库等网段，权重3）
- 重要业务网段列表（内网主要网段、办公网段等，权重2）
- 一般网段（默认其他所有网段，权重1）

##  危险漏洞关键词列表

在`score_dangerous_vuln`和`generate_risk_reason`函数中，定义了危险关键词列表：

```python
dangerous_keywords = ['远程代码执行', '命令执行', 'RCE', '提权', '缓冲区溢出', 'SQL注入']
```

**甲方需提供**：
- 自定义的危险关键词列表，根据自身安全关注点，添加或删除特定的漏洞类型关键词

## 风险评分权重配置（已经提供建议：传统评分增加）

在`calculate_comprehensive_score`函数中，使用了混合评分模型：

```python
# 使用混合评分模型：60% CVSS标准评分 + 30% 传统评分 + 10% 环境调整
```

**甲方需提供**：
- CVSS标准评分权重（默认60%）
- 传统评分权重（默认30%）
- 环境调整权重（默认10%）

## 6. 风险原因描述阈值

在`generate_risk_reason`函数中，定义了一些阈值来生成风险原因描述：

```python
# 基于漏洞数量
if row['vuln_count'] >= 10:
    reasons.append(f"大量漏洞({row['vuln_count']}个)")
elif row['vuln_count'] >= 5:
    reasons.append(f"较多漏洞({row['vuln_count']}个)")
```

**建议提供**：
- 大量漏洞阈值（默认10个）
- 较多漏洞阈值（默认5个）

# 奇安信

## 内网IP判断自定义配置

在`step2_flow_direction.py`文件中的`is_private_ip`函数用于判断IP是否为内网IP，甲方可根据自身网络环境进行自定义：

```python
def is_private_ip(ip):
    """
    判断IP是否为内网IP
    使用ipaddress库判断是否为私有IP地址
    异常情况返回False（按外网处理）
    """
    try:
        return ipaddress.ip_address(ip).is_private
    except:
        return False
```

**甲方需提供**：

1. 自定义内网IP范围（如有特殊网段需要被识别为内网）
2. 特殊IP白名单（需要被视为内网的外部IP）
3. 特殊IP黑名单（需要被视为外网的内部IP）

自定义示例：

```python
def is_private_ip(ip):
    # 定义白名单和黑名单
    whitelist = ['***********', '***********']  # 这些外网IP按内网处理
    blacklist = ['*************', '********']   # 这些内网IP按外网处理
    
    # 检查白名单和黑名单
    if ip in whitelist:
        return True
    if ip in blacklist:
        return False
    
    try:
        ip_obj = ipaddress.ip_address(ip)
        
        # 标准私有IP判断
        if ip_obj.is_private:
            return True
            
        # 添加自定义内网IP范围
        custom_ranges = [
            ipaddress.ip_network('**********/10'),  # 运营商级NAT地址
            ipaddress.ip_network('自定义IP段/掩码'),
        ]
        
        for network in custom_ranges:
            if ip_obj in network:
                return True
                
        return False
    except:
        return False
```

## 可信来源白名单（step6_false_positive.py）

`trusted_sources = set([`
    `'**********',`  

`假设这是可信的外部服务`

`可以添加更多可信来源`

`])`、

## 预置业务相关池 - 用于识别业务相关的HTTP请求

`business_related_patterns = [`
    `"Prometheus", "电力监控", "monitor", "power", "energy", "dashboard",` 
    `"panel", "control", "system", "internal", "业务", "监控", "电力", "能源"`
`]`

## 预置常见互联网池 - 用于识别非业务相关的HTTP请求
`common_internet_patterns = [`
    `"Mozilla", "Chrome", "Firefox", "Safari", "Edge", "Google", "Baidu",` 
    `"Bing", "Yahoo", "public", "cdn", "api", "www", "http", "https"`
`]`

## 工作时间定义（step4_work_time.py）

`工作日范围：目前设置为周一至周五`

`is_weekday = time.dayofweek < 5  # 0-4 表示周一至周五`

`工作时间段：目前设置为08:30-17:30`

`is_work_hours = (hour > 6 or (hour == 6 and minute >= 30)) and` 
               `(hour < 20 or (hour == 20 and minute <= 30))`

