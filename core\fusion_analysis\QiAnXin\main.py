import os
import sys
import pandas as pd
import numpy as np
import time

def run_step(step_name, script_name):
    """
    运行指定步骤的脚本
    """
    print(f"\n{'='*50}")
    print(f"开始执行 {step_name}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        # 导入并执行脚本的main函数
        module_name = script_name.replace('.py', '')
        __import__(module_name).main()
        
        end_time = time.time()
        print(f"\n{step_name} 执行完成，耗时 {end_time - start_time:.2f} 秒")
        return True
    except Exception as e:
        print(f"\n{step_name} 执行失败: {str(e)}")
        return False

def main():
    print("开始执行奇安信数据融合分析完整流程（9个步骤）...\n")
    
    # 定义步骤和对应的脚本
    steps = [
        ("步骤1: 安全日志解析", "step1_parse_security_logs.py"),
        ("步骤2: IP流向判定", "step2_flow_direction.py"),
        ("步骤3: 业务相关性分析", "step3_business_analysis.py"),
        ("步骤4: 工作时间标记", "step4_work_time.py"),
        ("步骤5: IP威胁分数生成", "step5_temp.py"),
        ("步骤6: 误报处理", "step6_false_positive.py"),
        ("步骤7: 行为分析", "step7_behavior_analysis.py"),
        ("步骤8: 风险打分与结论", "step8_risk_scoring.py"),
        ("步骤9: 告警类型和攻击结果分析", "step9_alert_analysis2.py")
    ]
    
    # 检查原始输入文件是否存在
    if not os.path.exists("奇安信天眼流量传感器.csv"):
        print("错误: 输入文件 奇安信天眼流量传感器.csv 不存在")
        return
    
    # 依次执行各步骤
    for step_name, script_name in steps:
        success = run_step(step_name, script_name)
        if not success:
            print(f"\n{step_name} 失败，流程中断")
            return
    
    print("\n所有步骤执行完成！")
    print("\n最终结果文件: step9_alert_analysis_result2.csv")
    print("统计结果文件: stats_*.csv")
    
    # 显示攻击结果和置信度统计信息
    try:
        attack_stats = pd.read_csv("stats_attack_result_counts.csv")
        confidence_stats = pd.read_csv("stats_confidence_counts.csv")
            
        print("\n攻击结果统计:")
        for _, row in attack_stats.iterrows():
            print(f"{row['attackResult']}: {row['count']}条记录")
            
        print("\n置信度统计:")
        for _, row in confidence_stats.iterrows():
            print(f"{row['confidence_level']}置信度: {row['count']}条记录")
    except Exception as e:
        print(f"读取统计信息失败: {str(e)}")


if __name__ == "__main__":
    main()