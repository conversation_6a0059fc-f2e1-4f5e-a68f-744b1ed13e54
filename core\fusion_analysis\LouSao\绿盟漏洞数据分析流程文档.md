# 绿盟漏洞数据分析流程文档

## 项目概述

本项目是一个基于CVSS标准的漏洞风险评分系统，用于分析绿盟安全设备导出的漏洞数据，计算风险评分，并生成相关报告。整个流程包括数据解析、风险评分计算和报告生成三个主要步骤。

## 详细流程说明

### 步骤1: 数据解析

**功能**: 解析原始漏洞数据文件

**输入**: `sourcedata.txt` (包含多个JSON对象的文本文件)

**输出**: `step1_parsed_vuln_data.csv` (解析后的漏洞数据CSV文件)

**实现文件**: `step1_parse_vuln_data.py`

**主要处理逻辑**:
1. 读取包含多个JSON对象的文本文件
2. 使用JSON解码器逐个解析JSON对象
3. 从每个JSON对象中提取漏洞信息
4. 提取的字段包括:
   - plugin_id: 漏洞插件ID
   - vuln_level: 漏洞级别
   - date_found: 发现日期
   - date_recorded: 记录日期
   - exp_desc: 漏洞描述
   - target_ip: 目标IP
   - severity_score: 严重程度评分
   - scan_method: 扫描方法
   - vul_confirmed: 漏洞确认状态
   - is_dangerous: 是否危险
   - i18n_name: 国际化名称
   - cve_id: CVE编号
   - threat_level: 威胁级别
5. 将提取的数据转换为Pandas DataFrame
6. 保存为CSV文件

### 步骤2: 风险评分计算

**功能**: 计算漏洞风险评分

**输入**: 步骤1生成的解析后的漏洞数据

**输出**: 
- `step2_vuln_detailed_scores.csv` (详细风险评分数据)
- `step2_vuln_ip_risk_summary.csv` (IP风险汇总数据)

**实现文件**: `step2_vuln_risk_score.py`

**主要处理逻辑**:

1. **CVSS评分映射**:
   - 将绿盟漏洞数据映射到CVSS v3.1指标
   - 映射的指标包括:
     - 攻击向量 (Attack Vector)
     - 攻击复杂度 (Attack Complexity)
     - 所需权限 (Privileges Required)
     - 用户交互 (User Interaction)
     - 影响范围 (Scope)
     - CIA影响指标 (Confidentiality, Integrity, Availability)

2. **CVSS评分计算**:
   - 计算基础评分 (Base Score)
   - 计算时间评分 (Temporal Score)
   - 计算环境评分 (Environmental Score)

3. **传统评分计算**:
   - 基础漏洞评分: 基于漏洞级别和严重程度
   - 漏洞密度评分: 基于IP地址的漏洞数量
   - 危险漏洞评分: 基于漏洞描述关键词
   - CVE关联评分: 基于是否有CVE编号

4. **资产重要性评估**:
   - 根据IP地址判断资产重要性
   - 核心业务网段权重为3
   - 重要业务网段权重为2
   - 一般网段权重为1

5. **综合评分计算**:
   - 结合CVSS评分、传统评分和环境因素
   - 使用加权平均方法计算最终评分

6. **IP风险汇总**:
   - 按IP地址汇总漏洞风险
   - 计算每个IP的平均风险评分和最高风险评分
   - 统计每个IP的漏洞数量和高危漏洞数量

### 步骤3: 报告生成

**功能**: 创建简化版漏洞报告

**输入**: 步骤2生成的详细风险评分数据

**输出**: `step3_vuln_detailed_scores_simplified.csv` (简化版漏洞报告)

**实现文件**: `step3_create_simplified_csv.py`

**主要处理逻辑**:
1. 读取详细风险评分数据
2. 选择重要字段，包括:
   - plugin_id: 插件ID - 漏洞标识
   - vuln_level: 漏洞级别 - 风险等级
   - target_ip: 目标IP - 受影响资产
   - severity_score: 严重程度评分 - CVSS评分
   - i18n_name: 漏洞名称 - 漏洞描述
   - cve_id: CVE编号 - 标准漏洞编号
   - final_score: 最终评分 - 综合风险评分
   - risk_reason: 风险原因 - 风险分析
3. 格式化数值字段为一位小数
4. 处理空值
5. 保存简化后的CSV文件
6. 显示数据统计和预览

## 主程序流程

**实现文件**: `main.py`

**主要处理逻辑**:
1. 导入各个步骤的模块
2. 定义完整工作流函数 `run_full_workflow()`
3. 按顺序执行三个步骤:
   - 步骤1: 解析漏洞数据
   - 步骤2: 计算风险评分
   - 步骤3: 创建简化CSV报告
4. 记录执行时间和结果
5. 输出总结信息

## 理论依据

本项目的风险评分系统基于CVSS v3.1标准，结合了多准则决策分析理论，采用混合评分模型:

### CVSS v3.1标准

- **基础评分**: 反映漏洞的固有特性
- **时间评分**: 考虑随时间变化的因素
- **环境评分**: 考虑特定部署环境的影响

### 混合评分模型

综合评分 = α × CVSS评分 + β × 传统评分 + γ × 环境因素

权重分配:
- α = 0.6 (CVSS权重)
- β = 0.3 (传统评分权重)
- γ = 0.1 (环境因素权重)

### 传统评分方法

传统评分 = w1×基础分 + w2×密度分 + w3×危险分 + w4×CVE关联分

权重分配:
- w1 = 0.30 (基础分权重)
- w2 = 0.25 (密度分权重)
- w3 = 0.25 (危险分权重)
- w4 = 0.20 (CVE关联分权重)

## 使用方法

1. 准备原始漏洞数据文件 `sourcedata.txt`
2. 运行主程序:
   ```
   python main.py
   ```
3. 查看生成的报告文件:
   - `step1_parsed_vuln_data.csv`: 原始漏洞数据解析结果
   - `step2_vuln_detailed_scores.csv`: 详细风险评分数据
   - `step2_vuln_ip_risk_summary.csv`: IP风险汇总数据
   - `step3_vuln_detailed_scores_simplified.csv`: 简化版漏洞报告

## 总结

本项目实现了一个完整的漏洞数据分析工作流，从原始数据解析到风险评分计算再到报告生成。系统基于国际标准CVSS v3.1，结合了传统评分方法和环境因素，提供了科学、准确的漏洞风险评估。