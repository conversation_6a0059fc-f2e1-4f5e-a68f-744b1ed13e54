# 天眼告警查询APP - 功能更新总结

## 🔄 更新概述

根据需求，已完成天眼告警查询APP的功能更新，主要变更包括：
- 查询时间范围从"过去1小时"改为"过去5分钟"
- 扩展返回字段从4个增加到8个
- 删除冗余文档和测试文件

## 📊 字段变更对比

### 更新前（4个字段）
1. `access_time` - 监测时间
2. `alarm_sip` - 受害者IP
3. `table_name` - 告警类型
4. `rule_name` - 威胁名称

### 更新后（8个字段）
1. `access_time` - 最近发生时间（保持原字段名）
2. `alarm_sip` - 受害IP（保持原字段名）
3. `attack_sip` - 攻击IP（新增）
4. `table_name` - 告警类型（保持原字段名）
5. `rule_name` - 威胁名称（保持原字段名）
6. `host_state` - 攻击结果（新增）
7. `hazard_level` - 威胁级别（新增）
8. `repeat_count` - 次数（新增）

## 🔧 技术实现变更

### 1. 配置文件更新 (app.json)
- 参数名称：`hours_ago` → `minutes_ago`
- 默认值：`1` → `5`
- 描述：查询1小时 → 查询5分钟

### 2. 核心函数更新 (run.py)
- 函数签名：`query_alerts(..., hours_ago=1, ...)` → `query_alerts(..., minutes_ago=5, ...)`
- 调用方法：`get_alerts_by_time_range()` → `get_alerts_by_minutes_range()`
- 数据格式化：扩展到8个字段

### 3. 查询模块更新 (query_list.py)
- 新增方法：`get_alerts_by_minutes_range()`
- 时间计算：`hours_ago * 60 * 60 * 1000` → `minutes_ago * 60 * 1000`

### 4. 文档更新 (readme.md)
- 更新功能描述
- 更新参数说明
- 更新返回数据格式示例

## 📋 新增字段映射

### 攻击IP (attack_sip)
- 优先级：`attack_sip` > `sip` > `'Unknown'`
- 说明：攻击来源IP地址

### 受害IP (alarm_sip)
- 优先级：`alarm_sip` > `dip` > `'Unknown'`
- 说明：攻击目标IP地址

### 攻击结果 (host_state)
- 字段：`host_state`
- 可能值：成功、企图、失败

### 威胁级别 (hazard_level)
- 字段：`hazard_level`
- 可能值：危急、高危、中危、低危

### 次数 (repeat_count)
- 优先级：`repeat_count` > `count` > `1`
- 说明：攻击重复次数

## 🗂️ 文件清理

已删除的冗余文件：
- `BUG_FIX_REPORT.md`
- `INTEGRATION_SUMMARY.md`
- `usage_example.md`
- `test_app.py`
- `test_type_fix.py`
- `validate_app.py`

保留的核心文件：
- `app.json` - APP配置
- `readme.md` - 功能说明
- `icon.png` - APP图标
- `main/run.py` - 主执行逻辑
- `main/login.py` - 登录模块
- `main/query_list.py` - 查询模块
- `main/alert_detail.py` - 详情模块

## 🔍 返回数据示例

```json
{
  "status": 0,
  "result": {
    "total": 3,
    "alerts": [
      {
        "access_time": "7月21日11:30",
        "alarm_sip": "*************",
        "attack_sip": "*********",
        "table_name": "网页漏洞利用",
        "rule_name": "SQL注入攻击检测",
        "host_state": "成功",
        "hazard_level": "高危",
        "repeat_count": 3
      },
      {
        "access_time": "7月21日11:28",
        "alarm_sip": "************",
        "attack_sip": "************",
        "table_name": "网络攻击",
        "rule_name": "端口扫描检测",
        "host_state": "企图",
        "hazard_level": "中危",
        "repeat_count": 1
      }
    ]
  }
}
```

## ✅ 验证要点

1. **时间范围**：确认查询的是过去5分钟的告警
2. **字段完整性**：确认返回8个字段且命名正确
3. **数据映射**：确认IP地址、攻击结果、威胁级别等字段正确映射
4. **兼容性**：确认与现有SOAR系统的兼容性

## 🚀 部署说明

1. 更新后的APP可直接替换原版本
2. 参数名称已更改，需要更新工作流配置
3. 返回数据结构已扩展，后续处理节点可能需要相应调整

---

**更新状态**: ✅ 已完成  
**测试状态**: ⏳ 待验证  
**部署建议**: 可立即部署使用
