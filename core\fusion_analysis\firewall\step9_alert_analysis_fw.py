import pandas as pd
import numpy as np
import os
from step8_risk_scoring_fw import main as get_risk_scoring_data

def analyze_alert_types(df):
    """
    分析告警类型，根据调整后的威胁分数和结论分配告警级别
    """
    print("正在分析告警类型...")
    
    # 初始化告警级别列
    df['alert_level'] = ''
    
    # 根据调整后的威胁分数和结论分配告警级别
    # 高级别告警
    high_risk_keywords = ['高危', '立即处理', '立即处置']
    df.loc[df['_adjusted_threat_score'] >= 75, 'alert_level'] = '高'
    df.loc[df['conclusion'].str.contains('|'.join(high_risk_keywords)), 'alert_level'] = '高'
    
    # 中级别告警
    medium_risk_keywords = ['中危', '需审核', '需关注', '优先']
    df.loc[(df['_adjusted_threat_score'] >= 50) & (df['_adjusted_threat_score'] < 75), 'alert_level'] = '中'
    df.loc[(df['alert_level'] == '') & (df['conclusion'].str.contains('|'.join(medium_risk_keywords))), 'alert_level'] = '中'
    
    # 低级别告警
    df.loc[df['alert_level'] == '', 'alert_level'] = '低'
    
    # 误报覆盖
    df.loc[df['conclusion'].str.contains('误报'), 'alert_level'] = '低'
    
    return df

def analyze_attack_results(df):
    """
    分析攻击结果，根据调整后的威胁分数、结论和流向判断攻击阶段
    """
    print("正在分析攻击结果...")
    
    # 初始化攻击结果列
    df['attackResult'] = ''
    
    # 失陷判断
    compromise_keywords = ['失陷', '数据外泄', '持续性外部入侵']
    df.loc[df['conclusion'].str.contains('|'.join(compromise_keywords)), 'attackResult'] = '失陷'
    df.loc[(df['_adjusted_threat_score'] >= 85) & 
           (df['event_frequency'] >= 10) & 
           (df['current_run_hours'] >= 5), 'attackResult'] = '失陷'
    
    # 攻击成功判断
    success_keywords = ['攻击成功', '入侵', '高危']
    df.loc[(df['attackResult'] == '') & 
           (df['conclusion'].str.contains('|'.join(success_keywords))), 'attackResult'] = '成功'
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] >= 70) & 
           (df['event_frequency'] >= 5), 'attackResult'] = '成功'
    
    # 企图判断
    attempt_keywords = ['探测', '扫描', '中危']
    df.loc[(df['attackResult'] == '') & 
           (df['conclusion'].str.contains('|'.join(attempt_keywords))), 'attackResult'] = '尝试'
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] >= 50), 'attackResult'] = '尝试'
    
    # 失败判断
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] < 50), 'attackResult'] = '失败'
    
    # 误报覆盖
    df.loc[df['conclusion'].str.contains('误报'), 'attackResult'] = '误报'
    
    return df

def analyze_confidence(df):
    """
    分析置信度，根据多种因素评估告警的可信度
    """
    print("正在分析置信度...")
    
    # 初始化置信度列
    df['confidence'] = ''
    
    # 根据攻击结果判断置信度
    # 成功的攻击置信度为高
    df.loc[df['attackResult'] == '成功', 'confidence'] = '高'
    df.loc[df['attackResult'] == '失陷', 'confidence'] = '高'
    
    # 误报的置信度为高（根据用户要求）
    df.loc[df['attackResult'] == '误报', 'confidence'] = '高'
    
    # 尝试的攻击置信度为低或中（根据威胁分数决定）
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '尝试') & 
           (df['_adjusted_threat_score'] >= 60), 'confidence'] = '中'
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '尝试') & 
           (df['_adjusted_threat_score'] < 60), 'confidence'] = '低'
    
    # 失败的攻击置信度为低
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '失败'), 'confidence'] = '低'
    
    # 根据结论中的关键词判断
    # 高危的置信度为中或高
    df.loc[(df['confidence'] == '') & (df['conclusion'].str.contains('高危')), 'confidence'] = '高'
    
    # 其他因素判断
    # 高置信度判断（其他因素）
    df.loc[(df['confidence'] == '') & 
           ((df['event_frequency'] >= 10) | 
            (df['current_run_hours'] >= 5) | 
            ((df['_adjusted_threat_score'] >= 80) & (df['false_positive_flag'] == False))), 'confidence'] = '高'
    
    # 中置信度判断（其他因素）
    df.loc[(df['confidence'] == '') & 
           ((df['event_frequency'] >= 5) | 
            (df['current_run_hours'] >= 3) | 
            (df['_adjusted_threat_score'] >= 60)), 'confidence'] = '中'
    
    # 低置信度判断（默认）
    df.loc[df['confidence'] == '', 'confidence'] = '低'
    
    # 格式化置信度输出
    df['confidence'] = '置信度：' + df['confidence']
    
    return df

def generate_comprehensive_analysis(df):
    """
    生成综合分析结果
    """
    print("正在生成综合分析结果...")
    
    # 初始化综合分析列
    df['comprehensive_analysis'] = ''
    
    # 根据告警级别、攻击结果和置信度生成综合分析
    for idx, row in df.iterrows():
        alert_level = row['alert_level']
        attack_result = row['attackResult']
        confidence = row['confidence']
        conclusion = row['conclusion']
        
        # 构建综合分析
        analysis = f"{alert_level}级告警 - {attack_result}（{confidence}）\n"
        
        # 添加详细说明
        if alert_level == '高':
            if attack_result == '失陷':
                analysis += "系统已被攻击者控制，需立即隔离并进行应急响应"
            elif attack_result == '成功':
                analysis += "攻击者已成功利用漏洞，需立即处置并评估影响范围"
            else:
                analysis += "高风险安全事件，需立即处理"
        elif alert_level == '中':
            if attack_result == '尝试':
                analysis += "检测到明显的攻击企图，需进一步调查"
            else:
                analysis += "中等风险安全事件，建议及时处理"
        else:  # 低级别
            if attack_result == '误报':
                analysis += "疑似误报，可能是正常业务行为"
            else:
                analysis += "低风险安全事件，建议定期复核"
        
        df.loc[idx, 'comprehensive_analysis'] = analysis
    
    return df

def generate_statistics(df):
    """
    生成统计表并返回统计结果
    """
    print("正在生成统计表...")
    
    # 1. 攻击结果分布
    attack_result_counts = df['attackResult'].value_counts()
    print("\n1. 攻击结果分布:")
    for result, count in attack_result_counts.items():
        print(f"{result}: {count}条记录")
    
    # 2. 置信度分布
    # 提取置信度中的高/中/低部分进行统计
    df['confidence_level'] = df['confidence'].str.replace('置信度：', '')
    confidence_counts = df['confidence_level'].value_counts()
    print("\n2. 置信度分布:")
    for conf, count in confidence_counts.items():
        print(f"{conf}置信度: {count}条记录")
    
    # 3. 攻击结果与置信度交叉表
    attack_confidence_cross = pd.crosstab(df['attackResult'], df['confidence_level'])
    print("\n3. 攻击结果与置信度交叉表:")
    print(attack_confidence_cross)
    
    # 构建统计结果字典
    statistics_result = {
        'attack_result_counts': attack_result_counts,
        'confidence_counts': confidence_counts,
        'attack_confidence_cross': attack_confidence_cross
    }
    
    # 删除临时列
    if 'confidence_level' in df.columns:
        df = df.drop('confidence_level', axis=1)
    
    return df, statistics_result

def main():
    # 调用step8模块获取风险评分数据
    print("调用step8模块获取风险评分数据...")
    result = get_risk_scoring_data()
    
    if result is None:
        print("错误: step8模块未返回有效数据")
        return None, None
    
    # 解包结果
    df, step8_statistics = result
    
    if df is None or df.empty:
        print("错误: step8模块返回的数据为空")
        return None, None
    
    print(f"从step8获取到 {len(df)} 条记录")
    
    # 检查必需列是否存在
    required_columns = ['hour_slot', 'srcAddress', 'flow_directions', '_adjusted_threat_score', 
                       'conclusion', 'event_frequency', 'current_run_hours', 'false_positive_flag']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 输入文件缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return None, None
    
    # 处理flow_directions列（如果是字符串形式）
    if df['flow_directions'].dtype == 'object' and not isinstance(df['flow_directions'].iloc[0], list):
        df['flow_directions'] = df['flow_directions'].apply(eval)  # 将字符串转换为列表
    
    # 分析攻击结果
    df = analyze_attack_results(df)
    
    # 分析置信度
    df = analyze_confidence(df)
    
    # 生成统计表
    df, step9_statistics = generate_statistics(df)
    
    # 删除不需要的列
    if 'alert_level' in df.columns:
        df = df.drop('alert_level', axis=1)
    if 'comprehensive_analysis' in df.columns:
        df = df.drop('comprehensive_analysis', axis=1)

    return df, step9_statistics
    

if __name__ == "__main__":
    main()