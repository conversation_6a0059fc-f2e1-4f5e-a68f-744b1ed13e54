# 绿盟漏洞扫描器 W5 SOAR 应用

基于绿盟漏洞扫描系统API的W5 SOAR应用，提供完整的扫描任务管理和漏洞查询功能。

## 功能特性

### 🔍 核心功能
- **获取所有扫描任务** - 自动分页获取所有扫描任务列表
- **获取任务漏洞信息** - 查询指定任务的详细漏洞信息
- **检查新完成任务** - 智能检测新完成的任务（包括子任务）

### 🚀 技术特点
- **自动登录认证** - 支持验证码自动识别（基于ddddocr）
- **会话管理** - 智能缓存登录状态，避免频繁认证
- **子任务支持** - 完整支持父子任务关系检测
- **状态持久化** - 本地JSON文件保存任务状态
- **容错处理** - 完善的错误处理和重试机制

## 系统要求

### 环境依赖
- Python 3.7+
- W5 SOAR 平台

### Python依赖包
```
requests>=2.25.0
loguru>=0.6.0
ddddocr>=1.4.0  # 可选，用于验证码识别
```

## 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd nsfocus_vuln_scanner
```

### 2. 安装依赖
```bash
pip install requests loguru ddddocr
```

### 3. 配置参数
编辑 `nsfocus_vuln_scanner/main/run.py` 中的连接参数：
```python
# 修改绿盟系统地址
login_manager = GreenLeagueLogin(host="your-greenleague-host")
```

### 4. 部署到W5 SOAR
将整个 `nsfocus_vuln_scanner` 目录复制到W5 SOAR应用目录中。

## 使用说明

### W5 SOAR 平台使用

#### 1. 获取所有扫描任务
- **功能**: 获取绿盟系统中的所有扫描任务
- **参数**: 无
- **返回**: 任务列表，包含任务ID、名称、状态、风险等级等信息

#### 2. 获取任务漏洞信息
- **功能**: 获取指定任务的详细漏洞信息
- **参数**: 
  - `task_id` (必填): 任务ID
- **返回**: 漏洞列表和统计信息

#### 3. 检查新完成任务
- **功能**: 检查自上次调用以来新完成的任务
- **参数**: 无
- **返回**: 新完成任务的数量和详细信息

### 本地测试
```bash
cd nsfocus_vuln_scanner/main
python run.py
```

## API接口说明

### 任务状态码
- `3`: 等待中
- `9`: 暂停  
- `15`: 已完成

### 漏洞等级
- `high`: 高危
- `middle`: 中危
- `low`: 低危

### 返回格式
所有接口统一返回格式：
```json
{
  "status": 0,  // 0=成功, 1=失败
  "result": {
    // 具体数据
  }
}
```

## 配置文件

### app.json
W5 SOAR应用配置文件，定义了应用的基本信息和可用操作。

### last_completed_tasks.json
本地状态文件，保存上次检查时已完成的任务ID列表：
```json
{
  "completed_task_ids": [12345, 12346],
  "last_check_time": "2024-01-01T12:00:00.000000",
  "total_count": 2
}
```

## 日志管理

应用使用loguru进行日志管理：
- 日志文件: `nsfocus_vuln_scanner.log`
- 自动轮转: 10MB
- 日志级别: INFO

## 故障排除

### 常见问题

#### 1. 登录失败
- 检查网络连接
- 确认绿盟系统地址正确
- 验证用户名密码

#### 2. 验证码识别失败
- 确保已安装ddddocr库
- 检查验证码图片质量

#### 3. 任务获取失败
- 确认登录状态正常
- 检查API接口权限

#### 4. 子任务获取异常
- 正常现象，系统会自动跳过无子任务的任务
- 查看debug日志了解详情

### 调试模式
修改日志级别为DEBUG以获取更详细的信息：
```python
logger.add("nsfocus_vuln_scanner.log", rotation="10 MB", level="DEBUG")
```

## 开发说明

### 项目结构
```
nsfocus_vuln_scanner/
├── app.json              # W5 SOAR应用配置
├── main/
│   └── run.py           # 主程序文件
├── last_completed_tasks.json  # 状态文件（运行时生成）
└── README.md            # 说明文档
```

### 核心类说明

#### GreenLeagueLogin
- 负责绿盟系统的登录认证
- 支持验证码自动识别
- 提供会话管理功能

#### TaskManager  
- 任务管理核心类
- 提供任务列表获取、漏洞查询等功能
- 支持子任务递归获取

### 扩展开发
如需添加新功能：
1. 在TaskManager类中添加新方法
2. 在run.py中添加对应的W5 SOAR函数
3. 更新app.json配置文件

## 版本历史

### v2.0
- 重构代码架构，移除MCP依赖
- 新增子任务支持
- 实现状态持久化
- 优化错误处理机制

### v1.0
- 基础功能实现
- 支持任务查询和漏洞获取

## 许可证

本项目采用 MIT 许可证。

## 详细功能说明

### 获取所有扫描任务

**接口**: `get_all_scan_tasks()`

**功能描述**:
- 自动分页获取绿盟系统中的所有扫描任务
- 支持大数据量处理（单次最多获取1000万条记录）
- 自动处理分页逻辑

**返回数据示例**:
```json
{
  "status": 0,
  "result": {
    "data": [
      {
        "任务ID": 12345,
        "任务名称": "Web安全扫描",
        "任务类型": "Web扫描",
        "任务状态": "已完成",
        "任务整体风险等级": "比较危险",
        "创建时间": "2024-01-01 10:00:00",
        "开始时间": "2024-01-01 10:30:00",
        "结束时间": "2024-01-01 11:30:00",
        "漏洞统计": {
          "high_count": 5,
          "middle_count": 10,
          "low_count": 3
        }
      }
    ],
    "total_tasks": 1,
    "host": "************",
    "message": "成功获取1个扫描任务"
  }
}
```

### 获取任务漏洞信息

**接口**: `get_task_vulnerabilities(task_id)`

**参数**:
- `task_id`: 任务ID（必填）

**功能描述**:
- 获取指定任务的详细漏洞信息
- 自动按漏洞名称和严重程度排序
- 提供漏洞统计和主机影响分析

**返回数据示例**:
```json
{
  "status": 0,
  "result": {
    "data": [
      {
        "漏洞名称": "SQL注入漏洞",
        "漏洞等级": "高危",
        "影响主机数量": 3,
        "漏洞描述": "存在SQL注入风险...",
        "修复建议": "建议使用参数化查询..."
      }
    ],
    "task_id": "12345",
    "total_vulns": 18,
    "漏洞统计": {
      "高危": 5,
      "中危": 10,
      "低危": 3,
      "总数": 18
    },
    "受影响主机数量": 15,
    "message": "成功获取任务 12345 的 18 个漏洞信息"
  }
}
```

### 检查新完成任务

**接口**: `check_new_completed_tasks()`

**功能描述**:
- 智能检测自上次检查以来新完成的任务
- 支持主任务和子任务的完整检测
- 状态持久化到本地JSON文件
- 提供详细的任务完成信息

**工作原理**:
1. 从本地文件加载上次已完成任务列表
2. 获取当前所有任务及其子任务
3. 筛选出状态为15（已完成）的任务
4. 对比找出新完成的任务
5. 保存最新状态到本地文件

**返回数据示例**:
```json
{
  "status": 0,
  "result": {
    "total_tasks": 2,
    "new_completed_tasks": [
      {
        "任务ID": 12347,
        "任务名称": "内网安全扫描",
        "任务类型": "评估任务",
        "是否为子任务": false,
        "父任务ID": null,
        "任务创建时间": "2024-01-01 09:00:00",
        "任务开始时间": "2024-01-01 09:30:00",
        "扫描结束时间": "2024-01-01 10:30:00",
        "风险等级": "比较危险",
        "漏洞统计": {
          "high_count": 3,
          "middle_count": 8,
          "low_count": 2
        },
        "执行时间": "1小时"
      }
    ],
    "current_total_completed": 25,
    "previous_total_completed": 23,
    "host": "************",
    "check_time": "2024-01-01T12:00:00.000000",
    "message": "发现 2 个新完成的任务"
  }
}
```

## 高级配置

### 自定义登录参数
```python
# 在run.py中修改登录配置
login_manager = GreenLeagueLogin(
    host="your-host",
    username="your-username",  # 可选，默认从环境变量获取
    password="your-password"   # 可选，默认从环境变量获取
)
```

### 调整缓存时间
```python
# 修改登录缓存时间（秒）
LOGIN_CACHE_DURATION = 3600  # 默认1小时
```

### 自定义状态文件路径
```python
# 修改状态文件路径
COMPLETED_TASKS_FILE = "/path/to/your/state.json"
```

## 性能优化建议

### 1. 合理设置检查频率
- 建议每5-10分钟检查一次新完成任务
- 避免过于频繁的API调用

### 2. 网络优化
- 确保与绿盟系统的网络连接稳定
- 考虑在同一网络环境部署

### 3. 日志管理
- 定期清理日志文件
- 根据需要调整日志级别

## 安全注意事项

### 1. 凭据管理
- 不要在代码中硬编码用户名密码
- 建议使用环境变量或配置文件

### 2. 网络安全
- 使用HTTPS连接（如果绿盟系统支持）
- 考虑使用VPN或专用网络

### 3. 权限控制
- 确保使用的账户具有适当的API访问权限
- 定期更新访问凭据

## 技术支持

如有问题或建议，请联系开发团队。

### 常用调试命令
```bash
# 查看日志
tail -f nsfocus_vuln_scanner.log

# 检查状态文件
cat last_completed_tasks.json

# 测试连接
python -c "from main.run import *; import asyncio; asyncio.run(get_all_scan_tasks())"
```
