from . import r
from flask import request
import sys
import os
import pandas as pd
import json

qianxin_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'QiAnXin')
if qianxin_path not in sys.path:
    sys.path.append(qianxin_path)

try:
    from step5_temp import main as get_threat_scores
except ImportError as e:
    print(f"导入step5_temp模块失败: {e}")
    get_threat_scores = None

try:
    from step6_false_positive import main as get_false_positive_analysis
    from step6_false_positive import get_known_false_positive_list_with_details, update_known_false_positive_ips, delete_known_false_positive_ip
    from step6_false_positive import get_trusted_source_whitelist_with_details, update_trusted_source_whitelist, delete_trusted_source_whitelist_ip
except ImportError as e:
    print(f"导入step6_false_positive模块失败: {e}")
    get_false_positive_analysis = None
    get_known_false_positive_list_with_details = None
    update_known_false_positive_ips = None
    delete_known_false_positive_ip = None
    get_trusted_source_whitelist_with_details = None
    update_trusted_source_whitelist = None
    delete_trusted_source_whitelist_ip = None

try:
    from step8_risk_scoring import main as get_risk_scoring_analysis
except ImportError as e:
    print(f"导入step8_risk_scoring模块失败: {e}")
    get_risk_scoring_analysis = None

try:
    from step9_alert_analysis2 import main as get_alert_end_analysis
except ImportError as e:
    print(f"导入step9_alert_analysis2模块失败: {e}")
    get_alert_end_analysis = None

@r.route("/test/hello", methods=['GET'])
def test_hello():
    return {"msg": "hello", "code": 200}

@r.route("/qiAnXin/getIpThreatScoreList", methods=['GET'])
def get_ip_threat_score_list():
    """
    获取IP威胁评分列表接口（支持分页）
    参数:
    - page: 页码，默认为1
    - size: 每页数量，默认为10
    """
    try:
        if get_threat_scores is None:
            return {
                "code": 9001,
                "msg": "威胁评分模块未正确加载"
            }
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1:
            size = 10
        if size > 1000:  # 限制最大每页数量
            size = 1000

        # 调用step5_temp的main函数获取威胁评分数据
        result_df = get_threat_scores()
        
        if result_df is None or result_df.empty:
            return {
                "code": 9001,
                "msg": "未获取到威胁评分数据",
                "data": {}
            }
        
        # 将DataFrame转换为字典列表
        data_list = result_df.to_dict('records')
        
        # 处理数据格式，确保所有值都是JSON可序列化的
        for item in data_list:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
                elif hasattr(value, 'item'):  # numpy类型转换
                    item[key] = value.item()
                elif str(type(value)).startswith('<class \'pandas'):
                    item[key] = str(value)
        
        # 计算分页信息
        total_count = len(data_list)
        total_page = (total_count + size - 1) // size  # 向上取整
        
        # 计算分页数据的起始和结束索引
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 获取当前页的数据
        page_data = data_list[start_index:end_index]
        
        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        }
        
    except ValueError as e:
        return {
            "code": 9002,
            "msg": "参数格式错误，page和size必须为正整数"
        }
    except Exception as e:
        print(f"获取IP威胁评分列表失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getFalsePositiveResults", methods=['GET'])
def get_false_positive_results():
    """
    获取误报分析结果接口（支持分页）
    参数:
    - page: 页码，默认为1
    - size: 每页数量，默认为10
    """
    try:
        if get_false_positive_analysis is None:
            return {
                "code": 9001,
                "msg": "误报分析模块未正确加载"
            }
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1:
            size = 10
        if size > 1000:  # 限制最大每页数量
            size = 1000

        # 调用step6_false_positive的main函数获取误报分析数据
        result = get_false_positive_analysis()
        
        if result is None:
            return {
                "code": 9001,
                "msg": "未获取到误报分析数据",
                "data": {}
            }
        
        # 解包返回结果
        fp_df, known_false_positive_ips = result
        
        if fp_df is None or fp_df.empty:
            return {
                "code": 9001,
                "msg": "未获取到误报分析数据",
                "data": {}
            }
        
        # 将DataFrame转换为字典列表
        data_list = fp_df.to_dict('records')
        
        # 处理数据格式，确保所有值都是JSON可序列化的
        for item in data_list:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
                elif hasattr(value, 'item'):  # numpy类型转换
                    item[key] = value.item()
                elif str(type(value)).startswith('<class \'pandas'):
                    item[key] = str(value)
        
        # 计算分页信息
        total_count = len(data_list)
        total_page = (total_count + size - 1) // size  # 向上取整
        
        # 计算分页数据的起始和结束索引
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 获取当前页的数据
        page_data = data_list[start_index:end_index]
        
        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        }
        
    except ValueError as e:
        return {
            "code": 9002,
            "msg": "参数格式错误，page和size必须为正整数"
        }
    except Exception as e:
        print(f"获取误报分析结果失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getKnowFalsePositiveList", methods=['GET'])
def get_know_false_positive_list():
    """
    获取已知误报IP列表接口（支持分页）
    参数:
    - page: 页码，默认为1
    - size: 每页数量，默认为10
    """
    try:
        if get_false_positive_analysis is None:
            return {
                "code": 9001,
                "msg": "误报分析模块未正确加载"
            }
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1:
            size = 10
        if size > 1000:  # 限制最大每页数量
            size = 1000

        # 调用step6_false_positive的main函数获取误报分析数据
        result = get_false_positive_analysis()
        
        if result is None:
            return {
                "code": 9001,
                "msg": "未获取到误报分析数据",
                "data": {}
            }
        
        # 解包返回结果，获取已知误报IP列表
        fp_df, known_false_positive_ips = result
        
        if known_false_positive_ips is None:
            return {
                "code": 9001,
                "msg": "未获取到已知误报IP列表",
                "data": {}
            }
        
        # 将已知误报IP集合转换为列表格式
        ip_list = []
        for ip in known_false_positive_ips:
            ip_list.append({
                "ip_address": ip,
                "description": "已知误报IP",
                "type": "known_false_positive"
            })
        
        # 计算分页信息
        total_count = len(ip_list)
        total_page = (total_count + size - 1) // size  # 向上取整
        
        # 计算分页数据的起始和结束索引
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 获取当前页的数据
        page_data = ip_list[start_index:end_index]
        
        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        }
        
    except ValueError as e:
        return {
            "code": 9002,
            "msg": "参数格式错误，page和size必须为正整数"
        }
    except Exception as e:
        print(f"获取已知误报IP列表失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/updateKnowFalsePositiveList", methods=['PUT'])
def update_know_false_positive_list():
    """
    更新已知误报IP列表接口
    请求方法: PUT
    请求参数: JSON数组格式的IP地址列表
    返回格式: {"code": 9001, "msg": "Success", "data": {}}
    """
    try:
        # 检查模块是否导入成功
        if update_known_false_positive_ips is None:
            return {
                "code": 500,
                "msg": "step6_false_positive模块导入失败",
                "data": {}
            }
        
        # 获取请求数据
        if not request.is_json:
            return {
                "code": 400,
                "msg": "请求数据必须是JSON格式",
                "data": {}
            }
        
        ip_addresses = request.get_json()
        
        # 验证请求数据格式
        if not isinstance(ip_addresses, list):
            return {
                "code": 400,
                "msg": "请求数据必须是IP地址数组",
                "data": {}
            }
        
        # 验证IP地址格式（简单验证）
        import re
        ip_pattern = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
        
        valid_ips = []
        invalid_ips = []
        
        for ip in ip_addresses:
            if isinstance(ip, str) and ip_pattern.match(ip.strip()):
                valid_ips.append(ip.strip())
            else:
                invalid_ips.append(str(ip))
        
        # 如果有无效IP，返回错误
        if invalid_ips:
            return {
                "code": 400,
                "msg": f"以下IP地址格式无效: {', '.join(invalid_ips)}",
                "data": {}
            }
        
        # 如果没有有效IP，返回错误
        if not valid_ips:
            return {
                "code": 400,
                "msg": "没有有效的IP地址",
                "data": {}
            }
        
        # 调用更新函数
        success = update_known_false_positive_ips(valid_ips)
        
        if success:
            return {
                "code": 9001,
                "msg": "Success",
                "data": {
                    "updated_ips": valid_ips,
                    "count": len(valid_ips)
                }
            }
        else:
            return {
                "code": 500,
                "msg": "更新已知误报IP列表失败",
                "data": {}
            }
    
    except json.JSONDecodeError:
        return {
            "code": 400,
            "msg": "JSON格式错误",
            "data": {}
        }
    except Exception as e:
        print(f"更新已知误报IP列表时发生错误: {str(e)}")
        return {
            "code": 500,
            "msg": "服务器内部错误",
            "data": {}
        }

@r.route("/qiAnXin/deleteKnowFalsePositiveListByIp", methods=['DELETE'])
def delete_know_false_positive_list_by_ip():
    """
    根据IP地址删除已知误报列表中的记录
    参数:
    - ip: IP地址（通过查询参数传递）
    """
    try:
        if delete_known_false_positive_ip is None:
            return {
                "code": 500,
                "msg": "误报分析模块未正确加载",
                "data": {}
            }
        
        # 获取IP参数
        ip_address = request.args.get('ip', '').strip()
        
        # 参数验证
        if not ip_address:
            return {
                "code": 400,
                "msg": "IP地址参数不能为空",
                "data": {}
            }
        
        # IP格式验证
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        if not re.match(ip_pattern, ip_address):
            return {
                "code": 400,
                "msg": f"IP地址格式无效: {ip_address}",
                "data": {}
            }
        
        # 调用删除函数
        success, deleted_count = delete_known_false_positive_ip(ip_address)
        
        if success:
            if deleted_count > 0:
                return {
                    "code": 9001,
                    "msg": "Success",
                    "data": {
                        "deleted_ip": ip_address,
                        "deleted_count": deleted_count
                    }
                }
            else:
                return {
                    "code": 404,
                    "msg": f"IP地址 {ip_address} 不存在于已知误报列表中",
                    "data": {
                        "ip": ip_address,
                        "deleted_count": 0
                    }
                }
        else:
            return {
                "code": 500,
                "msg": "删除已知误报IP失败",
                "data": {}
            }
    
    except Exception as e:
        print(f"删除已知误报IP时发生错误: {str(e)}")
        return {
            "code": 500,
            "msg": "服务器内部错误",
            "data": {}
        }

@r.route("/qiAnXin/getRiskScoringsList", methods=['GET'])
def get_risk_scorings_list():
    """
    获取风险评分列表接口（支持分页）
    参数:
    - page: 页码，默认为1
    - size: 每页数量，默认为10
    """
    try:
        if get_risk_scoring_analysis is None:
            return {
                "code": 9001,
                "msg": "风险评分模块未正确加载"
            }
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))

        # 参数验证
        if page < 1:
            page = 1
        if size < 1:
            size = 10
        if size > 1000:  # 限制最大每页数量
            size = 1000
        # 调用step8_risk_scoring的main函数获取风险评分数据
        result_df, statistics_result = get_risk_scoring_analysis()
        if result_df is None or result_df.empty:
            return {
                "code": 9001,
                "msg": "未获取到风险评分数据",
                "data": {}
            }
        # 将DataFrame转换为字典列表
        data_list = result_df.to_dict('records')
        # 计算分页信息
        total_count = len(data_list)
        total_page = (total_count + size - 1) // size  # 向上取整
        # 计算分页数据的起始和结束索引
        start_index = (page - 1) * size
        end_index = start_index + size
        # 获取当前页的数据
        page_data = data_list[start_index:end_index]

        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        }

    except ValueError as e:
        print(f"参数格式错误: {str(e)}")
        return {
            "code": 9002,
            "msg": "参数格式错误，page和size必须为正整数"
        }
    except Exception as e:
        print(f"获取风险评分列表失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getAllConclusion", methods=['GET'])
def get_all_conclusion():
    """
    获取所有总结信息接口
    返回包含统计结果的完整信息
    """
    try:
        if get_risk_scoring_analysis is None:
            return {
                "code": 9001,
                "msg": "风险评分模块未正确加载"
            }

        # 调用step8_risk_scoring的main函数获取统计结果
        result_df, statistics_result = get_risk_scoring_analysis()

        if statistics_result is None:
            return {
                "code": 9001,
                "msg": "未获取到统计结果数据",
                "data": {}
            }

        # 处理统计结果，确保所有值都是JSON可序列化的
        def convert_to_serializable(obj):
            if hasattr(obj, 'to_dict'):
                return obj.to_dict()
            elif hasattr(obj, 'to_json'):
                return json.loads(obj.to_json())
            elif pd.api.types.is_numeric_dtype(type(obj)):
                return obj.item() if hasattr(obj, 'item') else obj
            elif isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_to_serializable(item) for item in obj]
            else:
                return str(obj)

        # 转换统计结果为可序列化格式
        serializable_stats = {}
        for key, value in statistics_result.items():
            serializable_stats[key] = convert_to_serializable(value)

        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "conclusion_counts": serializable_stats.get('conclusion_counts', {}),
                "flow_conclusion": serializable_stats.get('flow_conclusion', {}),
                "business_conclusion": serializable_stats.get('business_conclusion', {}),
                "work_time_conclusion": serializable_stats.get('work_time_conclusion', {}),
                "frequency_conclusion": serializable_stats.get('frequency_conclusion', {})
            }
        }

    except Exception as e:
        print(f"获取统计结果失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getAlertEndAnalysisResultsList", methods=['GET'])
def get_alert_end_analysis_results_list():
    """
    获取告警最终分析结果列表接口（支持分页）
    参数:
    - page: 页码，默认为1
    - size: 每页数量，默认为10
    """
    try:
        if get_alert_end_analysis is None:
            return {
                "code": 9001,
                "msg": "告警最终分析模块未正确加载"
            }
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1:
            size = 10
        if size > 1000:  # 限制最大每页数量
            size = 1000

        # 调用step9_alert_analysis2的main函数获取告警最终分析数据
        result_df, statistics_result = get_alert_end_analysis()
        
        if result_df is None or result_df.empty:
            return {
                "code": 9001,
                "msg": "未获取到告警最终分析数据",
                "data": {}
            }
        
        # 将DataFrame转换为字典列表
        data_list = result_df.to_dict('records')
        
        # # 处理数据格式，确保所有值都是JSON可序列化的
        # for item in data_list:
        #     for key, value in item.items():
        #         if pd.isna(value):
        #             item[key] = None
        #         elif hasattr(value, 'item'):  # numpy类型转换
        #             item[key] = value.item()
        #         elif str(type(value)).startswith('<class \'pandas'):
        #             item[key] = str(value)

        # 计算分页信息
        total_count = len(data_list)
        total_page = (total_count + size - 1) // size  # 向上取整
        
        # 计算分页数据的起始和结束索引
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 获取当前页的数据
        page_data = data_list[start_index:end_index]
        
        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "page": page,
                "page_size": size,
                "total_page": total_page,
                "total_count": total_count,
                "list": page_data
            }
        }
        
    except ValueError as e:
        return {
            "code": 9002,
            "msg": "参数格式错误，page和size必须为正整数"
        }
    except Exception as e:
        print(f"获取告警最终分析结果列表失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getEndCountResults", methods=['GET'])
def get_end_count_results():
    """
    获取告警最终分析统计结果接口
    返回包含attack_result_counts、confidence_counts、attack_confidence_cross的统计信息
    """
    try:
        if get_alert_end_analysis is None:
            return {
                "code": 9001,
                "msg": "告警最终分析模块未正确加载"
            }

        # 调用step9_alert_analysis2的main函数获取统计结果
        result_df, statistics_result = get_alert_end_analysis()

        if statistics_result is None:
            return {
                "code": 9001,
                "msg": "未获取到统计结果数据",
                "data": {}
            }

        # 处理统计结果，确保所有值都是JSON可序列化的
        def convert_to_serializable(obj):
            if hasattr(obj, 'to_dict'):
                return obj.to_dict()
            elif hasattr(obj, 'to_json'):
                return json.loads(obj.to_json())
            elif pd.api.types.is_numeric_dtype(type(obj)):
                return obj.item() if hasattr(obj, 'item') else obj
            elif isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_to_serializable(item) for item in obj]
            else:
                return str(obj)

        # 转换统计结果为可序列化格式
        serializable_stats = {}
        for key, value in statistics_result.items():
            serializable_stats[key] = convert_to_serializable(value)

        return {
            "code": 0,
            "msg": "Success",
            "data": {
                "attack_result_counts": serializable_stats.get('attack_result_counts', {}),
                "confidence_counts": serializable_stats.get('confidence_counts', {}),
                "attack_confidence_cross": serializable_stats.get('attack_confidence_cross', {})
            }
        }

    except Exception as e:
        print(f"获取告警最终分析统计结果失败: {str(e)}")
        return {
            "code": 9001,
            "msg": f"未知错误: {str(e)}"
        }

@r.route("/qiAnXin/getTrustedSourceWhiteList", methods=['GET'])
def get_trusted_source_white_list():
    """
    获取可信源白名单列表接口
    请求方法: GET
    请求参数: page (可选，默认1), size (可选，默认10)
    返回格式: {"code": 9001, "msg": "Success", "data": {"list": [...], "total": 100}}
    """
    try:
        # 检查模块是否导入成功
        if get_trusted_source_whitelist_with_details is None:
            return {
                "code": 500,
                "msg": "step6_false_positive模块导入失败",
                "data": {}
            }
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)
        
        # 验证分页参数
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 10
        
        # 获取完整的白名单列表
        all_whitelist_data = get_trusted_source_whitelist_with_details()
        
        # 计算总数
        total = len(all_whitelist_data)
        
        # 实现分页逻辑
        start_index = (page - 1) * size
        end_index = start_index + size
        
        # 为每个IP添加ID和格式化时间
        formatted_data = []
        for i, item in enumerate(all_whitelist_data, 1):
            formatted_item = {
                "id": i,
                "ip_address": item.get('ip_address', ''),
                "source_type": "默认" if item.get('type', '').startswith('internal') else "用户添加",
                "add_time": "2024-01-15 10:30:00",  # 默认时间，实际应该从数据中获取
                "description": item.get('description', '')
            }
            formatted_data.append(formatted_item)
        
        # 获取分页数据
        paginated_data = formatted_data[start_index:end_index]
        
        return {
            "code": 9001,
            "msg": "Success",
            "data": {
                "list": paginated_data,
                "total": total,
                "page": page,
                "size": size
            }
        }
    
    except Exception as e:
        print(f"获取可信源白名单列表时发生错误: {str(e)}")
        return {
            "code": 500,
            "msg": "服务器内部错误",
            "data": {}
        }


@r.route("/qiAnXin/updateWhileList", methods=['PUT'])
def update_white_list():
    """
    更新可信源白名单接口
    请求方法: PUT
    请求参数: JSON数组格式的IP地址列表
    返回格式: {"code": 9001, "msg": "Success", "data": {}}
    """
    try:
        # 检查模块是否导入成功
        if update_trusted_source_whitelist is None:
            return {
                "code": 500,
                "msg": "step6_false_positive模块导入失败",
                "data": {}
            }
        
        # 获取请求数据
        if not request.is_json:
            return {
                "code": 400,
                "msg": "请求数据必须是JSON格式",
                "data": {}
            }
        
        ip_addresses = request.get_json()
        
        # 验证请求数据格式
        if not isinstance(ip_addresses, list):
            return {
                "code": 400,
                "msg": "请求数据必须是IP地址数组",
                "data": {}
            }
        
        # 验证IP地址格式（简单验证）
        import re
        ip_pattern = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
        
        valid_ips = []
        invalid_ips = []
        
        for ip in ip_addresses:
            if isinstance(ip, str) and ip_pattern.match(ip.strip()):
                valid_ips.append(ip.strip())
            else:
                invalid_ips.append(str(ip))
        
        # 如果有无效IP，返回错误
        if invalid_ips:
            return {
                "code": 400,
                "msg": f"以下IP地址格式无效: {', '.join(invalid_ips)}",
                "data": {}
            }
        
        # 如果没有有效IP，返回错误
        if not valid_ips:
            return {
                "code": 400,
                "msg": "没有有效的IP地址",
                "data": {}
            }
        
        # 调用更新函数
        success = update_trusted_source_whitelist(valid_ips)
        
        if success:
            return {
                "code": 9001,
                "msg": "Success",
                "data": {
                    "updated_ips": valid_ips,
                    "count": len(valid_ips)
                }
            }
        else:
            return {
                "code": 500,
                "msg": "更新可信源白名单失败",
                "data": {}
            }
    
    except json.JSONDecodeError:
        return {
            "code": 400,
            "msg": "JSON格式错误",
            "data": {}
        }
    except Exception as e:
        print(f"更新可信源白名单时发生错误: {str(e)}")
        return {
            "code": 500,
            "msg": "服务器内部错误",
            "data": {}
        }

@r.route("/qiAnXin/deleteWhileListByIp", methods=['DELETE'])
def delete_while_list_by_ip():
    """
    根据IP地址删除可信源白名单接口
    请求方法: DELETE
    请求参数: ?ip=xx.xx.xx.xx
    返回格式: {"code": 9001, "msg": "Success", "data": {}}
    """
    try:
        # 检查函数是否可用
        if delete_trusted_source_whitelist_ip is None:
            return {
                "code": 500,
                "msg": "删除可信源白名单功能不可用",
                "data": {}
            }
        # 获取IP参数
        ip_address = request.args.get('ip', '').strip()
        # 验证IP参数
        if not ip_address:
            return {
                "code": 400,
                "msg": "缺少IP参数",
                "data": {}
            }
        # 验证IP格式
        import re
        ip_pattern = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
        if not ip_pattern.match(ip_address):
            return {
                "code": 400,
                "msg": "IP地址格式无效",
                "data": {}
            }
        # 调用删除函数
        success, deleted_count = delete_trusted_source_whitelist_ip(ip_address)
        if success:
            if deleted_count > 0:
                return {
                    "code": 9001,
                    "msg": "Success",
                    "data": {}
                }
            else:
                return {
                    "code": 404,
                    "msg": "指定的IP地址不存在于可信源白名单中",
                    "data": {}
                }

        else:
            return {
                "code": 500,
                "msg": "删除可信源白名单失败",
                "data": {}
            }
    
    except Exception as e:
        print(f"删除可信源白名单时发生错误: {str(e)}")
        return {
            "code": 500,
            "msg": "服务器内部错误",
            "data": {}
        }
