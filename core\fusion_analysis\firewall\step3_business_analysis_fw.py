import pandas as pd
import random
import os
from step2_flow_direction_fw import main as get_flow_direction_data

def analyze_business_relevance(df):
    """
    分析业务相关性
    预置两套列表：业务相关池 vs 常见互联网池
    """
    print("正在分析业务相关性...")
    
    # 预置业务相关池
    business_related_patterns = [
        "Prometheus", "电力监控", "monitor", "power", "energy", "dashboard", 
        "panel", "control", "system", "internal", "业务", "监控", "电力", "能源"
    ]
    
    # 预置常见互联网池
    common_internet_patterns = [
        "Mozilla", "Chrome", "Firefox", "Safari", "Edge", "Google", "Baidu", 
        "Bing", "Yahoo", "public", "cdn", "api", "www", "http", "https"
    ]
    
    # 生成HTTP请求头和业务相关性
    def generate_http_info(row):
        # 仅当流向是"内到外"，且70%概率时，认定为业务相关
        is_business = row['flow_direction'] == "内到外" and random.random() < 0.7
        
        if is_business:
            # 生成业务相关的HTTP头
            user_agent = f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) {random.choice(business_related_patterns)}/1.0"
            http_host = f"{random.choice(business_related_patterns).lower()}.internal.example.com"
            referer = f"https://{random.choice(business_related_patterns).lower()}.internal.example.com/dashboard"
            origin = f"https://{random.choice(business_related_patterns).lower()}.internal.example.com"
        else:
            # 生成常见互联网HTTP头
            user_agent = f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) {random.choice(common_internet_patterns)}/1.0"
            http_host = f"{random.choice(common_internet_patterns).lower()}.example.com"
            referer = f"https://{random.choice(common_internet_patterns).lower()}.example.com/page"
            origin = f"https://{random.choice(common_internet_patterns).lower()}.example.com"
        
        return pd.Series({
            'user_agent': user_agent,
            'http_host': http_host,
            'referer': referer,
            'origin': origin,
            'is_business_related': is_business
        })
    
    # 应用函数生成HTTP信息和业务相关性
    http_info = df.apply(generate_http_info, axis=1)
    df = pd.concat([df, http_info], axis=1)
    
    # 统计业务相关性
    business_counts = df['is_business_related'].value_counts()
    print("\n业务相关性统计:")
    print(f"业务相关: {business_counts.get(True, 0)}条记录")
    print(f"非业务相关: {business_counts.get(False, 0)}条记录")
    
    return df

def main():
    # 从step2_flow_direction获取处理后的数据
    print("从step2_flow_direction获取处理后的数据...")
    df = get_flow_direction_data()
    
    if df is None or df.empty:
        print("错误: 未获取到任何数据")
        return
    
    # 检查必需列是否存在
    required_columns = ['srcAddress', 'destAddress', 'time', 'id', 'flow_direction']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return
    
    # 处理时间列
    df['time'] = pd.to_datetime(df['time'], errors='coerce')
    # 丢弃时间为空的行
    df = df.dropna(subset=['time'])
    
    # 分析业务相关性
    df = analyze_business_relevance(df)
    # 保存结果
    # output_file = "step3_business_analysis.csv"
    # df.to_csv(output_file, index=False)
    # print(f"\n结果已保存至 {output_file}")
    print(df.head(10))
    return df

if __name__ == "__main__":
    main()