# windows_agent.py (线程锁)

from flask import Flask, jsonify, request
import subprocess
import json
import os
import sys
import threading # 1. 导入线程库

# --- 全局配置区 ---
S6000_SCRIPT_PATH = "C:\\Users\\<USER>\\Desktop\\s60000_2.0\\s60000_selenium.py"
TASK_MAP = {
    "工作通知": "-n",
    "工作任务": "-t",
    "工作联系单": "-c",
    "预警单管理": "-w",
    "地市预警通告": "-a"
}

# 2. 创建一个全局线程锁
# 这个锁将保护 s60000_selenium.py 的执行过程，确保同一时间只有一个任务在运行
task_lock = threading.Lock()

app = Flask(__name__)

@app.route('/start-task', methods=['POST'])
def start_task_endpoint():
    # ... 参数解析逻辑不变 ...
    data = request.get_json()
    if not data or 'tasks' not in data or not isinstance(data['tasks'], list):
        return jsonify({"success": False, "error": "请求体错误..."}), 400

    task_flags = [TASK_MAP.get(name) for name in data['tasks'] if TASK_MAP.get(name)]
    if not task_flags:
        return jsonify({"success": False, "error": "任务列表为空或所有任务名称都无效。"}), 400

    print(f"[*] 收到任务请求 {task_flags}，正在尝试获取执行锁...")

    # 3. 使用 try...finally 结构来确保锁一定会被释放
    try:
        # 尝试获取锁。如果锁已被其他请求占用，此行代码会阻塞，直到获取到锁。
        # 这就实现了“排队等待”的效果。
        task_lock.acquire()
        
        print(f"[*] 任务 {task_flags} 已获取执行锁，开始执行...")
        command = ['python', '-u', S6000_SCRIPT_PATH] + task_flags

        # --- 运行和解码逻辑与之前相同 ---
        process = subprocess.run(
            command,
            capture_output=True,
            check=True
        )
        
        s6000_result_str = process.stdout.decode('utf-8')
        stderr_text = process.stderr.decode('gbk', errors='ignore')
        if stderr_text:
            print(f"--- [S6000脚本日志 START] ---\n{stderr_text}\n--- [S6000脚本日志 END] ---")

        result_data = json.loads(s6000_result_str)
        
        print(f"[*] 任务 {task_flags} 执行成功，准备返回结果。")
        return jsonify(result_data), 200

    except Exception as e:
        # ... 异常处理逻辑不变 ...
        error_message = f"执行过程中发生未知错误: {str(e)}"
        print(f"[严重错误] {error_message}")
        return jsonify({"success": False, "error": error_message}), 500
    finally:
        # 无论成功还是失败，都必须释放锁，以便下一个等待的请求可以开始执行。
        # 这是至关重要的一步，可以防止死锁。
        if task_lock.locked():
            task_lock.release()
            print(f"[*] 任务 {task_flags} 已释放执行锁。")

@app.route('/check-deadlines', methods=['GET'])
def check_deadlines_endpoint():
    """
    触发执行任务六：截止时间检测。
    这是一个专有接口，无需任何参数。
    """
    print(f"[*] 收到截止时间检测请求，正在尝试获取执行锁...")

    try:
        # 同样使用全局锁来确保串行执行
        task_lock.acquire()
        
        print(f"[*] 截止时间检测任务已获取执行锁，开始执行...")
        # 直接指定任务6的命令行参数
        command = ['python', '-u', S6000_SCRIPT_PATH, '-d']

        process = subprocess.run(
            command,
            capture_output=True,
            check=True
        )
        
        s6000_result_str = process.stdout.decode('utf-8')
        stderr_text = process.stderr.decode('gbk', errors='ignore')
        if stderr_text:
            print(f"--- [S6000脚本日志 START] ---\n{stderr_text}\n--- [S6000脚本日志 END] ---")

        result_data = json.loads(s6000_result_str)
        
        print(f"[*] 截止时间检测任务执行成功，准备返回结果。")
        return jsonify(result_data), 200

    except Exception as e:
        error_message = f"执行截止时间检测时发生未知错误: {str(e)}"
        print(f"[严重错误] {error_message}")
        return jsonify({"success": False, "error": error_message}), 500
    finally:
        # 确保释放锁
        if task_lock.locked():
            task_lock.release()
            print(f"[*] 截止时间检测任务已释放执行锁。")

if __name__ == '__main__':
    print("S6000 Automation Agent (同步锁模式) 正在启动...")
    app.run(host='0.0.0.0', port=6000, debug=False)