# 告警类型和攻击结果分析系统解释

##  概述

`step9_alert_type_analysis.py` 是一个基于多维度分析的安全告警误报识别系统，它在原有IP流向分析的基础上，增加了告警级别、攻击结果和置信度三个关键维度。

##  核心功能

###  多维度分析框架

系统基于以下五方面进行综合分析：

1. **IP流向**：区分内对内、内到外、外到内和外对外四种流量模式
2. **时间**：判断事件是否发生在工作时间（工作日8:30-17:30）
3. **告警级别**：将告警分为高、中、低三个级别
4. **攻击结果**：将攻击结果分为失陷、攻击成功、企图和失败四个层级
5. **置信度**：基于多因素计算的高、中、低三级置信度

###  误报识别能力

系统能够识别多种类型的误报场景：

- 低级别内网失败攻击且置信度低的事件
- 工作时间内的单次内网事件
- 纯内网低风险流量
- 工作时间内的低风险外联活动
- 来自可信来源（如省公司、电科院）的流量
- 低级别失败的外部访问尝试

###  风险分级

系统将安全事件分为以下几个风险等级：

- **高危**：需要立即处理的严重安全事件
- **中危**：需要审核的可疑安全事件
- **低危**：可能为正常业务活动
- **需关注**：虽可能误报但需人工审核的事件
- **可能误报**：建议复核的低风险事件
- **误报**：系统判定为误报的事件

##  技术实现

###  数据源整合

系统整合了多个数据源：

```python
# 加载清洗后的数据
df = pd.read_csv("cleaned_security_logs.csv", encoding="utf-8-sig")

# 加载IP风险评分数据
risk_df = pd.read_csv("step5_ip_threat_score.csv")

# 加载误报过滤后的数据
fp_df = pd.read_csv("step6_fp_filtered.csv")

# 加载已知误报IP列表
fp_ip_list = pd.read_csv("step6_fp_ip_list2.csv")
```

###  IP流向分析

系统使用 `ipaddress` 库判断IP是否为内网地址，并据此确定流量方向：

```python
def is_internal_ip(ip):
    try:
        # 检查是否是私有IP地址
        return ipaddress.ip_address(ip).is_private
    except:
        return False

def determine_flow_direction(src_ip, dest_ip):
    src_internal = is_internal_ip(src_ip)
    dest_internal = is_internal_ip(dest_ip)
    
    if src_internal and dest_internal:
        return "内对内"
    elif src_internal and not dest_internal:
        return "内到外"
    elif not src_internal and dest_internal:
        return "外到内"
    else:
        return "外对外"
```

###  工作时间判断

系统定义工作时间为工作日（周一至周五）的8:30-17:30：

```python
def is_work_time(dt):
    # 工作时间定义为8:30-17:30
    work_start = time(8, 30)
    work_end = time(17, 30)
    
    # 获取小时和分钟
    dt_time = dt.time()
    
    # 判断是否为工作日（周一至周五）
    is_weekday = dt.weekday() < 5
    
    # 判断是否在工作时间范围内
    is_work_hours = work_start <= dt_time <= work_end
    
    return is_weekday and is_work_hours
```

###  告警级别映射

基于严重程度（severity）将告警分为高、中、低三个级别：

```python
def map_severity_level(severity):
    if severity >= 5:
        return "高"
    elif severity >= 3:
        return "中"
    else:
        return "低"
```

###  攻击结果映射

基于事件类型（eventType）将攻击结果分为失陷、攻击成功、企图和失败四个层级：

```python
def map_attack_result(event_type):
    # 根据事件类型判断攻击结果
    # 失陷 > 攻击成功 > 企图 > 失败
    if event_type in [7, 8]:  # 假设7和8表示系统失陷或数据泄露
        return "失陷"
    elif event_type in [5, 6]:  # 假设5和6表示攻击成功
        return "攻击成功"
    elif event_type in [3, 4]:  # 假设3和4表示攻击企图
        return "企图"
    else:  # 1和2表示攻击失败或仅探测
        return "失败"
```

###  置信度计算

系统基于告警级别、攻击结果和设备类型综合计算置信度：

```python
def map_confidence_level(row):
    confidence_score = 0
    
    # 告警级别因素
    if row['alert_level'] == "高":
        confidence_score += 30
    elif row['alert_level'] == "中":
        confidence_score += 20
    else:
        confidence_score += 10
    
    # 攻击结果因素
    if row['attack_result'] == "失陷":
        confidence_score += 40
    elif row['attack_result'] == "攻击成功":
        confidence_score += 30
    elif row['attack_result'] == "企图":
        confidence_score += 20
    else:
        confidence_score += 10
    
    # 设备类型因素 - 不同设备的可信度不同
    if "入侵检测" in row['deviceName'] or "IDS" in row['deviceName']:
        confidence_score += 20
    elif "防火墙" in row['deviceName'] or "WAF" in row['deviceName']:
        confidence_score += 15
    else:
        confidence_score += 10
    
    # 根据总分判断置信度
    if confidence_score >= 70:
        return "高"
    elif confidence_score >= 50:
        return "中"
    else:
        return "低"
```

###  数据聚合与比例计算

系统按小时和源IP聚合数据，并计算各维度的比例：

```python
# 按小时和源IP聚合信息
agg_df = df_with_hour.groupby(['hour_slot', 'srcAddress']).agg(
    # 流向统计
    flow_directions=('flow_direction', lambda x: list(set(x))),
    internal_to_internal_count=('flow_direction', lambda x: (x == '内对内').sum()),
    internal_to_external_count=('flow_direction', lambda x: (x == '内到外').sum()),
    external_to_internal_count=('flow_direction', lambda x: (x == '外到内').sum()),
    external_to_external_count=('flow_direction', lambda x: (x == '外对外').sum()),
    work_time_count=('is_work_time', lambda x: x.sum()),
    total_events=('id', 'count'),
    # 告警级别统计
    high_alert_count=('alert_level', lambda x: (x == '高').sum()),
    medium_alert_count=('alert_level', lambda x: (x == '中').sum()),
    low_alert_count=('alert_level', lambda x: (x == '低').sum()),
    # 攻击结果统计
    compromise_count=('attack_result', lambda x: (x == '失陷').sum()),
    success_count=('attack_result', lambda x: (x == '攻击成功').sum()),
    attempt_count=('attack_result', lambda x: (x == '企图').sum()),
    fail_count=('attack_result', lambda x: (x == '失败').sum()),
    # 置信度统计
    high_confidence_count=('confidence_level', lambda x: (x == '高').sum()),
    medium_confidence_count=('confidence_level', lambda x: (x == '中').sum()),
    low_confidence_count=('confidence_level', lambda x: (x == '低').sum()),
    # 事件频率
    event_frequency=('id', 'count')
).reset_index()

# 计算各维度比例
agg_df['work_time_ratio'] = agg_df['work_time_count'] / agg_df['total_events']
agg_df['high_alert_ratio'] = agg_df['high_alert_count'] / agg_df['total_events']
# ... 其他比例计算
```

##  风险评估算法

###  多因素评分调整

系统基于五个维度调整原始威胁评分：

```python
# 告警级别因素影响分数
if row['high_alert_ratio'] > 0.7:  # 70%以上是高级别告警
    alert_factor = 15  # 提高风险评分
elif row['low_alert_ratio'] > 0.7:  # 70%以上是低级别告警
    alert_factor = -15  # 降低风险评分

# 攻击结果因素影响分数
if row['compromise_ratio'] > 0.3 or row['success_ratio'] > 0.5:  # 有较多失陷或攻击成功
    result_factor = 20  # 显著提高风险评分
elif row['attempt_ratio'] > 0.7:  # 主要是攻击企图
    result_factor = 5  # 略微提高风险评分
elif row['fail_ratio'] > 0.7:  # 主要是失败的攻击
    result_factor = -10  # 降低风险评分

# 置信度因素影响分数
if row['high_confidence_ratio'] > 0.6:  # 60%以上是高置信度
    confidence_factor = 15  # 提高风险评分
elif row['low_confidence_ratio'] > 0.6:  # 60%以上是低置信度
    confidence_factor = -15  # 降低风险评分

# 工作时间因素影响分数
if row['work_time_ratio'] > 0.7:  # 70%以上发生在工作时间
    time_factor = -10  # 降低风险评分
elif row['work_time_ratio'] < 0.3:  # 70%以上发生在非工作时间
    time_factor = 10  # 提高风险评分

# 事件频率因素影响分数
if row['event_frequency'] >= 10:  # 每小时10次以上
    frequency_factor = 15  # 显著提高风险评分
elif row['event_frequency'] >= 5:  # 每小时5-10次
    frequency_factor = 10  # 提高风险评分
elif row['event_frequency'] <= 1:  # 每小时1次或更少
    frequency_factor = -5  # 略微降低风险评分

# 调整后的威胁评分
adjusted_threat_score = row['threat_score'] + alert_factor + result_factor + confidence_factor + time_factor + frequency_factor
```

###  场景化判断规则

系统针对不同流量模式应用不同的判断规则：

####  内对内流量判断

```python
if row['internal_to_internal_count'] > 0 and row['internal_to_external_count'] == 0 and row['external_to_internal_count'] == 0:
    # 低级别告警 + 失败攻击 + 低置信度 = 很可能误报
    if row['low_alert_ratio'] > 0.7 and row['fail_ratio'] > 0.6 and row['low_confidence_ratio'] > 0.5:
        return "误报 - 低级别内网失败攻击，低置信度"
    # 单次事件与持续性事件区分
    if row['event_frequency'] <= 1 and row['work_time_ratio'] > 0.7:
        return "误报 - 工作时间内的单次内网事件"
    # ... 其他内对内流量判断规则
```

####  内到外流量判断

```python
if row['internal_to_external_count'] > 0:
    # 低级别告警 + 失败攻击 + 工作时间 = 可能是正常业务外联
    if row['low_alert_ratio'] > 0.6 and row['fail_ratio'] > 0.7 and row['work_time_ratio'] > 0.7:
        return "误报 - 工作时间内的低风险外联活动"
    # 高置信度 + 失陷/攻击成功 = 高风险数据外泄
    elif (row['compromise_ratio'] > 0.2 or row['success_ratio'] > 0.3) and row['high_confidence_ratio'] > 0.5:
        if row['event_frequency'] > 5:
            return "高危 - 持续性数据外泄风险，高置信度，需立即处理"
    # ... 其他内到外流量判断规则
```

####  外到内流量判断

```python
if row['external_to_internal_count'] > 0:
    # 检查源IP是否为可信来源
    src_ip = row['srcAddress']
    if is_trusted_source(src_ip):
        # 即使是可信来源，如果有高置信度的失陷/攻击成功，也需要关注
        if (row['compromise_ratio'] > 0.2 or row['success_ratio'] > 0.3) and row['high_confidence_ratio'] > 0.5:
            return "需关注 - 来自可信来源但有成功攻击迹象，高置信度"
    # ... 其他外到内流量判断规则
```

####  混合流量判断

```python
# 混合流量模式 - 综合判断
if adjusted_threat_score >= 75:
    # 高置信度 + 失陷/攻击成功 = 高风险
    if (row['compromise_ratio'] > 0.2 or row['success_ratio'] > 0.3) and row['high_confidence_ratio'] > 0.5:
        return "高危 - 复杂流量模式，有成功攻击迹象，高置信度"
    # ... 其他混合流量判断规则
```

##  使用方法

```bash
### 高置信度
满足以下任一条件时，置信度被判定为"高"：

- 事件频率大于等于10次
- 当前运行持续时间大于等于5小时
- 调整后的威胁分数大于等于80，且不是误报
### 中置信度
当不满足高置信度条件，但满足以下任一条件时，置信度被判定为"中"：

- 事件频率大于等于5次
- 当前运行持续时间大于等于3小时
- 调整后的威胁分数大于等于60
### 低置信度
以下情况会被判定为"低"置信度：

- 不满足高置信度和中置信度的所有条件
- 结论中包含"误报"关键词
- 被标记为误报（false_positive_flag为True）python step9_alert_type_analysis.py
```

1. **多维度分析**：结合IP流向、时间、告警级别、攻击结果和置信度五个维度，提供全面的风险评估
2. **场景化判断**：针对不同流量模式应用不同的判断规则，提高误报识别的准确性
3. **可信来源识别**：能够识别来自可信来源（如省公司、电科院）的流量，减少误报
4. **单次与持续性事件区分**：区分单次偶发事件和持续高频事件，更准确地识别真实威胁













- 