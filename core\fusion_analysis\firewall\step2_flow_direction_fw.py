import pandas as pd
import numpy as np
import ipaddress
import os
import re
from datetime import datetime
from step1_parse_security_logs_fw import get_processed_security_logs

def is_private_ip(ip):
    """
    判断IP是否为内网IP
    使用ipaddress库判断是否为私有IP地址
    异常情况返回False（按外网处理）
    """
    try:
        return ipaddress.ip_address(ip).is_private
    except:
        return False

def normalize_time_format(time_str):
    """
    标准化时间格式，处理各种异常格式
    """
    if pd.isna(time_str) or not time_str:
        return None
    
    time_str = str(time_str).strip()
    
    # 处理标准格式：2025-08-20 15:22:30
    try:
        return pd.to_datetime(time_str, format='%Y-%m-%d %H:%M:%S')
    except:
        pass
    
    # 处理月日格式：08-20 15:35:34
    try:
        # 假设年份为2025
        full_time_str = f"2025-{time_str}"
        return pd.to_datetime(full_time_str, format='%Y-%m-%d %H:%M:%S')
    except:
        pass
    
    # 处理已经转换为datetime对象的情况
    try:
        return pd.to_datetime(time_str)
    except:
        pass
    
    # 处理特殊格式：2025/07/29 20:03
    if re.match(r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}', time_str):
        try:
            return datetime.strptime(time_str, '%Y/%m/%d %H:%M')
        except:
            pass
    
    # 处理只有月日的格式：07-29
    if re.match(r'\d{2}-\d{2}', time_str):
        try:
            # 假设年份为2025
            return datetime.strptime(f'2025-{time_str}', '%Y-%m-%d')
        except:
            pass
    
    # 处理Jul 29格式
    if re.match(r'[A-Za-z]{3}\s+\d{1,2}', time_str):
        try:
            # 提取月份和日期
            month_day_match = re.search(r'([A-Za-z]{3})\s+(\d{1,2})', time_str)
            if month_day_match:
                month = month_day_match.group(1)
                day = month_day_match.group(2)
                # 提取时间
                time_match = re.search(r'\d{2}:\d{2}:\d{2}', time_str)
                time_part = time_match.group(0) if time_match else "00:00:00"
                # 使用2025年
                return datetime.strptime(f"2025 {month} {day} {time_part}", "%Y %b %d %H:%M:%S")
        except:
            pass
    
    # 如果所有尝试都失败，返回一个默认时间
    print(f"警告: 无法解析时间格式 '{time_str}'，使用默认时间")
    return datetime(2025, 1, 1)  # 使用默认时间而不是返回None

def determine_flow_direction(df):
    """
    判断流量方向：内对内、内到外、外到内、外对外
    """
    print("正在判断IP流向...")
    
    # 判断源IP和目的IP是否为内网
    df['src_is_internal'] = df['srcAddress'].apply(is_private_ip)
    df['dest_is_internal'] = df['destAddress'].apply(is_private_ip)
    
    # 确定流向
    conditions = [
        (df['src_is_internal'] & df['dest_is_internal']),  # 内对内
        (df['src_is_internal'] & ~df['dest_is_internal']),  # 内到外
        (~df['src_is_internal'] & df['dest_is_internal']),  # 外到内
        (~df['src_is_internal'] & ~df['dest_is_internal'])   # 外对外
    ]
    choices = ["内对内", "内到外", "外到内", "外对外"]
    
    df['flow_direction'] = pd.Series(np.select(conditions, choices, default="未知"), index=df.index)
    
    # 统计各流向数量
    flow_counts = df['flow_direction'].value_counts()
    print("\n流向统计:")
    for direction, count in flow_counts.items():
        print(f"{direction}: {count}条记录")
    
    return df

def main():
    df = get_processed_security_logs()
    
    if df.empty:
        print("错误: 未获取到任何数据")
        return
        
    original_count = len(df)
    print(f"获取到的记录数: {original_count}")
    
    # 检查必需列是否存在
    required_columns = ['srcAddress', 'destAddress', 'time', 'id']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return
    
    # 处理时间列 - 使用自定义函数处理异常格式
    print("处理时间格式...")
    df['time'] = df['time'].apply(normalize_time_format)
    
    # 添加小时槽位列
    df['hour_slot'] = df['time'].dt.strftime('%Y-%m-%d %H:00:00')
    
    # 判断流向
    df = determine_flow_direction(df)
    return df
    # 保存结果
    # output_file = "step2_flow_direction.csv"
    # df.to_csv(output_file, index=False)
    # print(f"\n结果已保存至 {output_file}")
    # print(f"处理后记录数: {len(df)}")
    # print(f"保留率: {len(df)/original_count*100:.2f}%")

if __name__ == "__main__":
    import numpy as np  # 添加numpy导入
    main()