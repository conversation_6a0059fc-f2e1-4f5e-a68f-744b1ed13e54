#!/usr/bin/env python
# encoding:utf-8
"""
Step7 行为分析集成测试脚本
测试 step7_behavior_analysis.py 与其他模块的集成
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_step7_integration():
    """
    测试 step7_behavior_analysis.py 的集成功能
    """
    print("=== Step7 行为分析集成测试 ===")
    
    try:
        # 导入step7模块
        from step7_behavior_analysis import get_behavior_analysis_data, analyze_behavior, merge_external_info, main
        
        print("\n1. 测试数据获取...")
        # 测试数据获取
        df, risk_df, fp_df, false_positive_ips = get_behavior_analysis_data()
        
        print(f"✓ 基础数据: {len(df)} 条记录")
        print(f"✓ 威胁评分数据: {len(risk_df)} 条记录")
        print(f"✓ 误报数据: {len(fp_df)} 条记录")
        print(f"✓ 已知误报IP: {len(false_positive_ips)} 个")
        
        print("\n2. 测试数据结构...")
        # 检查基础数据结构
        required_base_columns = ['hour_slot', 'srcAddress']
        for col in required_base_columns:
            if col not in df.columns:
                raise ValueError(f"基础数据缺少必需列: {col}")
        print("✓ 基础数据结构检查通过")
        
        # 检查威胁评分数据结构
        required_risk_columns = ['hour_slot', 'srcAddress', 'threat_score']
        for col in required_risk_columns:
            if col not in risk_df.columns:
                raise ValueError(f"威胁评分数据缺少必需列: {col}")
        print("✓ 威胁评分数据结构检查通过")
        
        # 检查误报数据结构
        required_fp_columns = ['hour_slot', 'srcAddress', 'false_positive_flag']
        for col in required_fp_columns:
            if col not in fp_df.columns:
                raise ValueError(f"误报数据缺少必需列: {col}")
        print("✓ 误报数据结构检查通过")
        
        print("\n3. 测试时间格式...")
        # 检查时间格式
        if 'hour_slot' in df.columns:
            sample_time = df['hour_slot'].iloc[0]
            if pd.isna(sample_time):
                raise ValueError("时间字段包含空值")
            print(f"✓ 时间格式检查通过，示例: {sample_time}")
        
        print("\n4. 测试行为分析功能...")
        # 测试行为分析
        behavior_df = analyze_behavior(df)
        print(f"✓ 行为分析完成，生成 {len(behavior_df)} 条聚合记录")
        
        # 检查行为分析结果结构
        expected_behavior_columns = ['hour_slot', 'srcAddress', 'event_count', 'unique_dst_count']
        for col in expected_behavior_columns:
            if col not in behavior_df.columns:
                print(f"⚠ 行为分析结果缺少列: {col}")
        
        print("\n5. 测试外部信息合并...")
        # 测试外部信息合并
        final_df = merge_external_info(behavior_df, risk_df, fp_df, false_positive_ips)
        print(f"✓ 外部信息合并完成，最终 {len(final_df)} 条记录")
        
        print("\n6. 测试统计分析...")
        # 统计分析
        if len(final_df) > 0:
            # 威胁分统计
            if 'threat_score' in final_df.columns:
                threat_stats = {
                    '平均威胁分': final_df['threat_score'].mean(),
                    '最高威胁分': final_df['threat_score'].max(),
                    '最低威胁分': final_df['threat_score'].min()
                }
                print(f"✓ 威胁分统计: {threat_stats}")
            
            # 误报统计
            if 'false_positive_flag' in final_df.columns:
                fp_counts = final_df['false_positive_flag'].value_counts()
                print(f"✓ 误报统计: 误报 {fp_counts.get(True, 0)} 条，正常 {fp_counts.get(False, 0)} 条")
            
            # IP统计
            unique_ips = final_df['srcAddress'].nunique()
            print(f"✓ 唯一IP数: {unique_ips}")
        
        print("\n7. 测试main函数...")
        # 测试main函数
        result = main()
        if result is not None:
            print(f"✓ main函数测试通过，返回 {len(result)} 条记录")
        else:
            print("⚠ main函数返回None")
        
        print("\n=== 集成测试成功 ===")
        return True
        
    except Exception as e:
        print(f"\n❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_data_consistency():
    """
    测试数据一致性
    """
    print("\n=== 数据一致性测试 ===")
    
    try:
        from step7_behavior_analysis import get_behavior_analysis_data
        
        # 获取数据
        df, risk_df, fp_df, false_positive_ips = get_behavior_analysis_data()
        
        # 检查数据一致性
        print("\n检查数据一致性...")
        
        # 检查IP地址一致性
        base_ips = set(df['srcAddress'].astype(str))
        risk_ips = set(risk_df['srcAddress'].astype(str))
        fp_ips = set(fp_df['srcAddress'].astype(str))
        
        print(f"基础数据IP数: {len(base_ips)}")
        print(f"威胁评分IP数: {len(risk_ips)}")
        print(f"误报数据IP数: {len(fp_ips)}")
        
        # 检查时间范围一致性
        if 'hour_slot' in df.columns and 'hour_slot' in risk_df.columns:
            base_time_range = (df['hour_slot'].min(), df['hour_slot'].max())
            risk_time_range = (risk_df['hour_slot'].min(), risk_df['hour_slot'].max())
            print(f"基础数据时间范围: {base_time_range}")
            print(f"威胁评分时间范围: {risk_time_range}")
        
        print("✓ 数据一致性检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据一致性测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 运行集成测试
    success1 = test_step7_integration()
    success2 = test_data_consistency()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")