﻿plugin_id,vuln_level,date_found,date_recorded,exp_desc,target_ip,severity_score,scan_method,vul_confirmed,is_dangerous,i18n_name,cve_id,threat_level,level_score,basic_score,ip_vuln_count,density_score,dangerous_score,cve_score,asset_weight,cvss_base_score,cvss_temporal_score,cvss_environmental_score,comprehensive_score,final_score,risk_reason,false_positive_reason
76664,high,2015-12-10,2016-08-05,根据Java RMI 服务远程方法调用漏洞原理，通过执行id或date命令并根据命令执行结果进行漏洞验证。,*************,10.0,3.0,True,False,Java RMI 服务远程方法调用漏洞【原理扫描】,,2,10,10.0,4,2.0,10,5,3,5.2,5.2,7.8,21.6,88.0,高危漏洞(级别:high); 严重程度评分高(10.0分); 包含危险操作关键词: 命令执行; 核心业务网段资产; 漏洞详情: 根据Java RMI 服务远程方法调用漏洞原理，通过执行id或date命令并根据命令执行结果进行漏洞验证。...,漏洞发现时间较久远(2015-12-10)，可能已修复
60319,high,2021-04-27,2021-06-04,根据nacos权限绕过漏洞(CVE-2021-29441)原理，通过跟目标机的数据交互情况进行漏洞验证,************;************,9.8,3.0,True,False,nacos auth/users 权限绕过漏洞(CVE-2021-29441)【原理扫描】,CVE-2021-29441,2,10,9.92,2,1.0,0,10,1,5.2,5.2,2.6,3.5104,72.92533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分); 漏洞详情: 根据nacos权限绕过漏洞(CVE-2021-29441)原理，通过跟目标机的数据交互情况进行漏洞验证...,漏洞发现时间较久远(2021-04-27)，可能已修复
60093,high,2020-02-24,2020-02-21,根据Apache Tomcat文件包含漏洞(CVE-2020-1938)漏洞原理，通过读取/WEB-INF/web.xml文件并根据读取文件内容进行漏洞验证。,*************,9.8,3.0,True,False,Apache Tomcat文件包含漏洞(CVE-2020-1938)【原理扫描】,CVE-2020-1938,2,10,9.92,4,2.0,0,10,3,5.2,5.2,7.8,20.6712,87.226,高危漏洞(级别:high); 严重程度评分高(9.8分); 核心业务网段资产; 漏洞详情: 根据Apache Tomcat文件包含漏洞(CVE-2020-1938)漏洞原理，通过读取/WEB-INF/web.xml文件并根据读取文件内容进行漏洞验证。...,漏洞发现时间较久远(2020-02-24)，可能已修复
76546,high,2016-06-10,2016-06-01,,*************,9.8,3.0,False,False,ImageMagick Popen函数shell命令执行(CVE-2016-5118)【原理扫描】,CVE-2016-5118,2,10,9.92,1,0.5,0,10,2,5.2,5.2,5.2,10.280800000000001,78.56733333333334,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2016-06-10)，可能已修复
60070,high,2019-05-14,2019-05-21,,*************,9.8,3.0,False,False,Microsoft Windows 远程桌面服务远程执行代码漏洞(CVE-2019-0708)【原理扫描】,CVE-2019-0708,2,10,9.92,4,2.0,0,10,3,5.2,5.2,7.8,20.6712,87.226,高危漏洞(级别:high); 严重程度评分高(9.8分); 核心业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-05-14)，可能已修复
60406,high,2021-12-09,2021-12-09,,10.240.3.186,9.8,3.0,False,False,JAVA JMX agent不安全的配置漏洞【原理扫描】,,2,10,9.92,2,1.0,0,5,1,5.2,5.2,2.6,3.2104000000000004,72.67533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-12-09)，可能已修复
71247,high,2001-01-01,2025-03-24,,10.227.129.186;10.229.26.185;10.229.101.179;10.229.35.212;10.228.27.34;22.55.62.77;*************;10.229.27.218;10.229.185.49;10.229.97.130;10.228.29.65;**************;*************;22.55.62.147;22.55.62.85;10.229.186.13;10.229.25.188;**********;22.55.62.16;10.229.25.145;22.55.62.78;10.227.135.228;10.227.143.246;**********;10.229.102.248;*************;************;10.228.17.129;**************;10.229.102.252;*************;10.227.135.23;10.229.35.215;**********;10.229.186.99;22.55.70.63;10.229.185.209;10.229.184.29;************;10.227.135.98;10.229.32.220,9.8,3.0,False,False,SSLV2和SSLV3 协议检测【原理扫描】,,2,10,9.92,1,0.5,0,5,1,5.2,5.2,2.6,3.1804,72.65033333333334,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
71518,high,2012-03-13,2012-03-22,,*************,9.3,3.0,False,False,Microsoft Windows RDP 远程代码执行漏洞(CVE-2012-0002)(MS12-020)【原理扫描】,CVE-2012-0002,2,10,9.72,4,2.0,0,10,3,5.2,5.2,7.8,20.5992,87.166,高危漏洞(级别:high); 严重程度评分高(9.3分); 核心业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2012-03-13)，可能已修复
60726,high,2021-11-01,2023-03-15,,************;************,8.8,3.0,False,False,Nacos 默认secret.key密钥漏洞(CVE-2021-43116)【原理扫描】,CVE-2021-43116,2,10,9.52,2,1.0,0,10,1,5.2,5.2,2.6,3.4624,72.88533333333334,高危漏洞(级别:high); 严重程度评分高(8.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-11-01)，可能已修复
62813,high,2025-02-14,2025-02-14,,10.228.18.104;10.228.18.115,7.8,3.0,False,False,Ollama 敏感信息泄露漏洞【原理扫描】,,2,10,9.120000000000001,1,0.5,0,5,1,5.2,5.2,2.6,3.0844000000000005,72.57033333333334,高危漏洞(级别:high),漏洞未经确认验证
62813,high,2025-02-14,2025-02-14,,10.228.18.104,7.8,3.0,False,False,Ollama 未授权访问漏洞(CNVD-2025-04094)(CNNVD-202503-081)【原理扫描】,,2,10,9.120000000000001,1,0.5,0,5,2,5.2,5.2,5.2,9.488800000000001,77.90733333333334,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证
60369,high,2013-10-12,2021-10-13,根据Dahua Security DVR Appliances 身份验证绕过漏洞(CVE-2013-6117)原理，通过绕过目标机获取登录后的信息，判断是否能成功绕过登录。,10.240.4.25;22.55.56.184,7.5,3.0,True,False,Dahua Security DVR Appliances 身份验证绕过漏洞(CVE-2013-6117)【原理扫描】,CVE-2013-6117,2,10,9.0,1,0.5,0,10,1,5.2,5.2,2.6,3.37,72.80833333333334,高危漏洞(级别:high); 漏洞详情: 根据Dahua Security DVR Appliances 身份验证绕过漏洞(CVE-2013-6117)原理，通过绕过目标机获取登录后的信息，判断是否能成功绕过登录。...,漏洞发现时间较久远(2013-10-12)，可能已修复
50299,high,2001-06-01,2021-06-16,"根据通过SNMP获取路由等信息漏洞原理，通过读取目标服务的特定信息，验证漏洞是否存在。
",10.229.97.17;10.227.138.3,7.5,3.0,True,False,可通过SNMP获取路由等信息【原理扫描】,,2,10,9.0,2,1.0,0,5,1,5.2,5.2,2.6,3.1,72.58333333333333,"高危漏洞(级别:high); 漏洞详情: 根据通过SNMP获取路由等信息漏洞原理，通过读取目标服务的特定信息，验证漏洞是否存在。
",漏洞发现时间较久远(2001-06-01)，可能已修复
71247,high,2016-01-29,2010-07-23,"根据检测到目标服务支持SSL中等强度加密算法(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.227.135.228;10.229.186.99;10.229.185.209;10.240.4.117;10.229.184.29;10.229.102.248;10.229.185.49;10.229.102.252,7.5,3.0,True,False,检测到目标服务支持SSL中等强度加密算法(CVE-2016-2183)【原理扫描】,CVE-2016-2183,2,10,9.0,1,0.5,0,10,1,5.2,5.2,2.6,3.37,72.80833333333334,"高危漏洞(级别:high); 漏洞详情: 根据检测到目标服务支持SSL中等强度加密算法(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2016-01-29)，可能已修复
60000,high,2024-05-29,2024-05-29,,10.229.32.112;*************,7.5,3.0,False,False,Nacos 默认口令漏洞【原理扫描】,,2,10,9.0,1,0.5,0,5,1,5.2,5.2,2.6,3.07,72.55833333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2024-05-29)，可能已修复
71247,high,2016-01-29,2017-09-12,"根据SSL/TLS协议信息泄露漏洞(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",22.55.56.200;10.228.27.34;22.55.62.16;22.55.62.77;10.229.103.25;10.240.4.226;22.55.6.17;10.240.4.117;10.240.4.239;10.229.185.49;10.229.103.109;10.227.133.40;*************;10.240.4.203;10.229.201.128;10.240.4.111;10.229.103.16;*************;22.55.62.147;10.228.25.66;*************;22.55.62.85;10.229.103.206;10.240.4.176;22.55.56.11;10.229.25.188;**********;10.228.17.214;10.228.17.177;************;10.227.128.140;10.229.96.57;10.229.24.240;10.240.4.70;*************;22.55.62.78;10.227.135.228;22.55.62.112;10.228.26.113;10.229.98.189;**********;10.227.140.32;***********69;10.229.102.48;10.229.29.142;10.229.102.248;************;10.227.130.46;**********;10.229.102.252;*************;10.229.99.2;10.227.133.58;22.55.62.18;10.229.103.94;10.240.4.96;**********;10.228.29.70;10.229.185.83;10.229.98.202;10.229.186.99;10.227.128.135;10.228.21.244;10.240.2.206;10.240.4.183;10.229.185.209;10.227.143.111;10.229.184.29;22.55.70.63;10.227.139.194;10.229.201.72;************;10.227.135.31,7.5,3.0,True,False,SSL/TLS协议信息泄露漏洞(CVE-2016-2183)【原理扫描】,CVE-2016-2183,2,10,9.0,1,0.5,0,10,1,5.2,5.2,2.6,3.37,72.80833333333334,"高危漏洞(级别:high); 漏洞详情: 根据SSL/TLS协议信息泄露漏洞(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2016-01-29)，可能已修复
76960,high,2016-10-12,2016-10-25,,10.229.201.128;10.227.143.111;10.229.29.78;10.229.201.72;10.228.18.217,7.5,3.0,False,False,"OpenSSL ""SSL-Death-Alert"" 拒绝服务漏洞(CVE-2016-8610)【原理扫描】",CVE-2016-8610,2,10,9.0,1,0.5,0,10,1,5.2,5.2,2.6,3.37,72.80833333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-12)，可能已修复
60092,high,2017-12-01,2019-09-29,,10.228.26.113;10.240.4.226;22.55.6.17;*************;10.240.4.183,7.0,3.0,False,False,Microsoft Windows CredSSP 远程执行代码漏洞(CVE-2018-0886)【原理扫描】,CVE-2018-0886,2,10,8.8,1,0.5,0,10,1,5.2,5.2,2.6,3.3460000000000005,72.78833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-12-01)，可能已修复
60715,high,2023-02-23,2023-02-23,,10.240.3.186,7.0,3.0,False,False,Java JMX 未授权访问漏洞【原理扫描】,,2,10,8.8,2,1.0,0,5,1,5.2,5.2,2.6,3.076,72.56333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-02-23)，可能已修复
60236,middle,2018-03-30,2021-03-01,,*************;10.229.201.233;*************;10.240.4.226;************;22.55.6.17;10.240.4.183;*************,6.5,3.0,False,False,Spring Boot Actuator未授权访问漏洞 【原理扫描】,,1,6,6.199999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.434,51.62266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-03-30)，可能已修复
71247,middle,2001-01-01,2025-03-24,,22.55.56.200;10.240.4.226;10.229.27.105;*************;10.240.4.117;10.229.97.130;10.229.103.109;10.240.4.111;**************;*************;10.240.4.176;22.55.56.11;**********;************;10.229.24.240;10.240.4.27;10.227.133.40;10.240.4.26;10.240.4.19;**********;10.229.29.142;*************;10.228.17.129;*************;10.227.128.135;10.240.4.28;22.55.70.63;10.240.4.239;10.240.4.24;10.240.4.203;10.240.4.36;10.229.103.16;10.228.29.65;10.229.27.142;10.240.4.35;22.55.56.184;10.240.4.20;10.229.96.57;10.240.4.55;************;10.229.99.2;10.229.103.94;10.240.4.96;10.228.21.244;10.240.4.14;10.227.135.31;10.229.32.220;10.240.4.34;10.229.26.185;10.229.101.179;10.229.103.25;10.240.4.13;*************;10.240.4.38;10.228.17.177;10.227.128.140;10.240.4.25;10.227.133.58;10.229.27.173;10.228.29.70;10.227.139.194;10.229.26.3;10.227.130.46;10.227.135.98;10.227.129.186;10.229.35.212;10.240.4.37;22.55.6.17;10.229.27.218;10.229.201.128;10.240.4.16;*************;10.228.25.66;10.229.103.206;10.240.4.53;10.240.4.33;10.228.17.214;10.240.4.70;22.55.62.112;*************;10.228.26.113;10.227.143.246;***********69;10.227.140.32;**************;**********;10.227.135.23;10.229.35.215;22.55.62.18;**********;10.229.185.83;10.229.98.202;10.240.2.206;10.240.4.183;10.227.143.111;10.240.4.56;10.229.201.72;************,6.5,3.0,False,False,目标使用过期的TLS1.1 版协议【原理扫描】,,1,6,6.199999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.434,51.62266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
71247,middle,2001-01-01,2025-03-24,,*************;*************;22.55.56.200;*************;10.240.4.226;10.229.27.105;*************;*************;10.240.4.117;************;10.227.140.74;10.229.97.130;10.227.142.4;10.229.103.109;10.229.101.50;10.240.5.180;10.228.27.150;***********;*************;**************;22.55.64.168;22.55.4.39;*************;10.228.30.88;10.228.24.239;**************;10.228.27.220;10.227.136.208;10.228.27.31;22.55.56.11;10.229.97.89;**********;10.228.18.217;10.228.18.82;************;10.240.4.27;10.227.133.40;22.55.8.120;10.240.4.26;************;*************;*************;10.229.24.85;10.240.4.19;**********;10.240.2.31;**************;10.229.29.142;*************;10.240.3.41;************;10.229.30.11;10.228.17.129;10.227.133.121;10.227.129.88;10.229.30.59;10.228.18.219;10.240.4.28;22.55.70.63;10.240.5.211;22.55.54.225;10.227.128.56;10.228.27.34;10.229.32.241;10.228.23.3;10.229.30.57;10.228.21.159;10.227.143.247;10.228.28.78;10.240.4.239;10.229.25.182;22.55.70.51;22.55.62.132;10.228.27.116;*************;10.240.4.24;10.240.4.203;10.240.4.36;10.229.103.16;10.228.29.65;10.229.29.89;*************;10.228.27.187;10.240.4.35;22.55.62.147;*************;22.55.62.85;10.227.128.86;10.229.30.51;22.55.8.47;10.227.134.136;22.55.64.129;10.229.30.44;10.229.25.188;10.227.129.89;22.55.56.184;10.240.4.20;10.229.96.57;10.227.136.41;10.240.5.182;10.229.102.48;10.228.18.218;10.229.98.189;10.229.25.134;10.229.30.79;10.228.21.146;22.55.9.28;10.240.4.55;10.228.27.86;22.55.1.93;10.229.32.169;10.229.99.2;10.229.27.147;22.55.56.72;22.55.54.236;10.228.27.83;22.55.64.83;10.229.103.94;10.240.4.96;10.229.30.106;10.228.21.153;*************;10.240.4.14;10.229.32.185;10.228.21.129;10.229.98.236;22.55.62.150;10.229.203.169;10.229.32.220;10.240.4.34;10.229.26.185;10.229.101.179;10.229.98.232;10.227.128.74;10.228.27.179;10.227.135.31;10.229.103.25;10.229.202.103;10.228.17.124;10.228.18.199;10.229.29.78;10.229.96.163;10.240.4.13;10.227.132.18;10.227.135.125;*************;10.240.3.67;*************;10.229.25.25;10.229.30.77;10.228.27.44;10.229.30.120;10.240.3.124;10.240.4.38;10.229.30.6;10.228.21.200;************1;10.229.29.219;10.229.30.78;10.229.203.143;10.240.4.25;10.228.29.244;10.229.202.152;10.227.133.114;10.227.133.58;10.227.137.9;10.228.21.131;10.229.35.248;10.229.203.155;10.240.4.173;10.228.29.70;10.227.128.73;10.227.129.167;22.55.75.173;10.228.23.162;10.229.29.174;10.227.139.194;10.227.130.46;10.227.142.34;10.227.135.98;10.229.201.196;10.227.129.186;10.229.35.212;22.55.62.77;10.240.4.37;10.229.35.64;10.229.200.150;22.55.1.56;22.55.6.17;10.229.27.218;10.240.3.162;10.227.129.90;10.229.25.234;10.227.142.41;10.229.201.128;10.229.29.120;10.240.4.16;*************;*************;10.228.25.66;10.227.137.120;*************;22.55.70.2;10.229.103.206;*************;10.240.5.183;10.240.4.53;10.240.4.33;*************8;10.228.17.214;10.228.27.144;22.55.62.112;22.55.62.16;*************;22.55.62.78;*************;10.228.27.180;10.227.135.228;10.227.143.246;10.229.24.227;10.228.21.198;*************;10.227.140.32;10.228.26.113;***********69;**************;10.229.101.153;10.228.24.161;10.228.17.109;**************;**********;10.227.135.23;10.229.35.215;22.55.62.18;**********;10.229.185.83;10.240.3.186;10.240.2.206;10.240.4.183;10.228.30.33;10.228.18.22;10.240.4.56;10.227.143.111;10.229.30.17;10.229.30.223;10.229.30.5;10.228.27.218;10.229.201.72;10.228.30.21,6.5,3.0,False,False,目标使用过期的TLS1.0 版协议【原理扫描】,,1,6,6.199999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.434,51.62266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
50407,middle,2005-06-01,2005-08-09,,10.227.129.74;10.229.98.189;*************;10.228.25.66;10.228.17.214,6.4,3.0,False,False,Microsoft Windows远程桌面协议中间人攻击漏洞(CVE-2005-1794)【原理扫描】,CVE-2005-1794,1,6,6.16,2,1.0,0,10,1,4.2,4.2,2.1,2.7592000000000003,51.83946666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2005-06-01)，可能已修复
60000,middle,2025-05-22,2025-05-27,,10.228.28.245;************,6.3,3.0,False,False,Metrics 未授权访问漏洞【原理扫描】,,1,6,6.119999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.4244,51.61626666666667,中危漏洞(级别:middle),漏洞未经确认验证
60000,middle,2021-11-11,2025-03-18,,10.228.28.245,6.0,3.0,False,False,Grafana 未授权访问漏洞【原理扫描】,,1,6,6.0,1,0.5,0,5,2,4.2,4.2,4.2,7.540000000000001,55.02666666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-11-11)，可能已修复
75142,middle,2015-03-26,2015-03-26,根据Redis 未授权访问漏洞原理，通过对目标机的登录测试进行漏洞验证。,22.55.62.81;10.229.102.133;*************,6.0,3.0,True,False,Redis 未授权访问漏洞(CNNVD-201511-230)【原理扫描】,,1,6,6.0,1,0.5,0,5,1,4.2,4.2,2.1,2.41,51.60666666666667,中危漏洞(级别:middle); 漏洞详情: 根据Redis 未授权访问漏洞原理，通过对目标机的登录测试进行漏洞验证。,漏洞发现时间较久远(2015-03-26)，可能已修复
60478,middle,2005-12-05,2025-02-07,,10.228.27.34;22.55.62.77;10.240.4.226;10.229.98.20;10.229.34.133;10.229.29.78;************;*************;22.55.56.219;10.228.25.66;*************;22.55.62.85;*************;22.55.62.147;22.55.56.184;**********;10.228.18.217;10.228.17.214;10.228.17.177;10.227.128.140;22.55.62.16;22.55.62.78;10.227.135.228;10.229.102.48;10.229.98.189;10.240.2.31;10.240.4.25;10.227.134.50;************;22.55.62.18;10.229.103.94;10.228.18.219;10.229.185.83;10.229.98.202;10.227.128.135;10.240.4.183;10.229.103.224;10.229.102.33,5.9,3.0,False,False,SSL证书使用了弱hash算法(CVE-2005-4900)【原理扫描】,CVE-2005-4900,1,6,5.96,1,0.5,0,10,1,4.2,4.2,2.1,2.7052,51.803466666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2005-12-05)，可能已修复
71247,middle,2016-03-01,2016-02-18,"根据OpenSSL SSLv2 协议""Drown""漏洞(CVE-2016-0800)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.228.27.34;22.55.62.78;22.55.62.77;22.55.62.147;22.55.62.85;10.229.25.188;22.55.62.16,5.9,3.0,True,False,"OpenSSL SSLv2 协议""Drown""漏洞(CVE-2016-0800)【原理扫描】 ",CVE-2016-0800,1,6,5.96,1,0.5,0,10,1,4.2,4.2,2.1,2.7052,51.803466666666665,"中危漏洞(级别:middle); 漏洞详情: 根据OpenSSL SSLv2 协议""Drown""漏洞(CVE-2016-0800)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2016-03-01)，可能已修复
71247,middle,2013-03-15,2016-03-18,"根据SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.227.129.186;10.229.26.185;10.229.101.179;10.229.35.212;10.228.27.34;22.55.62.77;10.229.103.25;10.240.4.226;*************;10.229.27.218;22.55.6.17;10.240.4.239;10.229.185.49;10.229.97.130;10.229.103.109;10.240.4.203;10.229.201.128;10.240.4.111;10.229.103.16;10.228.29.65;**************;*************;10.228.25.66;*************;22.55.62.85;22.55.62.147;10.240.4.176;10.229.186.13;10.229.25.188;**********;22.55.56.184;10.228.17.214;10.228.17.177;10.227.128.140;10.229.24.240;10.229.25.145;22.55.62.16;22.55.62.78;10.227.135.228;10.227.143.246;10.228.26.113;10.229.98.189;**********;10.229.102.48;10.229.102.248;*************;10.240.4.25;************;10.228.17.129;10.227.130.46;**************;10.229.102.252;*************;10.227.135.23;10.229.35.215;22.55.62.18;10.229.103.94;10.240.4.96;**********;10.229.98.202;10.229.186.99;10.227.128.135;10.229.185.209;10.229.184.29;10.240.4.183;10.227.143.111;22.55.70.63;10.229.201.72;************;10.227.135.98;10.229.32.220,5.9,3.0,True,False,SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)【原理扫描】,CVE-2013-2566,1,6,5.96,2,1.0,0,10,1,4.2,4.2,2.1,2.7352,51.82346666666667,"中危漏洞(级别:middle); 漏洞详情: 根据SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2013-03-15)，可能已修复
60000,middle,2022-03-16,2023-08-28,,*************,5.6,3.0,False,False,海康Hikvision IP 摄像机后门漏洞【原理扫描】,,1,6,5.84,1,0.5,0,5,1,4.2,4.2,2.1,2.3908,51.59386666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-03-16)，可能已修复
60001,middle,2018-08-15,2018-08-23,根据OpenSSH 用户枚举漏洞原理，通过从目标机获取敏感信息进行漏洞验证。,************;************;*************;************;************;*************;*************;************;*************;*************;*************;**************;*************;*************;*************;*************;*************;************;*************;************;*************;*************;************;************;*************;*************;*************;*************;************;************;************;************,5.3,3.0,True,False,OpenSSH 用户枚举漏洞(CVE-2018-15473)【原理扫描】,CVE-2018-15473,1,6,5.72,1,0.5,0,10,1,2.3,2.3,1.2,2.1364,51.42426666666667,中危漏洞(级别:middle); 漏洞详情: 根据OpenSSH 用户枚举漏洞原理，通过从目标机获取敏感信息进行漏洞验证。,漏洞发现时间较久远(2018-08-15)，可能已修复
110438,middle,2017-03-19,2017-07-07,,22.55.5.62;10.229.96.254;22.55.0.28;22.55.0.10;10.229.185.171;10.240.3.254;10.229.186.110;***********;**********;22.55.0.45;10.229.187.41;10.229.102.254;10.229.186.103;10.229.187.58;22.55.3.126;10.229.186.213;10.229.185.170;10.227.139.62;10.229.184.50;10.229.187.38;22.55.69.254;10.229.184.228;10.227.129.254;10.229.184.131;10.229.187.10;10.229.187.86;22.55.9.254;22.55.3.83;10.229.185.158;22.55.3.195;10.229.185.148;10.229.184.38;22.55.127.29;22.55.70.254;22.55.0.41;10.229.187.229;10.229.96.126;10.229.187.93;10.229.186.254;10.229.34.62;10.229.187.18;10.229.187.65;22.55.3.114;10.229.187.237;10.229.187.250;10.229.187.110;10.228.31.254;10.229.184.227;22.55.3.197;10.229.32.62;10.240.4.254;10.229.187.82;10.229.185.242;22.55.61.254;10.229.187.49;10.227.133.254;10.229.187.46;10.229.185.245;10.229.187.62;10.229.187.42;22.55.0.8;10.227.140.254;10.229.187.230;22.55.3.1;10.229.185.146;22.55.3.132;10.229.187.245;10.229.187.137;10.229.30.254;10.229.187.238;22.55.0.43;10.229.97.254;10.229.184.133;22.55.0.31;22.55.3.81;10.229.185.185;10.229.185.78;22.55.3.194;22.55.0.23;10.229.186.138;22.55.0.35;10.229.203.254;22.55.0.11;10.229.184.30;22.55.62.254;10.229.186.133;10.229.185.251;22.55.3.161;10.229.184.9;10.229.187.50;10.229.31.254;10.229.187.209;22.55.3.190;10.229.187.114;22.55.3.98;10.229.187.129;10.229.187.45;22.55.72.62;22.55.3.206;22.55.3.222;22.55.3.25;10.229.24.254;22.55.0.42;22.55.0.27;22.55.52.126;22.55.3.3;22.55.3.164;10.229.185.246;22.55.75.126;10.229.185.249;10.229.185.222;10.229.187.94;10.229.187.234;10.227.142.126;10.229.27.254;22.55.72.126;10.228.24.254;22.55.3.49;10.229.202.190;10.229.187.9;10.240.6.254;10.229.185.169;22.55.3.163;10.229.185.13;22.55.3.116;22.55.0.48;10.229.184.226;22.55.3.22;22.55.3.26;22.55.0.6;10.229.185.196;10.229.184.157;10.240.5.254;10.229.25.254;10.229.202.254;10.228.28.254;10.229.185.254;10.229.103.254;22.55.0.30;22.55.3.226;10.229.185.248;22.55.3.97;10.229.187.30;10.229.184.29;10.229.187.33;10.228.25.254;22.55.0.19;10.229.187.185;10.229.184.132;10.229.184.239;10.228.29.254;10.229.185.161;10.229.187.226;10.229.186.214;10.228.26.254;10.229.186.131;22.55.0.46;10.229.187.90;10.228.22.254;22.55.3.178;10.229.187.89;10.229.185.197;10.229.186.95;10.229.29.126;10.229.185.252;10.229.187.134;10.229.185.130;22.55.0.18;22.55.76.254;10.229.185.68;10.229.184.130;22.55.8.254;10.229.185.250;10.229.32.190;10.229.33.190;22.55.3.179;10.228.27.254;10.227.137.254;10.229.184.232;10.229.186.104;10.229.187.210;10.228.23.126;10.229.101.254;10.227.139.254;22.55.0.38;22.55.3.196;22.55.3.238;10.229.187.241;22.55.3.62;10.229.187.118;10.229.186.90;22.55.0.26;22.55.0.52;10.229.185.243;10.227.130.126;22.55.3.174;10.229.184.142;10.228.21.254;22.55.3.18;10.229.186.96;22.55.3.115;10.229.33.62;22.55.73.126;22.55.0.32;10.229.185.134;10.229.186.141;22.55.59.254;10.229.186.102;22.55.0.3;10.229.184.1;22.55.3.150;10.229.187.81;22.55.3.14;22.55.74.254;10.229.202.126;10.227.135.254;22.55.3.131;22.55.3.33;10.227.141.254;22.55.0.25;10.227.142.254;22.55.3.99;10.229.185.206;10.229.185.123;22.55.0.29;10.229.100.254;10.229.185.62;22.55.74.126;10.229.185.14;10.229.187.233;10.229.30.126;22.55.0.2;10.229.29.254;10.229.187.37;10.229.185.100;22.55.1.254;22.55.3.17;10.229.186.136;10.229.187.194;22.55.0.13;22.55.3.225;10.229.184.134;22.55.0.47;22.55.75.254;22.55.0.39;10.229.187.113;10.229.24.126;10.229.187.70;10.229.187.249;10.229.185.110;10.229.187.246;10.229.185.247;10.229.26.254;10.229.184.51;10.229.186.97;10.229.185.30;10.229.184.229;10.229.185.241;10.229.32.254;22.55.73.254;10.229.185.133;22.55.3.146;10.229.187.21;22.55.3.4;10.229.184.94;10.229.187.222;10.229.185.126;22.55.0.5;10.229.185.174;22.55.3.177;10.228.30.254;22.55.3.162;10.229.187.189;10.229.28.254;10.228.26.62;10.229.202.222;10.229.187.138;22.55.3.2;22.55.3.142;10.227.139.126;22.55.3.145;10.229.186.105;22.55.0.4;22.55.0.1;22.55.0.7;22.55.0.24;22.55.3.229;10.229.185.198;10.229.186.126;10.229.184.158;10.229.187.157;22.55.0.40;10.229.187.217;22.55.67.254;10.229.184.230;10.229.187.53;22.55.0.9;10.229.187.61;10.229.186.101;10.229.186.221;22.55.3.82;10.229.187.206;10.227.138.254;10.229.185.221;10.229.34.254;10.229.187.101;10.229.184.235;22.55.76.126;10.229.186.85;10.229.187.193;10.229.99.254;22.55.0.12;10.229.32.126;10.229.187.141;22.55.3.227;10.229.185.142;10.229.98.254;10.229.187.1;10.229.185.46;22.55.0.44;10.229.185.163;22.55.0.21;22.55.0.34;***********;10.229.187.69;***********;10.229.185.94;10.229.184.10;***********;***********;10.227.132.254;10.227.140.126;10.229.187.150;***********;10.229.187.122;22.55.0.22;10.229.184.225;10.229.186.51;10.227.134.254,5.0,3.0,False,False,NTP Mode 6 检测漏洞【原理扫描】,,1,6,5.6,1,0.5,0,5,1,4.2,4.2,2.1,2.362,51.574666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2017-03-19)，可能已修复
71247,middle,2009-01-05,2025-06-24,,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;*************;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;22.55.62.41;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;10.229.28.13;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;10.227.135.41;10.229.202.35;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;10.229.102.248;22.55.72.41;************5;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;22.55.62.87;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;10.229.35.65;10.229.30.6;***********82;22.55.54.246;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;22.55.127.7;22.55.62.22;10.229.201.153;10.229.29.219;10.228.18.85;10.229.200.10;10.229.203.143;10.240.4.25;10.229.96.243;10.227.131.90;10.229.26.210;10.229.27.173;10.227.137.9;10.227.134.55;10.229.203.155;10.228.22.28;************7;22.55.6.9;22.55.62.64;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.240.3.162;22.55.62.61;10.229.25.234;10.229.26.170;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.6.6;22.55.56.149;10.227.143.235;10.229.98.195;10.228.20.207;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.64.113;10.240.4.226;10.240.2.192;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;10.229.97.130;10.229.32.106;22.55.62.30;10.229.97.197;10.227.141.126;10.229.24.131;10.228.21.205;10.228.30.88;10.228.24.239;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.228.29.62;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.102.130;10.229.34.37;22.55.68.21;22.55.6.11;10.227.138.3;10.229.28.107;************;10.229.102.33;10.228.30.241;10.240.5.211;10.240.2.187;10.240.2.186;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;22.55.127.11;10.240.2.173;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;*************;22.55.56.154;22.55.73.111;10.229.200.126;10.227.136.9;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.229.32.102;10.240.3.108;10.228.27.83;10.227.136.170;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.229.184.29;10.227.133.239;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.227.131.253;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.127.15;22.55.72.23;22.55.5.8;10.240.3.67;*************;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;************1;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;22.55.8.128;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.72.15;10.227.129.90;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;22.55.62.218;10.229.29.120;22.55.72.31;10.227.141.28;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.62.56;22.55.56.165;10.227.141.206;10.229.201.151;10.228.24.161;10.228.17.109;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.229.185.83;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;10.240.5.180;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;10.229.101.11;10.227.129.88;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;10.227.128.135;10.229.185.209;22.55.70.63;10.228.28.74;22.55.6.16;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;10.229.185.49;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;*************;10.227.136.126;10.228.27.187;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;10.227.136.41;22.55.127.5;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;10.227.135.120;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;10.228.16.119;22.55.72.51;22.55.62.216;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.186.13;10.228.24.102;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;22.55.127.14;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.127.12;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;22.55.127.13;10.229.30.109;10.227.140.151;10.228.28.51;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;**********;10.229.202.83;10.229.201.82;10.240.4.243;10.240.3.186;10.229.98.202;10.229.186.99;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;10.229.32.108;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;10.227.136.34;10.228.27.150;10.240.4.111;10.229.32.105;22.55.1.68;10.227.132.237;10.227.129.96;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;22.55.127.2;10.229.24.240;************3;22.55.62.249;10.227.142.52;10.229.27.55;22.55.127.10;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.3;10.227.136.70;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.54.211;10.227.136.111;10.228.26.214;10.229.200.141;10.240.6.62;10.229.32.241;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;10.229.96.219;10.229.28.43;22.55.62.85;22.55.75.106;22.55.64.129;22.55.56.184;10.227.129.89;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;************6;10.227.136.115;10.227.140.42;10.229.102.252;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.1.108;22.55.72.20;10.227.136.92;10.229.24.245;22.55.127.6;10.228.21.129;10.229.98.236;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.227.129.91;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.228.17.177;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;22.55.6.14;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;10.229.32.104;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;***********;*************;*************;************;*************;*************;***********;**********;************;*************;*************;************;*************;**************;**************;*************;**************;**************;************;**********;***********;*************;************;**************;************;************,5.0,3.0,False,False,目标主机支持RSA密钥交换【原理扫描】,,1,6,5.6,1,0.5,0,5,1,4.2,4.2,2.1,2.362,51.574666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2009-01-05)，可能已修复
2055,middle,2021-05-24,2021-08-28,,*************,5.0,5.0,False,False,猜测出ActiveMQ存在可登录的用户名口令,,1,6,5.6,1,0.5,0,5,2,4.2,4.2,4.2,7.444000000000001,54.962666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-05-24)，可能已修复
2058,middle,2021-05-24,2021-08-28,,10.228.27.12,5.0,5.0,False,False,猜测出sftp存在可登录的用户名口令,,1,6,5.6,1,0.5,0,5,2,4.2,4.2,4.2,7.444000000000001,54.962666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-05-24)，可能已修复
2013,middle,2001-01-01,2011-08-18,,*************;*************,5.0,5.0,False,False,猜测出远程SSH服务存在的用户名口令,,1,6,5.6,1,0.5,0,5,1,4.2,4.2,2.1,2.362,51.574666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
60310,middle,2021-04-16,2021-04-22,,10.228.18.219,5.0,3.0,False,False,Alibaba Druid 未授权访问【原理扫描】,,1,6,5.6,1,0.5,0,5,2,4.2,4.2,4.2,7.444000000000001,54.962666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-04-16)，可能已修复
60000,middle,2022-10-26,2022-10-26,根据Swagger API 未授权访问漏洞原理，通过构造并发送访问请求，根据目标的响应情况，验证漏洞是否存在。,10.229.201.233;10.228.28.9;10.228.28.33;************;************;10.229.30.135;*************,5.0,3.0,True,False,Swagger API 未授权访问漏洞【原理扫描】,,1,6,5.6,1,0.5,0,5,1,4.2,4.2,2.1,2.362,51.574666666666666,中危漏洞(级别:middle); 漏洞详情: 根据Swagger API 未授权访问漏洞原理，通过构造并发送访问请求，根据目标的响应情况，验证漏洞是否存在。...,漏洞发现时间较久远(2022-10-26)，可能已修复
2004,middle,2001-01-01,2007-06-21,,10.229.97.17;10.227.138.3,5.0,,False,False,猜测出远程SNMP服务存在可登录的用户名口令,,1,6,5.6,2,1.0,0,5,1,4.2,4.2,2.1,2.392,51.59466666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
71247,middle,2015-03-31,2015-03-31,"根据SSL/TLS 受诫礼(BAR-MITZVAH)攻击漏洞(CVE-2015-2808)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.227.129.186;10.229.26.185;10.229.101.179;10.229.35.212;10.228.27.34;22.55.62.77;10.229.103.25;10.240.4.226;*************;10.229.27.218;22.55.6.17;10.240.4.239;10.229.185.49;10.229.97.130;10.229.103.109;10.240.4.203;10.229.201.128;10.240.4.111;10.229.103.16;10.228.29.65;**************;*************;10.228.25.66;*************;22.55.62.85;22.55.62.147;10.240.4.176;10.229.186.13;10.229.25.188;**********;22.55.56.184;10.228.17.214;10.228.17.177;10.227.128.140;10.229.24.240;10.229.25.145;22.55.62.16;22.55.62.78;10.227.135.228;10.227.143.246;10.228.26.113;10.229.98.189;**********;10.229.102.48;10.229.102.248;*************;10.240.4.25;************;10.228.17.129;10.227.130.46;**************;10.229.102.252;*************;10.227.135.23;10.229.35.215;22.55.62.18;10.229.103.94;10.240.4.96;**********;10.229.98.202;10.229.186.99;10.227.128.135;10.229.185.209;10.229.184.29;10.240.4.183;10.227.143.111;22.55.70.63;10.229.201.72;************;10.227.135.98;10.229.32.220,5.0,3.0,True,False,SSL/TLS 受诫礼(BAR-MITZVAH)攻击漏洞(CVE-2015-2808)【原理扫描】,CVE-2015-2808,1,6,5.6,2,1.0,0,10,1,4.2,4.2,2.1,2.6919999999999997,51.794666666666664,"中危漏洞(级别:middle); 漏洞详情: 根据SSL/TLS 受诫礼(BAR-MITZVAH)攻击漏洞(CVE-2015-2808)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2015-03-31)，可能已修复
71466,middle,2011-03-21,2011-11-10,,22.55.3.25;*************;22.55.3.3;10.229.97.130;10.229.32.106;22.55.3.177;***********;**********;10.229.32.105;**************;22.55.3.162;22.55.56.219;10.227.129.96;10.228.31.227;10.228.18.217;10.228.18.82;22.55.3.18;**********;10.240.2.31;*************;22.55.3.115;10.228.17.129;22.55.3.49;10.227.129.88;10.228.18.219;10.229.102.130;22.55.70.63;22.55.3.2;22.55.3.163;22.55.3.83;22.55.3.145;22.55.3.116;22.55.3.195;22.55.3.26;*************;10.228.29.65;22.55.3.229;10.229.185.198;*************;22.55.3.114;22.55.56.184;10.227.129.89;10.229.201.160;10.229.96.57;22.55.3.33;22.55.3.131;10.228.17.12;22.55.3.197;10.229.32.102;22.55.3.226;10.229.186.228;22.55.3.99;22.55.3.97;*************;10.228.21.244;22.55.3.82;10.229.32.220;10.229.26.185;10.229.101.179;10.229.184.239;22.55.3.1;10.227.129.91;22.55.3.132;22.55.3.17;10.228.18.85;10.240.4.25;22.55.3.178;22.55.3.81;22.55.3.225;22.55.3.227;10.227.135.98;22.55.3.194;10.227.129.186;10.229.35.212;10.229.27.218;10.229.186.138;10.227.129.90;*************;***********;***********;10.229.25.145;***********;10.227.143.246;***********;22.55.3.161;10.229.186.104;**************;10.227.135.23;10.229.35.215;22.55.3.146;***********;22.55.62.18;22.55.3.4;10.229.185.83;10.240.3.186;22.55.3.98;22.55.3.196;************;10.229.32.108,5.0,3.0,False,False,服务器支持 TLS Client-initiated 重协商攻击(CVE-2011-1473)【原理扫描】,CVE-2011-1473,1,6,5.6,1,0.5,0,10,1,4.2,4.2,2.1,2.662,51.77466666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2011-03-21)，可能已修复
2021,middle,2006-04-25,2009-01-14,,*************;************,5.0,5.0,False,False,猜测出远程FTP服务存在可登录的用户名口令,,1,6,5.6,2,1.0,0,5,1,4.2,4.2,2.1,2.392,51.59466666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2006-04-25)，可能已修复
75307,middle,2015-11-26,2015-11-26,,10.229.26.185;10.240.4.226;*************;10.229.29.78;10.229.97.130;*************;22.55.56.219;10.228.25.66;22.55.62.85;10.229.25.188;10.228.17.214;22.55.62.16;22.55.62.78;10.227.143.246;10.229.98.189;**********;**************;10.229.35.215;10.240.5.190;22.55.62.18;10.229.103.94;**********;10.228.18.219;10.240.3.186;22.55.70.63;10.240.4.183;10.229.102.33;************;10.229.32.220,4.3,3.0,False,False,SSL/TLS 服务器瞬时 Diffie-Hellman 公共密钥过弱【原理扫描】,,1,6,5.319999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.3284,51.55226666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2015-11-26)，可能已修复
60019,middle,2018-10-26,2018-10-26,服务支持弱加密算法原理，通过读取目标服务的特定信息，验证漏洞是否存在。,10.229.184.81;************;*************;************;22.55.76.36;10.229.184.35;22.55.62.252;10.228.16.119;10.228.16.18;*************;*************;22.55.68.126;10.229.186.214;10.228.16.111;10.228.28.35;10.229.184.86;************;10.229.184.84;22.55.0.52;10.229.187.142;**************;*************;************;22.55.56.219;10.228.28.51;10.229.185.90;10.229.184.234;10.229.187.94;*************;************;*************;************;10.229.32.103;10.229.102.249;10.229.186.220;*************;10.228.28.9;10.229.28.190;************6;10.229.202.252;10.227.129.121;10.229.184.230;10.229.186.213;22.55.10.62;************;************5;10.229.186.228;10.229.184.236;************;22.55.58.5;22.55.0.33;10.229.184.87;10.229.186.205;10.228.28.28;10.229.186.212;************7;22.55.0.49;10.229.185.244;*************;10.229.186.70;*************;10.229.184.134;10.228.28.33;*************;************;10.229.186.221;************;************;10.229.184.89;10.228.28.67;************,4.3,3.0,True,False,SSH 服务支持弱加密算法【原理扫描】,,1,6,5.319999999999999,2,1.0,0,5,1,4.2,4.2,2.1,2.3584,51.572266666666664,中危漏洞(级别:middle); 漏洞详情: 服务支持弱加密算法原理，通过读取目标服务的特定信息，验证漏洞是否存在。,漏洞发现时间较久远(2018-10-26)，可能已修复
62815,middle,2012-02-19,2025-02-19,,10.227.129.74;10.229.98.189;*************;10.228.25.66;10.228.17.214,4.3,3.0,False,False,远程终端服务未配置为仅使用网络级身份验证 (NLA)【原理扫描】,,1,6,5.319999999999999,2,1.0,0,5,1,4.2,4.2,2.1,2.3584,51.572266666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2012-02-19)，可能已修复
60000,low,2016-05-12,2023-12-27,,22.55.58.5;10.228.28.245;************;22.55.62.150,3.9,3.0,False,False,pprof调试信息泄露漏洞【原理扫描】,,0,2,2.76,1,0.5,0,5,1,4.2,4.2,2.1,2.0212,10.492813333333332,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2016-05-12)，可能已修复
76561,low,2015-05-20,2016-06-30,"根据SSL/TLS LogJam中间人安全限制绕过漏洞(CVE-2015-4000)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",**********,3.7,3.0,True,False,SSL/TLS LogJam中间人安全限制绕过漏洞(CVE-2015-4000)【原理扫描】,CVE-2015-4000,0,2,2.68,1,0.5,0,10,1,4.2,4.2,2.1,2.3116,10.850973333333334,"低危漏洞(级别:low); 漏洞详情: 根据SSL/TLS LogJam中间人安全限制绕过漏洞(CVE-2015-4000)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2015-05-20)，可能已修复
71247,low,2020-09-09,2020-09-15,"根据OpenSSL 信息泄露漏洞(CVE-2020-1968)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",**********;**********;22.55.70.63;************;*************;**********;************,3.7,3.0,True,False,OpenSSL 信息泄露漏洞(CVE-2020-1968)【原理扫描】,CVE-2020-1968,0,2,2.68,1,0.5,0,10,1,4.2,4.2,2.1,2.3116,10.850973333333334,"低危漏洞(级别:low); 漏洞详情: 根据OpenSSL 信息泄露漏洞(CVE-2020-1968)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2020-09-09)，可能已修复
61546,low,2024-03-05,2024-03-05,,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;***********;*************;***********;**********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;22.55.3.83;10.229.28.13;22.55.3.195;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;10.227.135.41;10.229.202.35;22.55.3.114;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.3.197;22.55.57.48;22.55.72.41;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;22.55.3.1;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;22.55.3.132;10.229.35.65;10.229.30.6;***********82;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;22.55.62.22;10.229.201.153;10.229.29.219;22.55.6.3;10.229.203.143;10.229.96.243;22.55.3.81;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.229.203.155;22.55.62.64;10.228.22.28;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;22.55.3.194;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.229.186.138;10.240.3.162;22.55.62.61;10.229.25.234;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.56.149;10.227.143.235;10.229.98.195;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;22.55.3.98;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.3.25;10.240.4.226;10.240.2.192;22.55.64.113;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;22.55.3.3;22.55.3.164;10.229.97.130;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;22.55.56.219;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.3.49;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.34.37;22.55.68.21;10.229.28.107;10.240.2.186;10.229.102.33;10.228.30.241;10.240.5.211;22.55.3.163;10.240.2.187;22.55.3.116;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;10.240.2.173;22.55.3.26;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;22.55.56.154;10.227.136.9;22.55.73.111;10.229.200.126;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.240.3.108;22.55.3.226;10.228.27.83;10.227.136.170;22.55.3.97;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.229.184.239;10.228.28.245;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.5.8;22.55.72.23;10.240.3.67;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;22.55.3.178;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.72.15;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;22.55.3.179;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.56.165;10.229.201.151;10.228.24.161;10.228.17.109;10.229.186.104;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;22.55.3.196;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;22.55.3.18;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;22.55.3.115;10.229.101.11;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.229.24.177;10.240.6.144;***********2;10.227.128.135;22.55.70.63;10.228.28.74;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;10.228.27.187;10.227.136.126;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;22.55.3.33;10.227.136.41;22.55.3.131;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;22.55.3.99;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;22.55.72.51;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.3.17;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;22.55.3.225;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;10.229.30.109;10.227.140.151;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;22.55.3.146;10.229.201.82;10.229.202.83;10.240.4.243;10.240.3.186;10.229.98.202;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;22.55.3.177;10.227.136.34;10.228.27.150;10.240.4.111;22.55.1.68;22.55.3.162;10.227.132.237;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;10.229.24.240;22.55.62.249;10.227.142.52;10.229.27.55;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.70;10.227.136.3;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.228.26.214;10.240.6.62;10.229.32.241;10.229.200.141;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;22.55.3.229;10.229.96.219;10.229.28.43;10.229.185.198;22.55.62.85;22.55.75.106;22.55.64.129;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;10.227.136.115;10.227.140.42;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.72.20;10.227.136.92;10.229.24.245;10.228.21.129;10.229.98.236;22.55.3.82;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;22.55.3.227;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;*************;*************;************;***********;*************;***********;*************;***********;***********;**********;************;*************;***********;*************;************;*************;***********;**************;**************;*************;**************;**************;************;***********;***********;*************;************;**************;************;************,3.5,3.0,False,False,SSL 证书无法受到信任【原理扫描】,,0,2,2.6,1,0.5,0,5,1,4.2,4.2,2.1,2.0020000000000002,10.469133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2024-03-05)，可能已修复
61545,low,2024-03-05,2024-03-05,,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;***********;*************;***********;**********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;22.55.3.83;10.229.28.13;22.55.3.195;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;10.227.135.41;10.229.202.35;22.55.3.114;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.3.197;22.55.57.48;22.55.72.41;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;22.55.3.1;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;22.55.3.132;10.229.35.65;10.229.30.6;***********82;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;22.55.62.22;10.229.201.153;10.229.29.219;22.55.6.3;10.229.203.143;10.229.96.243;22.55.3.81;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.229.203.155;22.55.62.64;10.228.22.28;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;22.55.3.194;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.229.186.138;10.240.3.162;22.55.62.61;10.229.25.234;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.56.149;10.227.143.235;10.229.98.195;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;22.55.3.98;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.3.25;10.240.4.226;10.240.2.192;22.55.64.113;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;22.55.3.3;22.55.3.164;10.229.97.130;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;22.55.56.219;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.3.49;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.34.37;22.55.68.21;10.229.28.107;10.240.2.186;10.229.102.33;10.228.30.241;10.240.5.211;22.55.3.163;10.240.2.187;22.55.3.116;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;10.240.2.173;22.55.3.26;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;22.55.56.154;10.227.136.9;22.55.73.111;10.229.200.126;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.240.3.108;22.55.3.226;10.228.27.83;10.227.136.170;22.55.3.97;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.229.184.239;10.228.28.245;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.5.8;22.55.72.23;10.240.3.67;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;22.55.3.178;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.72.35;22.55.62.177;10.229.96.240;22.55.6.17;22.55.72.15;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;22.55.3.179;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.56.165;10.229.201.151;10.228.24.161;10.228.17.109;10.229.186.104;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;22.55.3.196;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;22.55.3.18;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;22.55.3.115;10.229.101.11;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.229.24.177;10.240.6.144;***********2;10.227.128.135;22.55.70.63;10.228.28.74;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;10.228.27.187;10.227.136.126;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;22.55.3.33;10.227.136.41;22.55.3.131;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;22.55.3.99;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;22.55.72.51;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.3.17;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;22.55.3.225;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;10.229.30.109;10.227.140.151;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;22.55.3.146;10.229.201.82;10.229.202.83;10.240.4.243;10.240.3.186;10.229.98.202;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;22.55.3.177;10.227.136.34;10.228.27.150;10.240.4.111;22.55.1.68;22.55.3.162;10.227.132.237;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;10.229.24.240;22.55.62.249;10.227.142.52;10.229.27.55;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.70;10.227.136.3;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.228.26.214;10.240.6.62;10.229.32.241;10.229.200.141;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;22.55.3.229;10.229.96.219;10.229.28.43;10.229.185.198;22.55.62.85;22.55.75.106;22.55.64.129;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;10.227.136.115;10.227.140.42;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.72.20;10.227.136.92;10.229.24.245;10.228.21.129;10.229.98.236;22.55.3.82;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;22.55.3.227;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;*************;*************;************;***********;*************;***********;*************;***********;***********;**********;************;*************;***********;*************;************;*************;***********;**************;**************;*************;**************;**************;************;***********;***********;*************;************;**************;************;************,3.5,3.0,False,False,SSL 证书链不完整【原理扫描】,,0,2,2.6,1,0.5,0,5,1,4.2,4.2,2.1,2.0020000000000002,10.469133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2024-03-05)，可能已修复
61545,low,2024-03-05,2024-03-05,,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;***********;*************;***********;**********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;22.55.3.83;10.229.28.13;22.55.3.195;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;10.227.135.41;10.229.202.35;22.55.3.114;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.3.197;22.55.57.48;22.55.72.41;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;22.55.3.1;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;22.55.3.132;10.229.35.65;10.229.30.6;***********82;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;22.55.62.22;10.229.201.153;10.229.29.219;22.55.6.3;10.229.203.143;10.229.96.243;22.55.3.81;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.229.203.155;22.55.62.64;10.228.22.28;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;22.55.3.194;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.229.186.138;10.240.3.162;22.55.62.61;10.229.25.234;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.56.149;10.227.143.235;10.229.98.195;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;22.55.3.98;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.3.25;10.240.4.226;10.240.2.192;22.55.64.113;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;22.55.3.3;22.55.3.164;10.229.97.130;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;22.55.56.219;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.3.49;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.34.37;22.55.68.21;10.229.28.107;10.240.2.186;10.229.102.33;10.228.30.241;10.240.5.211;22.55.3.163;10.240.2.187;22.55.3.116;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;10.240.2.173;22.55.3.26;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;22.55.56.154;10.227.136.9;22.55.73.111;10.229.200.126;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.240.3.108;22.55.3.226;10.228.27.83;10.227.136.170;22.55.3.97;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.229.184.239;10.228.28.245;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.5.8;22.55.72.23;10.240.3.67;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;22.55.3.178;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.72.15;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;22.55.3.179;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.56.165;10.229.201.151;10.228.24.161;10.228.17.109;10.229.186.104;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;22.55.3.196;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;22.55.3.18;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;22.55.3.115;10.229.101.11;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.229.24.177;10.240.6.144;***********2;10.227.128.135;22.55.70.63;10.228.28.74;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;10.228.27.187;10.227.136.126;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;22.55.3.33;10.227.136.41;22.55.3.131;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;22.55.3.99;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;22.55.72.51;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.3.17;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;22.55.3.225;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;10.229.30.109;10.227.140.151;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;22.55.3.146;10.229.201.82;10.229.202.83;10.240.4.243;10.240.3.186;10.229.98.202;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;22.55.3.177;10.227.136.34;10.228.27.150;10.240.4.111;22.55.1.68;22.55.3.162;10.227.132.237;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;10.229.24.240;22.55.62.249;10.227.142.52;10.229.27.55;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.3;10.227.136.70;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.228.26.214;10.240.6.62;10.229.32.241;10.229.200.141;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;22.55.3.229;10.229.96.219;10.229.28.43;10.229.185.198;22.55.62.85;22.55.75.106;22.55.64.129;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;10.227.136.115;10.227.140.42;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.72.20;10.227.136.92;10.229.24.245;10.228.21.129;10.229.98.236;22.55.3.82;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;22.55.3.227;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;*************;*************;************;***********;*************;***********;*************;***********;***********;**********;************;*************;***********;*************;************;*************;***********;**************;**************;*************;**************;**************;************;***********;***********;*************;************;**************;************;************,3.5,3.0,False,False,使用了自签名证书【原理扫描】,,0,2,2.6,1,0.5,0,5,1,4.2,4.2,2.1,2.0020000000000002,10.469133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2024-03-05)，可能已修复
75006,low,2014-05-14,2014-10-15,"根据SSL 3.0 POODLE攻击信息泄露漏洞(CVE-2014-3566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.227.129.186;10.229.26.185;10.229.101.179;10.229.35.212;10.228.27.34;22.55.62.77;*************;10.229.27.218;10.229.185.49;10.229.97.130;10.228.29.65;**************;*************;22.55.62.147;22.55.62.85;10.229.25.188;**********;22.55.62.16;10.229.25.145;22.55.62.78;10.227.135.228;10.227.143.246;**********;10.229.102.248;*************;************;10.228.17.129;**************;10.229.102.252;*************;10.227.135.23;10.229.35.215;**********;10.229.186.99;22.55.70.63;10.229.185.209;10.229.184.29;************;10.227.135.98;10.229.32.220,3.4,3.0,True,False,SSL 3.0 POODLE攻击信息泄露漏洞(CVE-2014-3566)【原理扫描】,CVE-2014-3566,0,2,2.56,1,0.5,0,10,1,4.2,4.2,2.1,2.2972,10.833213333333333,"低危漏洞(级别:low); 漏洞详情: 根据SSL 3.0 POODLE攻击信息泄露漏洞(CVE-2014-3566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2014-05-14)，可能已修复
60478,low,2022-05-02,2025-01-13,,10.227.136.16;10.229.34.175;10.240.3.235;10.240.2.126;10.229.32.6;22.55.73.239;10.229.201.46;10.228.31.227;22.55.72.38;10.229.97.106;10.228.25.153;10.227.131.89;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;10.240.2.197;22.55.72.47;10.240.6.148;10.229.28.13;10.229.35.16;22.55.72.46;10.227.137.160;10.228.25.194;10.229.34.133;10.227.137.181;10.228.30.244;10.228.24.248;10.229.201.142;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.24.174;10.227.135.41;10.229.202.35;10.240.3.109;10.229.33.133;10.228.17.12;10.229.25.6;22.55.72.41;10.228.27.16;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;10.229.201.81;10.227.134.104;10.229.200.138;10.240.2.195;10.229.200.131;10.240.6.169;10.229.32.35;10.229.24.179;10.240.3.110;10.229.35.65;***********82;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;10.229.201.153;10.240.4.25;10.229.96.243;10.227.131.90;10.229.26.210;10.228.22.28;10.229.97.240;10.229.27.68;10.229.201.157;10.228.26.209;10.227.137.175;10.229.200.106;10.229.97.50;10.240.3.228;10.228.21.167;10.227.143.235;10.229.98.195;10.228.27.23;10.227.135.201;10.229.201.52;10.240.4.70;10.227.135.228;22.55.73.251;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;10.229.202.94;10.229.24.246;22.55.62.185;10.229.35.29;10.229.26.105;22.55.56.200;10.229.201.62;22.55.64.113;10.240.2.192;22.55.72.22;10.229.97.197;10.227.141.126;10.229.24.131;10.228.21.205;22.55.56.219;22.55.72.40;10.227.137.164;22.55.4.65;10.228.18.82;10.229.26.211;22.55.72.17;10.240.6.147;22.55.65.187;10.229.96.220;10.229.27.167;10.229.24.175;10.229.202.60;10.229.97.21;10.229.32.137;22.55.68.35;22.55.72.25;10.229.34.37;22.55.68.21;10.229.28.107;************;10.229.102.33;10.228.30.241;10.240.2.186;10.240.2.187;10.229.30.123;22.55.56.228;10.240.2.194;10.240.2.173;10.229.29.75;10.229.98.20;10.240.3.107;22.55.73.248;10.227.143.118;***********3;10.229.97.43;10.227.128.42;10.227.139.57;10.240.6.27;*************;10.227.136.9;22.55.73.111;10.229.200.126;10.229.28.14;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.228.18.218;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.240.3.108;10.229.24.182;*************;22.55.76.160;10.229.27.46;22.55.72.53;10.229.33.10;10.227.131.253;10.229.34.66;10.229.29.78;10.227.137.179;22.55.5.8;22.55.72.23;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.30.242;10.227.136.11;10.227.128.35;10.227.140.48;10.240.2.76;10.227.136.5;22.55.4.66;10.240.6.150;10.227.143.226;10.227.136.28;10.240.5.206;10.240.3.134;10.229.97.253;10.229.200.132;10.227.137.173;10.229.27.133;22.55.72.35;10.229.96.240;22.55.72.15;10.240.3.208;10.228.29.180;10.227.137.176;22.55.72.31;10.227.141.28;10.229.96.221;10.228.25.66;*************;10.227.136.8;22.55.72.48;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.72.14;10.240.6.151;10.227.128.212;10.227.137.163;10.229.29.69;10.228.29.179;10.229.103.109;10.240.2.193;10.240.2.124;22.55.67.58;22.55.65.188;10.227.137.178;10.227.134.181;10.240.3.194;10.229.35.40;10.229.101.64;10.229.201.145;10.228.25.130;10.240.2.31;22.55.56.229;10.227.134.50;10.229.101.11;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;10.227.128.135;10.228.28.74;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;10.229.101.100;10.240.6.140;10.227.129.223;10.229.28.92;10.227.142.53;10.228.27.103;10.240.3.39;10.229.201.102;10.227.137.174;10.240.6.149;10.228.25.162;10.240.4.203;10.240.5.207;22.55.64.117;10.229.96.218;10.228.21.208;10.228.25.119;22.55.72.21;10.229.201.160;10.229.29.252;10.228.29.36;10.240.6.170;10.229.28.78;10.227.141.13;22.55.62.184;10.227.131.91;22.55.72.34;10.229.35.113;22.55.62.150;10.227.143.121;10.228.23.130;10.227.128.179;22.55.76.159;10.229.202.125;10.240.6.143;10.229.35.247;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;22.55.72.51;10.228.27.105;*************;22.55.72.50;10.229.24.180;10.227.137.198;10.227.128.193;10.229.201.85;10.228.24.152;10.229.200.130;10.229.96.138;10.240.3.144;22.55.72.45;10.228.31.200;10.228.31.5;10.227.131.95;10.240.3.148;10.227.136.30;10.228.26.210;10.227.137.189;10.229.96.209;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;10.228.31.36;22.55.62.154;22.55.64.116;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.228.21.209;10.229.96.241;10.227.140.151;10.240.5.183;22.55.73.250;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.96.222;10.229.35.193;10.227.129.224;10.227.140.32;10.229.35.155;10.227.128.41;10.240.2.125;10.227.140.61;10.229.201.82;10.229.202.83;10.229.203.148;22.55.62.155;***********99;10.228.22.31;10.229.29.157;10.227.141.18;10.227.132.237;22.55.56.11;10.229.202.95;10.227.142.52;10.229.27.55;10.229.30.82;22.55.54.47;10.227.136.3;10.240.3.193;22.55.4.63;10.229.33.141;22.55.72.36;10.228.26.214;10.229.200.141;22.55.72.44;10.229.34.83;10.227.137.162;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;10.229.96.219;10.229.28.43;22.55.62.85;22.55.56.184;22.55.64.115;10.229.34.210;10.229.101.109;10.227.141.11;10.227.140.42;10.227.136.10;22.55.72.20;10.229.24.245;10.229.203.142;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.228.31.9;10.227.132.236;10.228.24.225;10.229.201.146;10.229.27.190;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;22.55.73.112;10.229.200.139;10.229.201.58;10.227.128.150;10.227.128.8;10.229.201.67;10.227.130.46;22.55.72.27;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.96.239;22.55.73.241;************;************;*************;***********;***********;************;*************;************;**************;************;***********;*************;************;**************;************,3.3,3.0,False,False,目标SSL/TLS证书60天内过期【原理扫描】,,0,2,2.52,1,0.5,0,5,1,4.2,4.2,2.1,1.9924,10.457293333333332,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-02)，可能已修复
76245,low,2008-11-19,2016-03-08,"根据OpenSSH CBC模式信息泄露漏洞(CVE-2008-5161)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.229.184.81;************;*************;************;22.55.76.36;10.229.184.35;22.55.62.252;10.228.16.119;10.228.16.18;*************;*************;22.55.68.126;10.229.186.214;10.228.16.111;10.228.28.35;10.229.184.86;************;10.229.184.84;22.55.0.52;10.229.187.142;**************;*************;************;22.55.56.219;10.228.28.51;10.229.185.90;10.229.184.234;10.229.187.94;*************;************;*************;************;10.229.32.103;10.229.102.249;10.229.186.220;*************;10.228.28.9;10.229.28.190;************6;10.229.202.252;10.227.129.121;10.229.184.230;10.229.186.213;22.55.10.62;************;************5;10.229.186.228;10.229.184.236;************;22.55.58.5;22.55.0.33;10.229.184.87;10.229.186.205;10.228.28.28;10.229.186.212;************7;22.55.0.49;10.229.185.244;*************;10.229.186.70;*************;10.229.184.134;10.228.28.33;*************;************;10.229.186.221;************;************;10.229.184.89;10.228.28.67;************,2.6,3.0,True,False,OpenSSH CBC模式信息泄露漏洞(CVE-2008-5161)【原理扫描】,CVE-2008-5161,0,2,2.24,2,1.0,0,10,1,4.2,4.2,2.1,2.2888,10.822853333333335,"低危漏洞(级别:low); 漏洞详情: 根据OpenSSH CBC模式信息泄露漏洞(CVE-2008-5161)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2008-11-19)，可能已修复
50295,low,2021-05-10,2021-05-10,根据远程主机允许使用未加密FTP协议漏洞原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在。,*************;************,2.0,3.0,True,False,远程主机允许使用未加密FTP协议 【原理扫描】,,0,2,2.0,2,1.0,0,5,1,4.2,4.2,2.1,1.96,10.417333333333334,低危漏洞(级别:low); 漏洞详情: 根据远程主机允许使用未加密FTP协议漏洞原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在。...,漏洞发现时间较久远(2021-05-10)，可能已修复
71247,low,2001-01-01,2025-03-24,,22.55.62.121;**************;*************;*************;10.228.18.104;************;22.55.6.2;10.227.140.74;10.227.136.51;10.227.142.4;10.229.101.50;10.228.27.150;**********;10.228.27.71;22.55.1.68;**************;22.55.4.39;10.228.24.239;22.55.64.168;**************;10.228.27.220;10.227.136.12;10.227.136.208;10.228.27.31;22.55.62.57;10.229.97.89;10.229.32.238;10.228.18.82;22.55.8.120;************;10.227.136.39;*************;10.229.24.85;22.55.62.249;**********;10.240.6.41;10.240.2.31;**************;**************;10.240.3.41;************;10.227.134.50;10.227.136.70;10.229.30.11;10.229.202.60;10.240.6.40;22.55.56.163;10.240.5.190;10.229.30.59;10.228.29.62;10.228.18.219;22.55.62.215;***********2;10.229.203.208;22.55.62.153;10.240.5.211;22.55.54.211;10.240.6.62;22.55.54.225;10.227.128.56;10.229.32.241;10.227.136.62;10.229.201.233;10.228.23.3;10.229.30.57;10.228.21.159;22.55.62.170;22.55.6.4;10.227.143.247;22.55.56.166;10.227.136.99;10.228.28.78;10.229.25.182;10.229.98.20;*************;10.229.34.133;22.55.70.51;22.55.62.132;10.228.27.116;22.55.62.145;*************;10.229.203.218;22.55.54.210;10.229.29.89;10.227.136.52;10.228.27.187;22.55.56.154;10.227.128.86;22.55.75.106;10.229.30.51;22.55.58.1;22.55.8.47;10.227.134.136;22.55.64.129;10.229.30.44;10.227.136.6;10.227.136.41;22.55.6.20;10.229.30.79;10.228.21.146;10.227.136.114;10.229.25.134;22.55.9.28;10.228.27.86;22.55.1.93;22.55.57.48;10.229.32.169;22.55.1.48;10.229.27.147;22.55.54.236;22.55.56.72;10.228.27.83;22.55.62.65;22.55.64.83;10.229.30.106;10.228.21.153;10.227.135.120;22.55.1.108;22.55.6.21;10.229.31.7;10.227.133.239;10.227.136.4;10.229.32.185;10.228.21.129;22.55.62.150;10.229.203.142;10.229.203.169;10.227.128.74;10.227.136.157;10.228.27.179;10.228.28.245;22.55.62.176;10.229.202.103;22.55.56.237;10.229.96.163;10.227.132.18;22.55.5.8;10.227.135.125;10.240.3.67;22.55.56.151;10.229.25.25;10.229.30.77;10.228.27.44;10.229.30.120;10.240.3.124;10.240.4.152;22.55.76.29;10.229.30.6;10.228.24.102;10.228.21.200;10.227.136.98;************1;22.55.55.6;10.228.31.23;22.55.56.173;10.229.28.168;10.229.29.219;10.229.103.20;22.55.8.135;10.229.200.10;22.55.6.3;10.229.30.78;10.229.203.143;10.228.29.244;10.229.202.152;10.227.133.114;22.55.62.168;22.55.58.5;10.227.137.9;10.228.21.131;10.229.203.155;22.55.62.64;10.229.35.248;10.227.128.73;10.229.32.104;22.55.56.238;10.229.97.17;22.55.76.28;22.55.8.54;10.227.129.167;22.55.54.212;22.55.75.173;10.229.27.68;10.228.23.162;10.229.29.174;22.55.62.181;10.227.142.34;10.229.32.182;22.55.62.180;10.229.201.196;10.227.141.59;10.229.202.39;10.240.6.65;22.55.8.128;22.55.62.177;10.229.35.64;10.229.200.150;22.55.1.56;22.55.6.17;10.240.3.162;10.229.26.170;10.229.25.234;10.227.142.41;10.229.102.178;22.55.62.122;10.229.29.120;10.229.30.109;22.55.56.149;*************;*************;10.228.20.207;*************;10.227.137.120;22.55.70.2;22.55.60.56;*************8;10.229.32.235;10.229.31.209;10.228.27.144;**********;*************;10.228.27.180;10.227.136.141;10.229.24.227;10.228.21.198;22.55.56.165;22.55.62.169;10.227.141.206;10.229.201.151;*************;**************;10.227.136.139;*************;10.229.101.153;10.228.24.161;10.228.17.109;10.228.16.5;10.227.141.70;22.55.54.209;22.55.8.56;22.55.56.177;10.240.3.186;10.227.136.242;10.228.30.33;10.229.30.17;10.229.30.223;10.229.30.5;10.228.27.218;10.228.30.21;10.227.141.64,1.0,3.0,False,False,TLS 1.3 版协议检测【原理扫描】,,0,2,1.6,1,0.5,0,5,1,4.2,4.2,2.1,1.8820000000000001,10.321133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
72325,low,2010-11-01,2013-07-17,,*************;*************;22.55.3.25;10.240.5.232;22.55.3.3;22.55.3.164;22.55.3.177;***********;**********;*************;22.55.3.162;22.55.64.168;22.55.56.219;22.55.68.13;22.55.3.18;22.55.3.115;22.55.3.49;10.227.129.88;10.228.21.214;22.55.3.2;22.55.3.163;22.55.3.83;22.55.3.145;22.55.3.116;22.55.3.195;22.55.3.26;10.228.29.165;*************;22.55.3.229;10.229.185.198;*************;10.229.201.72;22.55.3.114;10.227.129.89;10.229.96.57;22.55.3.33;22.55.3.131;10.240.5.182;22.55.3.197;22.55.3.226;10.229.186.228;22.55.3.99;22.55.3.97;10.229.98.236;22.55.3.82;10.229.98.232;10.229.184.239;10.228.17.124;10.228.18.199;10.229.29.78;*************;22.55.3.1;*************;22.55.3.132;10.228.17.62;22.55.3.17;22.55.3.178;22.55.3.81;22.55.3.225;10.228.29.70;22.55.3.227;22.55.3.194;22.55.6.17;10.229.186.138;10.227.129.90;10.229.201.128;***********;10.240.5.183;10.240.5.176;22.55.3.179;***********;*************;***********;***********;22.55.1.3;22.55.3.161;10.229.186.104;22.55.3.146;***********;22.55.62.18;22.55.3.4;22.55.3.98;10.228.18.22;10.227.143.111;22.55.3.196;************,1.0,3.0,False,False,服务器允许SSL会话恢复【原理扫描】,,0,2,1.6,1,0.5,0,5,1,4.2,4.2,2.1,1.8820000000000001,10.321133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2010-11-01)，可能已修复
75315,low,1999-01-01,2015-12-31,,************;**********;**********,1.0,3.0,False,False,目标主机上SMB服务允许匿名登陆【原理扫描】,,0,2,1.6,1,0.5,0,5,1,4.2,4.2,2.1,1.8820000000000001,10.321133333333334,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(1999-01-01)，可能已修复
50205,low,2019-04-17,2019-04-17,,10.240.4.142;10.240.4.85;10.240.4.40;10.229.27.187;10.228.31.245;10.229.28.114;10.229.103.27;10.229.103.26;10.229.101.42;10.240.4.64;10.240.4.139;10.229.103.184;10.227.140.52;10.240.6.74;10.229.26.13;10.229.29.109;10.229.102.13;10.240.4.39;10.229.27.81;10.240.4.11,0.0,3.0,False,False,海康威视(HiKvision)NVR检测【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-17)，可能已修复
61549,low,2024-03-05,2024-03-05,,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;***********;*************;***********;**********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;*************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;22.55.62.41;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;22.55.3.83;10.229.28.13;22.55.3.195;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;*************;10.227.135.41;10.229.202.35;22.55.3.114;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.3.197;22.55.57.48;22.55.72.41;************5;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;22.55.62.87;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;22.55.3.1;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;22.55.3.132;10.229.35.65;10.229.30.6;***********82;22.55.54.246;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;10.229.32.103;10.229.201.153;10.229.29.219;10.228.18.85;22.55.6.3;10.229.203.143;10.240.4.25;10.229.96.243;22.55.3.81;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.229.203.155;22.55.6.9;10.228.22.28;************7;22.55.62.64;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;22.55.3.194;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.229.186.138;10.240.3.162;22.55.62.61;10.229.25.234;10.229.97.50;10.240.3.228;10.228.21.167;22.55.6.6;22.55.56.149;10.227.143.235;10.229.98.195;10.228.20.207;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;22.55.3.98;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.3.25;10.240.4.226;10.240.2.192;22.55.64.113;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;22.55.3.3;22.55.3.164;10.229.97.130;10.229.32.106;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;22.55.56.219;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.3.49;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.228.29.62;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.102.130;10.229.34.37;22.55.68.21;22.55.6.11;10.229.28.107;************;10.229.102.33;10.228.30.241;10.240.5.211;22.55.3.163;10.240.2.187;10.240.2.186;22.55.3.116;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;10.240.2.173;22.55.3.26;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;*************;22.55.56.154;22.55.73.111;10.229.200.126;10.227.136.9;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.229.32.102;10.240.3.108;22.55.3.226;10.228.27.83;10.227.136.170;22.55.3.97;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.227.131.253;10.228.28.245;10.229.184.239;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.127.15;22.55.72.23;22.55.5.8;10.240.3.67;*************;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;************1;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;22.55.3.178;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.62.252;22.55.72.15;10.227.129.90;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;22.55.62.218;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;22.55.3.179;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.62.56;22.55.56.165;10.229.201.151;10.228.24.161;10.228.17.109;10.229.186.104;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.229.185.83;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;22.55.3.196;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;22.55.67.58;22.55.65.188;10.227.137.178;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;22.55.3.18;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;*************;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;22.55.3.115;10.229.101.11;10.227.129.88;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;10.227.128.135;22.55.70.63;10.228.28.74;22.55.6.16;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;22.55.62.253;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;*************;10.227.136.126;10.228.27.187;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;22.55.3.33;10.227.136.41;22.55.3.131;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;22.55.3.99;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;*************;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;10.228.16.119;22.55.72.51;22.55.62.216;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.3.17;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;22.55.3.225;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;22.55.127.14;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.127.12;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;22.55.127.13;10.229.30.109;10.227.140.151;10.228.28.51;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;22.55.3.146;22.55.3.4;10.229.201.82;10.229.202.83;10.240.4.243;10.240.3.186;10.229.98.202;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.229.30.17;10.227.141.64;10.229.32.108;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;22.55.3.177;10.227.136.34;10.228.27.150;10.229.32.105;22.55.1.68;22.55.3.162;10.227.132.237;10.227.129.96;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;************3;22.55.62.249;10.227.142.52;10.229.27.55;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.3;10.227.136.70;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.228.26.214;10.240.6.62;10.229.32.241;10.229.200.141;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;22.55.3.229;10.229.96.219;10.229.28.43;10.229.185.198;22.55.62.85;22.55.75.106;22.55.64.129;22.55.56.184;10.227.129.89;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;************6;10.227.136.115;10.227.140.42;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;10.229.186.228;22.55.62.65;22.55.64.83;10.229.30.106;22.55.72.20;10.227.136.92;10.229.24.245;10.228.21.129;10.229.98.236;22.55.3.82;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.227.129.91;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.228.17.177;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;22.55.6.14;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;10.229.32.104;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;22.55.3.227;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;22.55.76.36;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;***********;*************;*************;************;*************;***********;*************;***********;***********;**********;************;*************;***********;*************;************;*************;***********;**************;**************;*************;**************;**************;************;**********;***********;***********;*************;************;**************;************;************;************,0.0,3.0,False,False,获取SSL 证书中的hostname【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2024-03-05)，可能已修复
50510,low,2024-11-11,2024-11-11,,*************;************;**********;**********,0.0,3.0,False,False,检测到远端syslog服务正在运行【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low),漏洞未经确认验证
60478,low,2022-05-02,2022-06-10,根据获取证书过期时间漏洞原理，获取SSL证书距离过期还有多久。,*************;*************;**************;*************;*************;*************;************;***********;***********;*************;***********;**********;*************;**********;**************;**************;*************;**************;************;************;*************;**************;************;*************;**********;**********;**************;**************;*************;**************;************;10.229.30.11;10.227.133.121;10.240.6.40;10.228.18.219;22.55.62.215;22.55.62.41;10.228.28.33;10.229.203.208;22.55.3.83;22.55.3.195;10.228.21.159;10.227.136.99;10.240.4.239;10.229.25.182;22.55.62.132;22.55.62.145;*************;10.229.29.89;10.229.27.142;10.227.128.86;*************;22.55.3.114;10.227.136.6;22.55.6.20;22.55.3.197;22.55.57.48;************5;22.55.1.48;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.87;22.55.62.43;10.227.136.29;10.227.128.74;22.55.62.176;22.55.56.237;10.227.135.125;22.55.3.1;10.229.25.25;10.228.27.44;10.240.4.152;22.55.76.29;22.55.3.132;10.229.30.6;22.55.54.246;10.227.136.177;10.229.32.103;22.55.62.22;10.229.29.219;10.228.18.85;22.55.6.3;10.229.203.143;22.55.3.81;10.229.27.173;22.55.58.5;10.227.137.9;10.229.203.155;22.55.6.9;22.55.62.64;************7;22.55.56.238;10.227.130.218;10.229.98.165;10.227.136.136;22.55.3.194;10.229.35.212;10.229.27.218;10.229.186.138;10.240.3.162;22.55.62.61;10.229.25.234;22.55.6.6;22.55.56.149;22.55.70.2;22.55.60.56;*************8;10.228.17.214;10.228.27.144;22.55.62.112;22.55.62.16;10.228.27.180;10.229.24.227;10.228.26.113;***********69;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.56.177;22.55.3.98;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;10.240.4.235;22.55.62.121;22.55.56.200;22.55.3.25;10.240.4.226;10.228.18.104;22.55.6.2;10.227.140.74;22.55.3.3;22.55.3.164;10.229.97.130;10.229.32.106;22.55.62.30;22.55.64.168;10.228.24.239;10.228.30.88;22.55.56.219;10.227.136.12;10.227.136.208;10.228.27.31;10.229.97.89;10.228.18.217;10.228.18.82;10.227.136.71;10.240.6.41;10.228.17.129;22.55.3.49;10.229.30.59;22.55.56.252;10.229.102.130;22.55.6.11;10.229.103.224;10.240.5.211;22.55.3.163;22.55.3.116;22.55.54.225;10.227.128.56;10.228.27.34;10.229.201.233;22.55.56.12;22.55.3.26;10.227.143.247;22.55.56.166;10.228.27.116;10.228.29.65;10.227.136.52;*************;*************;22.55.56.154;10.227.134.136;10.229.25.188;10.240.5.182;10.229.102.48;22.55.9.28;10.228.21.146;10.229.32.169;10.229.32.102;22.55.3.226;10.228.27.83;10.227.136.170;22.55.3.97;10.227.136.108;10.228.27.179;10.229.103.25;10.229.184.239;10.228.28.245;10.229.202.103;10.228.17.124;10.227.132.18;22.55.127.15;10.240.3.67;*************;10.228.21.200;************1;10.228.31.23;10.229.30.78;22.55.3.178;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.4.173;22.55.127.16;10.229.29.174;22.55.62.181;10.227.135.98;22.55.62.180;10.229.201.196;10.227.129.186;10.240.6.65;22.55.62.77;10.227.136.67;22.55.62.177;22.55.6.17;22.55.62.252;10.227.129.90;22.55.62.122;10.229.201.128;22.55.62.218;10.229.29.120;10.229.26.94;10.227.137.120;10.229.103.206;22.55.3.179;10.229.31.209;22.55.62.78;*************;22.55.62.56;22.55.56.165;10.229.201.151;10.228.24.161;10.228.17.109;10.229.186.104;22.55.62.18;10.229.185.83;10.227.136.242;10.228.30.33;22.55.3.196;10.229.201.72;10.228.30.21;10.229.101.50;22.55.4.39;10.227.133.40;22.55.8.120;22.55.3.18;10.227.136.39;10.229.24.85;*************;10.240.2.31;10.229.29.142;22.55.3.115;10.227.129.88;22.55.70.63;22.55.6.16;22.55.62.153;10.227.136.62;10.229.30.57;22.55.62.170;22.55.62.253;10.229.96.183;10.228.28.78;22.55.70.51;10.229.103.16;22.55.54.210;*************;10.228.27.187;10.227.136.126;22.55.62.147;10.229.30.51;22.55.58.1;22.55.8.47;10.229.30.44;10.229.96.57;22.55.3.33;10.227.136.41;22.55.3.131;10.229.30.79;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.229.103.94;22.55.3.99;10.229.31.7;10.227.136.4;*************;10.229.32.185;22.55.62.150;10.227.135.31;10.229.32.220;10.229.26.185;10.229.98.232;10.227.136.157;10.228.16.119;22.55.62.216;*************;22.55.56.151;10.229.30.77;10.240.3.124;22.55.56.173;10.228.28.9;22.55.3.17;22.55.8.135;10.228.29.244;22.55.62.42;10.229.202.152;22.55.62.168;10.229.35.248;22.55.3.225;10.227.129.167;22.55.8.54;10.229.32.182;22.55.62.20;10.229.35.64;22.55.1.56;10.227.142.41;10.229.102.178;10.229.30.109;22.55.127.13;10.228.28.51;10.229.32.235;10.229.25.145;10.227.136.141;10.227.143.246;10.228.21.198;10.229.101.153;22.55.54.209;10.229.35.215;22.55.3.146;22.55.3.4;10.240.4.243;10.240.3.186;10.229.98.202;10.240.4.183;10.227.136.60;10.228.18.22;10.227.143.111;10.229.30.17;10.227.141.64;10.229.32.108;10.227.136.51;10.227.142.4;22.55.3.177;10.227.136.34;10.228.27.150;10.229.32.105;22.55.1.68;22.55.3.162;10.227.129.96;10.227.136.46;10.228.27.220;22.55.62.57;10.229.32.238;**********;************3;22.55.62.249;10.240.3.41;10.227.136.70;22.55.56.163;10.240.5.190;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.240.6.62;10.229.32.241;10.228.23.3;22.55.6.4;22.55.3.229;10.229.185.198;22.55.75.106;22.55.64.129;10.227.129.89;10.229.34.40;10.229.98.189;10.229.25.134;10.228.27.86;************6;10.227.136.115;10.229.99.2;22.55.54.236;22.55.56.72;10.229.186.228;22.55.62.65;22.55.64.83;10.229.30.106;10.227.136.92;10.228.21.129;10.229.98.236;22.55.3.82;10.229.203.169;10.229.101.179;10.228.18.199;10.227.136.102;10.228.16.18;10.229.96.163;10.227.129.91;10.229.30.120;10.228.17.177;10.227.136.98;10.229.28.168;10.229.103.20;22.55.127.3;22.55.6.14;10.228.29.70;10.227.128.73;10.229.32.104;10.229.97.17;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.227.139.194;22.55.3.227;10.227.142.34;22.55.127.4;10.229.200.150;22.55.76.36;10.227.136.91;10.229.27.183;*************;*************;*************;************;*************;***********;*************;***********;**********;*************;***********;*************;***********;**************;**************;*************;**************;**********;***********;************;************,0.0,3.0,True,False,获取目标SSL证书过期时间【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low); 漏洞详情: 根据获取证书过期时间漏洞原理，获取SSL证书距离过期还有多久。,漏洞发现时间较久远(2022-05-02)，可能已修复
71247,low,2001-01-01,2010-07-23,根据检测到目标主机加密通信支持的SSL加密算法原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在,*************;*************;**************;*************;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;*************;***********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;*************;**************;************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;*************;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;22.55.62.41;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;10.229.28.13;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;*************;10.227.135.41;10.229.202.35;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;10.229.102.248;22.55.57.48;22.55.72.41;************5;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;22.55.62.87;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;10.229.35.65;10.229.30.6;***********82;22.55.54.246;10.227.136.177;10.227.128.140;10.240.6.91;22.55.56.34;10.227.141.10;22.55.127.7;22.55.55.6;10.229.201.153;22.55.62.22;10.229.32.103;10.229.29.219;10.228.18.85;10.229.200.10;22.55.6.3;10.229.203.143;10.240.4.25;10.229.96.243;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.227.134.55;10.229.203.155;10.228.22.28;************7;22.55.6.9;22.55.62.64;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.240.3.162;22.55.62.61;10.229.25.234;10.229.26.170;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.6.6;22.55.56.149;10.227.143.235;10.229.98.195;10.228.20.207;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;22.55.62.16;10.227.135.228;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.64.113;10.240.4.226;10.240.2.192;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;10.229.97.130;10.229.32.106;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.228.29.62;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.102.130;10.229.34.37;22.55.68.21;22.55.6.11;10.227.138.3;10.229.28.107;************;10.229.102.33;10.228.30.241;10.240.5.211;10.240.2.187;10.240.2.186;22.55.54.225;10.227.128.56;10.229.30.123;10.228.27.34;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;22.55.127.11;10.240.2.173;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;*************;22.55.56.154;22.55.73.111;10.229.200.126;10.227.136.9;10.229.28.14;10.227.134.136;10.229.25.188;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.229.102.48;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.229.32.102;10.240.3.108;10.228.27.83;10.227.136.170;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.229.184.29;10.227.133.239;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.227.131.253;10.228.28.245;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.127.15;22.55.72.23;22.55.5.8;10.240.3.67;*************;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;************1;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;22.55.8.128;10.229.200.132;22.55.62.77;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.62.252;22.55.72.15;10.227.129.90;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;22.55.62.218;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;*************;10.229.103.206;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.78;22.55.62.56;22.55.56.165;10.227.141.206;10.229.201.151;10.228.24.161;10.228.17.109;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.229.185.83;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;10.240.5.180;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.4.176;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;*************;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;10.229.101.11;10.227.129.88;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;10.227.128.135;10.229.185.209;22.55.70.63;10.228.28.74;22.55.6.16;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;22.55.62.253;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;10.229.185.49;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;*************;10.227.136.126;10.228.27.187;22.55.62.147;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;10.227.136.41;22.55.127.5;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;10.227.135.120;10.229.31.7;22.55.62.184;10.227.131.91;10.227.136.4;*************;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;22.55.76.159;10.227.136.157;10.229.35.247;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;10.228.16.119;22.55.72.51;22.55.62.216;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.227.128.193;10.229.186.13;10.228.24.102;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;22.55.127.14;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.127.12;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;22.55.127.13;10.229.30.109;10.227.140.151;10.228.28.51;10.240.5.183;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;**********;10.229.202.83;10.229.201.82;10.240.4.243;10.240.3.186;10.229.98.202;10.229.186.99;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;10.229.32.108;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;10.227.136.34;10.228.27.150;10.240.4.111;10.229.32.105;22.55.1.68;10.227.132.237;10.227.129.96;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;22.55.127.2;10.229.24.240;************3;22.55.62.249;10.227.142.52;10.229.27.55;22.55.127.10;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.3;10.227.136.70;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.54.211;10.227.136.111;10.228.26.214;10.229.200.141;10.240.6.62;10.229.32.241;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;10.229.96.219;10.229.28.43;22.55.62.85;22.55.75.106;22.55.64.129;22.55.56.184;10.227.129.89;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.229.98.189;10.229.25.134;10.227.141.11;10.228.27.86;************6;10.227.136.115;10.227.140.42;10.229.102.252;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.1.108;22.55.72.20;10.227.136.92;10.229.24.245;22.55.127.6;10.228.21.129;10.229.98.236;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.227.129.91;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.228.17.177;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;22.55.6.14;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;10.229.32.104;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;22.55.76.36;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;***********;*************;*************;************;*************;*************;***********;**********;************;*************;*************;************;*************;**************;**************;*************;**************;**************;************;**********;***********;*************;************;**************;************;************,0.0,3.0,True,False,检测到目标主机加密通信支持的SSL加密算法【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low); 漏洞详情: 根据检测到目标主机加密通信支持的SSL加密算法原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在...,漏洞发现时间较久远(2001-01-01)，可能已修复
71037,low,2008-06-24,2008-06-25,根据达梦数据库版本泄漏原理，通过从目标机获取敏感信息进行漏洞验证。,************1;10.229.201.233,0.0,3.0,True,False,达梦数据库版本泄漏【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low); 漏洞详情: 根据达梦数据库版本泄漏原理，通过从目标机获取敏感信息进行漏洞验证。,漏洞发现时间较久远(2008-06-24)，可能已修复
71247,low,2001-01-01,2025-03-24,,*************;*************;**************;*************;10.229.27.105;*************;*************;************;10.227.136.16;***********;10.229.34.175;10.240.3.235;*************;***********;*************;**********;**************;**************;10.240.2.126;*************;10.229.32.6;**************;22.55.73.239;10.229.201.46;************;10.228.31.227;************;10.240.4.27;10.240.4.26;************;**************;*************;22.55.72.38;10.229.97.106;**********;**********;10.229.98.6;**************;**************;*************;**************;************;10.228.25.153;10.229.30.11;10.227.133.121;10.227.131.89;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;10.228.18.219;22.55.62.215;10.240.2.197;22.55.62.41;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;10.229.28.13;10.229.35.16;22.55.72.46;10.228.21.159;10.227.137.160;10.228.25.194;10.227.136.99;10.240.4.239;10.229.25.182;10.229.34.133;10.227.137.181;22.55.62.132;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;*************;10.240.4.24;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.29.89;10.229.27.142;10.229.24.174;10.227.128.86;*************;10.227.135.41;10.229.202.35;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.57.48;22.55.72.41;************5;22.55.1.48;10.228.27.16;10.240.4.96;10.228.21.153;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;22.55.62.87;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.227.128.74;22.55.62.176;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;10.227.135.125;10.229.32.35;10.229.24.179;10.229.25.25;10.240.3.110;10.228.27.44;10.240.4.152;22.55.76.29;10.229.35.65;10.229.30.6;***********82;22.55.54.246;10.227.136.177;10.240.6.91;22.55.56.34;10.227.141.10;10.229.32.103;22.55.127.7;22.55.62.22;10.229.201.153;10.229.29.219;10.228.18.85;10.229.200.10;22.55.6.3;10.229.203.143;10.240.4.25;10.229.96.243;10.227.131.90;10.229.26.210;10.229.27.173;22.55.58.5;10.227.137.9;10.227.134.55;10.229.203.155;10.228.22.28;************7;22.55.6.9;22.55.62.64;22.55.56.238;10.227.130.218;10.229.97.240;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;10.228.26.209;10.227.137.175;10.229.35.212;10.229.200.106;10.229.27.218;10.240.3.162;22.55.62.61;10.229.25.234;10.229.26.170;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;10.240.4.16;22.55.6.6;22.55.56.149;10.227.143.235;10.229.98.195;10.228.20.207;22.55.70.2;22.55.60.56;10.228.27.23;10.227.135.201;*************8;10.229.201.52;10.228.27.144;10.240.4.70;10.228.17.214;22.55.62.112;10.228.27.180;10.229.24.227;22.55.73.251;10.228.26.113;***********69;22.55.62.169;10.227.136.118;10.227.136.139;10.228.16.5;10.227.141.70;10.227.135.23;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;10.240.2.206;10.229.30.223;10.229.30.5;10.228.27.218;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;22.55.56.200;10.229.201.62;22.55.64.113;10.240.4.226;10.240.2.192;10.240.4.117;22.55.72.22;10.228.18.104;22.55.6.2;10.227.140.74;10.229.97.130;10.229.32.106;22.55.62.30;10.229.97.197;10.227.141.126;22.55.64.168;10.228.21.205;10.228.30.88;10.228.24.239;10.229.24.131;10.227.136.12;10.227.136.208;10.228.27.31;22.55.72.40;10.229.97.89;10.227.137.164;10.228.18.217;10.228.18.82;22.55.4.65;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;10.228.17.129;22.55.65.187;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.229.97.21;10.229.30.59;10.228.29.62;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.102.130;10.229.34.37;22.55.68.21;22.55.6.11;10.227.138.3;10.229.28.107;************;10.229.102.33;10.228.30.241;10.240.5.211;10.240.2.187;10.240.2.186;22.55.54.225;10.227.128.56;10.229.30.123;22.55.56.228;10.229.201.233;10.240.2.194;22.55.56.12;22.55.127.11;10.240.2.173;10.229.29.75;10.227.143.247;22.55.56.166;10.229.98.20;10.240.3.107;10.228.27.116;22.55.73.248;10.227.143.118;***********3;10.240.4.36;10.228.29.65;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;10.240.4.35;*************;*************;22.55.56.154;22.55.73.111;10.229.200.126;10.227.136.9;10.229.28.14;10.227.134.136;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;10.228.18.218;22.55.9.28;10.228.21.146;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.169;10.229.32.102;10.240.3.108;10.228.27.83;10.227.136.170;10.229.24.182;*************;22.55.76.160;10.228.21.244;10.227.133.239;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.228.27.179;10.229.103.25;10.227.131.253;10.228.28.245;10.229.202.103;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;10.227.132.18;22.55.127.15;22.55.72.23;22.55.5.8;10.240.3.67;*************;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.21.200;10.228.30.242;************1;10.228.31.23;10.227.136.11;10.227.128.35;10.229.30.78;10.227.140.48;10.227.133.114;22.55.127.8;10.227.133.58;10.228.21.131;10.240.2.76;10.227.136.5;10.240.4.173;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.229.29.174;10.227.136.28;10.240.5.206;22.55.62.181;10.227.135.98;10.229.26.3;22.55.62.180;10.240.3.134;10.229.201.196;10.229.97.253;10.227.129.186;10.227.137.173;10.240.6.65;22.55.8.128;10.229.200.132;10.229.27.133;10.227.136.67;22.55.62.177;22.55.72.35;10.229.96.240;22.55.6.17;22.55.62.252;22.55.72.15;10.227.129.90;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;22.55.62.218;10.229.29.120;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.228.25.66;10.227.137.120;10.229.103.206;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.56;22.55.56.165;10.227.141.206;10.229.201.151;10.228.24.161;10.228.17.109;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.229.185.83;10.227.137.163;10.229.29.69;10.227.136.242;10.228.30.33;10.228.29.179;10.229.201.72;10.228.30.21;10.229.103.109;10.229.101.50;10.240.2.124;10.240.2.193;10.240.5.180;22.55.67.58;22.55.65.188;10.227.137.178;10.228.27.71;22.55.4.39;10.227.134.181;10.240.3.194;10.229.35.40;10.227.133.40;10.229.101.64;22.55.8.120;10.229.201.145;10.227.136.39;10.229.24.85;10.228.25.130;*************;10.240.2.31;10.229.29.142;22.55.56.229;10.227.134.50;10.229.101.11;10.227.129.88;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;22.55.70.63;10.228.28.74;22.55.6.16;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.30.57;10.229.28.92;10.227.142.53;10.227.129.223;22.55.62.170;22.55.62.253;10.229.96.183;10.228.27.103;10.240.3.39;10.229.201.102;10.228.28.78;10.227.137.174;22.55.70.51;10.240.6.149;10.228.25.162;10.240.4.203;10.229.103.16;22.55.54.210;10.240.5.207;*************;10.227.136.126;10.228.27.187;22.55.64.117;10.229.30.51;22.55.58.1;10.229.96.218;22.55.8.47;10.228.21.208;10.228.25.119;22.55.72.21;10.229.30.44;10.229.201.160;10.229.96.57;10.227.136.41;22.55.127.5;10.229.29.252;10.229.30.79;10.228.29.36;10.227.136.114;22.55.1.93;************;10.229.27.147;10.227.136.66;10.240.6.170;10.229.28.78;10.229.103.94;10.227.141.13;10.227.135.120;10.229.31.7;22.55.62.184;10.227.131.91;10.240.4.14;10.227.136.4;*************;10.229.32.185;22.55.72.34;10.229.35.113;22.55.62.150;10.227.135.31;10.228.23.130;10.229.32.220;10.229.26.185;10.227.128.179;10.229.98.232;10.240.4.34;22.55.76.159;10.229.35.247;10.227.136.157;10.229.202.125;10.240.6.143;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;10.228.16.119;22.55.72.51;22.55.62.216;10.228.27.105;*************;10.227.143.121;22.55.56.151;22.55.72.50;10.229.30.77;10.229.24.180;10.240.3.124;10.227.137.198;10.240.4.38;10.227.128.193;10.228.24.102;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;10.228.29.244;22.55.62.42;10.228.31.200;10.229.202.152;22.55.62.168;10.229.35.248;10.228.31.5;10.227.131.95;10.227.129.167;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;22.55.127.14;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.240.4.37;10.229.35.77;22.55.1.56;10.229.35.64;10.228.31.36;22.55.127.12;22.55.62.154;22.55.64.116;10.227.142.41;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.229.102.178;10.228.21.209;10.229.96.241;22.55.127.13;10.229.30.109;10.227.140.151;10.228.28.51;10.240.4.53;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.25.145;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.227.143.246;10.228.21.198;10.227.140.32;10.229.101.153;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;10.229.35.215;**********;10.229.202.83;10.229.201.82;10.240.4.243;10.240.3.186;10.229.98.202;10.229.203.148;10.240.4.183;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.229.30.17;10.227.141.64;10.229.32.108;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.142.4;10.227.141.18;10.227.136.34;10.228.27.150;10.229.32.105;22.55.1.68;10.227.132.237;10.227.129.96;10.227.136.46;10.228.27.220;22.55.62.57;22.55.56.11;10.229.202.95;10.229.32.238;**********;22.55.127.2;************3;22.55.62.249;10.227.142.52;10.229.27.55;22.55.127.10;10.240.4.19;10.229.30.82;10.240.3.41;22.55.54.47;10.227.136.3;10.227.136.70;22.55.56.163;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;10.240.4.28;22.55.54.211;10.227.136.111;10.228.26.214;10.229.200.141;10.240.6.62;10.229.32.241;22.55.72.44;10.229.34.83;10.228.23.3;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;*************;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;10.229.96.219;10.229.28.43;22.55.75.106;22.55.64.129;22.55.56.184;10.227.129.89;22.55.64.115;10.240.4.20;10.229.34.210;10.229.101.109;10.229.34.40;10.229.25.134;10.227.141.11;10.240.4.55;10.228.27.86;************6;10.227.136.115;10.227.140.42;10.227.136.10;10.229.99.2;22.55.56.72;22.55.54.236;22.55.62.65;22.55.64.83;10.229.30.106;22.55.1.108;22.55.72.20;10.227.136.92;10.229.24.245;22.55.127.6;10.228.21.129;10.229.98.236;10.229.203.142;10.229.203.169;10.229.101.179;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.16.18;10.228.18.199;10.228.31.9;10.240.4.13;10.229.96.163;10.227.132.236;10.228.24.225;10.229.201.146;10.227.129.91;10.229.27.190;10.229.30.120;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.227.136.98;10.229.28.168;22.55.73.112;10.229.103.20;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;22.55.6.14;10.228.29.70;10.227.128.73;10.227.128.8;10.229.97.17;10.229.32.104;22.55.76.28;22.55.54.212;22.55.75.173;10.228.23.162;10.229.201.67;10.227.139.194;10.227.130.46;22.55.72.27;10.227.142.34;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;10.229.200.150;22.55.76.36;10.229.96.239;22.55.73.241;10.227.136.91;10.229.27.183;************;************;*************;***********;*************;*************;************;*************;*************;10.240.4.33;***********;**********;************;*************;*************;************;*************;**************;**************;*************;**************;**************;************;**********;***********;*************;************;**************;10.240.4.56;************;************,0.0,3.0,False,False,TLS 1.2 版协议检测【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
72323,low,1999-09-01,2013-07-17,根据探测到服务器支持的SSL加密协议原理，通过对目标机的登录测试进行漏洞验证。,*************;**************;10.227.136.16;***********;10.229.34.175;10.240.3.235;***********;*************;**********;*************;**********;**************;10.240.2.126;10.229.32.6;22.55.73.239;10.229.201.46;************;10.228.31.227;**************;22.55.72.38;10.229.97.106;**********;**********;**************;**************;10.228.25.153;10.227.131.89;10.240.6.40;10.228.27.17;10.229.24.178;10.227.143.120;22.55.62.215;10.240.2.197;22.55.62.41;10.240.6.77;10.228.28.33;22.55.72.47;10.229.203.208;10.240.6.148;22.55.3.83;10.229.28.13;22.55.3.195;10.229.35.16;22.55.72.46;10.227.137.160;10.228.25.194;10.227.136.99;10.227.137.181;22.55.62.145;10.228.30.244;10.228.24.248;10.229.201.142;22.55.72.30;22.55.72.42;22.55.56.135;10.229.203.218;10.228.22.32;10.229.27.142;10.229.24.174;10.227.135.41;10.229.202.35;22.55.3.114;10.227.136.6;10.240.3.109;10.229.33.133;22.55.6.20;10.228.17.12;10.229.25.6;22.55.3.197;22.55.57.48;22.55.72.41;************5;22.55.1.48;10.229.97.107;10.228.27.16;22.55.6.21;22.55.62.160;10.227.138.171;10.227.134.99;22.55.72.24;22.55.62.87;10.229.201.81;10.227.134.104;10.227.136.29;10.229.200.138;22.55.62.43;10.240.2.195;10.229.200.131;10.240.6.169;22.55.56.237;22.55.3.1;10.229.32.35;10.229.24.179;10.240.3.110;22.55.76.29;22.55.3.132;10.229.35.65;***********82;22.55.54.246;10.227.136.177;10.240.6.91;22.55.56.34;10.227.141.10;10.229.32.103;22.55.127.7;22.55.62.22;10.229.201.153;10.228.18.85;10.229.200.10;22.55.6.3;10.229.96.243;22.55.3.81;10.227.131.90;10.229.26.210;10.229.27.173;22.55.62.64;10.228.22.28;************7;22.55.56.238;10.227.130.218;10.229.97.240;10.227.131.75;10.229.27.68;10.229.98.165;10.229.201.157;10.227.136.136;22.55.3.194;10.228.26.209;10.227.137.175;10.229.200.106;10.229.186.138;22.55.62.61;10.229.97.50;22.55.127.1;10.240.3.228;10.228.21.167;22.55.56.149;10.227.143.235;10.229.98.195;22.55.60.56;10.228.27.23;10.227.135.201;10.229.201.52;10.240.4.70;22.55.73.251;22.55.62.169;10.227.136.118;22.55.3.161;10.227.136.139;10.228.16.5;10.227.141.70;22.55.8.56;22.55.62.171;10.228.25.200;22.55.73.238;10.240.6.89;22.55.56.177;10.229.202.94;10.229.24.246;22.55.3.98;10.229.99.15;22.55.62.185;10.229.35.29;10.229.26.105;10.240.4.235;22.55.62.121;10.229.201.62;22.55.3.25;22.55.64.113;10.240.2.192;22.55.72.22;10.228.18.104;22.55.6.2;22.55.3.3;22.55.3.164;22.55.62.30;10.229.97.197;10.227.141.126;10.229.24.131;10.228.21.205;10.228.30.88;22.55.56.219;10.227.136.12;22.55.72.40;10.227.137.164;22.55.4.65;10.228.18.82;10.229.96.68;10.227.136.71;10.229.26.211;22.55.72.17;10.240.6.41;10.240.6.147;22.55.65.187;22.55.3.49;10.229.27.167;10.229.96.220;10.229.202.60;10.229.24.175;10.228.21.214;10.229.97.21;10.229.32.137;22.55.68.35;22.55.72.25;22.55.56.252;10.229.34.37;10.229.98.51;22.55.68.21;10.229.28.107;10.240.2.186;10.229.102.33;10.228.30.241;22.55.3.163;10.240.2.187;22.55.3.116;10.229.30.123;22.55.56.228;10.240.2.194;22.55.56.12;10.240.2.173;22.55.3.26;10.228.29.165;22.55.56.166;10.229.29.75;10.240.3.107;22.55.73.248;10.227.143.118;***********3;10.229.97.43;10.227.136.52;10.227.128.42;10.227.139.57;10.240.6.27;*************;*************;10.227.136.9;22.55.73.111;10.229.200.126;10.229.28.14;22.55.73.249;10.229.24.140;10.240.2.123;22.55.73.240;10.240.5.182;22.55.72.19;22.55.72.18;10.229.96.141;10.227.140.1;10.229.32.102;10.240.3.108;22.55.3.226;10.227.136.170;22.55.3.97;10.229.24.182;22.55.76.160;10.228.21.244;10.229.184.29;10.227.136.108;10.229.27.46;22.55.72.53;10.229.33.10;10.227.131.253;10.229.184.239;10.228.17.124;10.229.34.66;10.229.29.78;10.227.137.179;22.55.127.15;22.55.5.8;22.55.72.23;*************;10.229.24.176;10.228.22.21;22.55.75.69;10.229.96.137;10.229.96.244;10.228.30.242;10.228.31.23;10.227.136.11;10.227.128.35;22.55.3.178;10.227.140.48;22.55.127.8;10.240.2.76;10.227.136.5;22.55.4.66;10.240.6.150;22.55.127.16;10.227.143.226;10.227.136.28;10.240.5.206;22.55.62.181;22.55.62.180;10.240.3.134;10.229.97.253;10.229.200.132;10.227.137.173;10.240.6.65;22.55.8.128;10.229.27.133;10.227.136.67;22.55.72.35;10.229.96.240;22.55.6.17;22.55.62.252;22.55.72.15;10.240.3.208;10.228.29.180;10.227.137.176;22.55.62.122;10.229.201.128;22.55.62.218;22.55.72.31;10.227.141.28;10.229.26.94;10.229.96.221;10.240.6.37;22.55.3.179;10.227.136.8;22.55.72.48;10.229.31.209;10.229.97.194;10.228.27.8;*************;10.229.97.105;22.55.62.56;22.55.56.165;10.229.201.151;10.229.186.104;22.55.72.14;10.240.6.151;22.55.62.18;10.227.128.212;10.227.137.163;10.229.29.69;10.227.136.242;10.228.29.179;22.55.3.196;10.229.201.72;10.240.5.232;10.228.28.229;10.240.2.193;10.240.2.124;22.55.67.58;22.55.65.188;10.227.137.178;10.227.134.181;22.55.64.106;10.240.3.194;10.227.130.71;10.229.35.40;10.229.101.64;22.55.3.18;10.229.201.145;10.227.136.39;10.228.25.130;22.55.56.229;22.55.3.115;10.229.101.11;10.229.101.138;10.229.35.203;10.227.139.173;22.55.72.13;10.227.141.12;10.240.6.78;10.229.24.177;10.240.6.144;***********2;10.228.28.74;22.55.6.16;10.228.26.213;10.229.97.153;10.228.24.84;10.228.23.132;22.55.62.153;10.229.101.100;10.240.6.140;10.227.136.62;10.229.28.92;10.227.129.223;10.227.142.53;10.228.27.103;22.55.62.170;22.55.62.253;10.240.3.39;10.229.201.102;10.227.137.174;10.229.185.49;10.240.6.149;10.228.25.162;22.55.54.210;10.240.5.207;*************;10.227.136.126;22.55.64.117;10.227.130.10;22.55.58.1;10.229.96.218;10.228.21.208;10.228.25.119;22.55.72.21;10.229.201.160;10.229.96.57;22.55.3.33;22.55.3.131;10.229.29.252;10.228.29.36;10.227.136.114;************;10.227.136.66;10.240.6.170;10.229.28.78;10.227.141.13;22.55.3.99;22.55.62.184;10.227.131.91;10.227.136.4;22.55.72.34;10.229.35.113;22.55.62.150;10.227.143.121;10.228.23.130;10.227.128.179;22.55.76.159;10.229.202.125;10.227.136.157;10.240.6.143;10.229.35.247;22.55.72.26;10.227.139.52;22.55.72.29;22.55.73.107;22.55.72.51;22.55.62.216;10.229.200.190;10.228.27.105;*************;22.55.56.151;22.55.72.50;10.229.24.180;10.227.137.198;10.227.128.193;10.229.201.85;22.55.56.173;10.228.24.152;10.228.28.9;10.229.200.130;22.55.3.17;22.55.8.135;10.229.96.138;10.240.3.144;22.55.72.45;22.55.62.42;10.228.31.200;22.55.62.168;22.55.3.225;10.228.31.5;10.227.131.95;22.55.8.54;10.240.3.148;10.227.136.30;10.228.26.210;10.229.32.182;10.227.137.189;10.229.96.209;22.55.62.20;22.55.72.11;22.55.56.35;10.227.141.16;10.227.141.59;10.229.28.122;10.229.35.77;10.228.31.36;22.55.127.12;22.55.62.154;22.55.64.116;10.229.202.102;10.227.128.9;22.55.67.60;10.228.24.179;10.228.21.209;10.229.96.241;22.55.127.13;10.229.30.109;10.227.140.151;10.229.200.241;10.240.5.183;10.240.5.176;22.55.73.250;10.229.32.235;10.229.35.42;10.227.136.1;10.227.140.60;10.229.29.76;10.229.96.222;10.229.35.193;10.227.129.224;10.227.136.141;10.229.35.155;22.55.54.209;10.240.2.125;10.227.128.41;10.227.140.61;22.55.3.146;22.55.3.4;10.229.201.82;10.229.202.83;10.240.4.243;10.240.3.186;10.229.203.148;10.227.136.60;10.228.18.22;22.55.62.155;10.227.143.111;10.227.141.64;***********99;10.228.22.31;10.229.29.157;10.227.136.51;10.227.141.18;22.55.3.177;10.227.136.34;10.229.32.105;22.55.1.68;22.55.3.162;10.227.132.237;10.227.129.96;10.227.136.46;22.55.62.57;10.229.202.95;10.229.32.238;22.55.68.13;************3;22.55.62.249;10.227.142.52;10.229.27.55;22.55.127.10;22.55.64.100;10.229.30.82;22.55.54.47;10.227.136.3;10.227.136.70;10.240.3.193;10.240.5.190;22.55.4.63;10.229.33.141;22.55.72.36;22.55.3.2;22.55.54.211;10.227.136.111;22.55.3.145;10.228.26.214;10.240.6.62;10.229.200.141;22.55.72.44;10.229.34.83;10.227.137.162;22.55.6.4;22.55.73.242;10.228.22.34;22.55.73.108;10.229.35.188;10.227.139.59;10.229.33.11;10.229.201.43;10.229.26.100;10.227.140.62;10.229.201.120;10.229.201.155;10.227.141.14;10.228.29.86;22.55.3.229;10.229.96.219;10.229.28.43;10.229.185.198;22.55.75.106;22.55.64.115;10.229.34.210;10.229.101.109;10.229.34.40;10.227.141.11;10.227.136.115;************6;10.227.140.42;10.229.102.252;10.227.136.10;10.229.186.228;22.55.62.65;22.55.72.20;10.227.136.92;10.229.24.245;22.55.127.6;10.229.98.236;22.55.3.82;10.229.203.142;10.240.2.174;10.228.28.39;10.229.201.149;10.228.27.7;10.227.136.102;10.228.18.199;10.228.31.9;10.227.132.236;10.228.24.225;10.229.201.146;10.227.129.91;10.229.27.190;22.55.72.10;10.227.139.53;10.228.22.101;10.228.21.213;10.228.17.62;10.227.136.98;10.229.28.168;22.55.73.112;22.55.127.3;10.229.200.139;10.229.201.58;10.227.128.150;10.228.29.70;10.227.128.8;10.229.97.17;22.55.76.28;22.55.54.212;10.229.201.67;22.55.3.227;22.55.72.27;22.55.127.4;10.229.96.139;10.240.3.209;10.229.202.39;10.229.29.68;10.227.141.115;22.55.76.36;10.229.96.239;22.55.73.241;10.229.96.69;10.227.136.91;10.229.27.183;************;************;*************;************;***********;*************;***********;***********;***********;**********;************;***********;*************;************;***********;22.55.1.3;**************;*************;**************;************;***********;***********;*************;************;**************;************;************;************,0.0,3.0,True,False,探测到服务器支持的SSL加密协议【原理扫描】,,0,2,1.2,1,0.5,0,5,1,4.2,4.2,2.1,1.834,10.261933333333333,低危漏洞(级别:low); 漏洞详情: 根据探测到服务器支持的SSL加密协议原理，通过对目标机的登录测试进行漏洞验证。,漏洞发现时间较久远(1999-09-01)，可能已修复
80001,high,2017-08-08,2017-07-24,,************;************,10.0,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-10137),CVE-2017-10137,2,10,10.0,121,10.0,0,10,1,5.2,5.2,2.6,4.06,73.38333333333334,高危漏洞(级别:high); 严重程度评分高(10.0分),漏洞未经确认验证; 漏洞发现时间较久远(2017-08-08)，可能已修复
80001,high,2017-10-19,2017-10-23,,************;************,9.9,4.0,False,False,Oracle WebLogic Server WLS-WebServices 组件拒绝服务漏洞(CVE-2017-10352),CVE-2017-10352,2,10,9.96,121,10.0,0,10,1,5.2,5.2,2.6,4.0552,73.37933333333334,高危漏洞(级别:high); 严重程度评分高(9.9分),漏洞未经确认验证; 漏洞发现时间较久远(2017-10-19)，可能已修复
80001,high,2021-01-19,2021-01-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-1994),CVE-2021-1994,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-19)，可能已修复
80001,high,2020-10-20,2020-10-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Core 安全漏洞(CVE-2020-14859),CVE-2020-14859,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-20)，可能已修复
80001,high,2020-10-20,2020-10-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Core 安全漏洞(CVE-2020-14841),CVE-2020-14841,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-20)，可能已修复
80001,high,2020-10-20,2020-10-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2020-14882),CVE-2020-14882,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-20)，可能已修复
80001,high,2020-07-14,2020-07-16,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic Server Core组件安全漏洞(CVE-2020-14645),CVE-2020-14645,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
80001,high,2018-05-14,2020-07-16,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2018-11058),CVE-2018-11058,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-05-14)，可能已修复
80001,high,2020-04-15,2020-04-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Core 组件访问控制错误漏洞(CVE-2020-2884),CVE-2020-2884,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
80001,high,2020-04-15,2020-04-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Core 组件访问控制错误漏洞(CVE-2020-2883),CVE-2020-2883,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
80001,high,2020-04-15,2020-04-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Core 组件访问控制错误漏洞(CVE-2020-2801),CVE-2020-2801,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
80001,high,2018-02-27,2023-08-31,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Spring Framework 组件安全漏洞(CVE-2018-1275),CVE-2018-1275,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-02-27)，可能已修复
80001,high,2021-07-20,2021-07-22,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic 安全漏洞(CVE-2021-2394),CVE-2021-2394,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
80001,high,2021-07-20,2021-07-22,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic 安全漏洞(CVE-2021-2397),CVE-2021-2397,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
80001,high,2021-07-20,2021-07-22,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic 安全漏洞(CVE-2021-2382),CVE-2021-2382,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
80001,high,2017-04-05,2025-05-12,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2017-7525),CVE-2017-7525,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2017-04-05)，可能已修复
80001,high,2020-01-15,2020-01-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Application Container - JavaEE 组件访问控制错误漏洞(CVE-2020-2546),CVE-2020-2546,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-01-15)，可能已修复
80001,high,2020-01-15,2020-01-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WSL Core 组件访问控制错误漏洞(CVE-2020-2551),CVE-2020-2551,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2020-01-15)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2019-2658),CVE-2019-2658,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,9.8,4.0,False,False,Oracle WebLogic Server EJB Container 组件安全漏洞(CVE-2019-2646),CVE-2019-2646,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2019-2645),CVE-2019-2645,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
80001,high,2017-04-18,2018-07-20,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WL Diagnostics Framework 组件安全漏洞(CVE-2017-5645),CVE-2017-5645,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2017-04-18)，可能已修复
71074,high,2022-10-21,2025-07-03,,10.228.16.18,9.8,4.0,False,False,Python 安全漏洞(CVE-2022-37454),CVE-2022-37454,2,10,9.92,14,7.0,0,10,2,5.2,5.2,5.2,11.0608,79.21733333333333,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-21)，可能已修复
80001,high,2018-07-19,2018-07-20,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2018-2893),CVE-2018-2893,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-07-19)，可能已修复
71074,high,2022-06-01,2023-01-19,,10.228.16.18;************;10.228.29.239,9.8,4.0,False,False,Oracle MySQL cURL 组件输入验证错误漏洞(CVE-2022-32221),CVE-2022-32221,2,10,9.92,15,7.5,0,10,1,5.2,5.2,2.6,3.9004,73.25033333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2022-06-01)，可能已修复
71074,high,2021-05-06,2025-07-03,,10.228.16.18,9.8,4.0,False,False,Python 安全漏洞(CVE-2021-29921),CVE-2021-29921,2,10,9.92,14,7.0,0,10,2,5.2,5.2,5.2,11.0608,79.21733333333333,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-05-06)，可能已修复
80001,high,2018-10-16,2023-08-31,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS 组件远程代码执行漏洞(CVE-2018-3245),CVE-2018-3245,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
80001,high,2021-01-19,2021-01-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2075),CVE-2021-2075,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-19)，可能已修复
80001,high,2016-07-21,2017-06-26,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic Server安全漏洞(CVE-2016-3586),CVE-2016-3586,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-21)，可能已修复
80001,high,2016-07-21,2017-06-26,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic Server安全漏洞(CVE-2016-3510),CVE-2016-3510,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-21)，可能已修复
80001,high,2015-09-29,2017-06-26,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2015-7501),CVE-2015-7501,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2015-09-29)，可能已修复
71074,high,2016-10-18,2016-12-06,,*************,9.8,4.0,False,False,Apache Tomcat 安全限制绕过漏洞(CVE-2016-8735),CVE-2016-8735,2,10,9.92,25,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-18)，可能已修复
80001,high,2017-01-27,2017-04-09,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2017-3248),CVE-2017-3248,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2017-01-27)，可能已修复
80001,high,2017-03-10,2017-04-25,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-5638),CVE-2017-5638,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2017-03-10)，可能已修复
80001,high,2018-10-16,2019-03-13,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS 组件远程代码执行漏洞(CVE-2018-3252),CVE-2018-3252,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
80001,high,2021-01-19,2021-01-21,,************;************,9.8,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2047),CVE-2021-2047,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-19)，可能已修复
71074,high,2018-05-17,2018-08-23,,*************,9.8,4.0,False,False,Apache Tomcat CORS Filter 安全漏洞(CVE-2018-8014),CVE-2018-8014,2,10,9.92,25,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-05-17)，可能已修复
80001,high,2018-04-17,2018-04-20,,************;************,9.8,4.0,False,False,Oracle WebLogic Server T3 反序列化漏洞(CVE-2018-2628),CVE-2018-2628,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-04-17)，可能已修复
71074,high,2019-07-19,2025-07-11,,10.228.18.217,9.8,4.0,False,False,ProFTPD 访问控制错误漏洞(CVE-2019-12815),CVE-2019-12815,2,10,9.92,11,5.5,0,10,2,5.2,5.2,5.2,10.880799999999999,79.06733333333334,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-07-19)，可能已修复
80001,high,2015-11-18,2017-09-06,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS Security 组件安全漏洞(CVE-2015-4852),CVE-2015-4852,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2015-11-18)，可能已修复
80001,high,2019-10-14,2020-04-15,,************;************,9.8,4.0,False,False,Oracle WebLogic Server Console 组件安全漏洞(CVE-2019-17571),CVE-2019-17571,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2019-10-14)，可能已修复
80001,high,2018-10-16,2023-08-31,,************;************,9.8,4.0,False,False,Oracle WebLogic Server WLS 组件远程代码执行漏洞(CVE-2018-3191),CVE-2018-3191,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
71074,high,2022-08-05,2023-04-22,,10.228.16.18;************;10.228.29.239;10.229.27.93,9.8,4.0,False,False,Oracle MySQL zlib安全漏洞(CVE-2022-37434),CVE-2022-37434,2,10,9.92,8,4.0,0,10,1,5.2,5.2,2.6,3.6904,73.07533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2022-08-05)，可能已修复
71074,high,2023-07-19,2023-07-25,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,9.8,4.0,False,False,OpenSSH 安全漏洞(CVE-2023-38408),CVE-2023-38408,2,10,9.92,6,3.0,0,10,1,5.2,5.2,2.6,3.6304,73.02533333333334,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2023-07-19)，可能已修复
71074,high,2022-05-03,2022-05-11,,10.228.16.18;************;10.228.29.239,9.8,4.0,False,False,Oracle Mysql OpenSSL组件操作系统命令注入漏洞(CVE-2022-1292),CVE-2022-1292,2,10,9.92,15,7.5,0,10,1,5.2,5.2,2.6,3.9004,73.25033333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-03)，可能已修复
71074,high,2023-07-20,2023-10-11,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,9.8,4.0,False,False,Oracle MySQL curl/libcURL 安全漏洞(CVE-2023-38545),CVE-2023-38545,2,10,9.92,6,3.0,0,10,1,5.2,5.2,2.6,3.6304,73.02533333333334,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2023-07-20)，可能已修复
71074,high,2021-10-19,2021-10-21,,10.228.16.18;************;10.228.29.239,9.8,4.0,False,False,Oracle MySQL Server 缓冲区溢出漏洞(CVE-2021-3711),CVE-2021-3711,2,10,9.92,15,7.5,0,10,1,5.2,5.2,2.6,3.9004,73.25033333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-10-19)，可能已修复
71074,high,2021-06-06,2021-06-08,,10.227.129.210,9.8,4.0,False,False,NGINX 安全漏洞(CVE-2017-20005),CVE-2017-20005,2,10,9.92,1,0.5,0,10,1,5.2,5.2,2.6,3.4804,72.90033333333334,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2021-06-06)，可能已修复
71074,high,2024-02-08,2024-05-17,,************,9.8,4.0,False,False,MongoDB Server 安全漏洞(CVE-2024-1351),CVE-2024-1351,2,10,9.92,47,10.0,0,10,2,5.2,5.2,5.2,11.420800000000002,79.51733333333334,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-02-08)，可能已修复
80001,high,2016-10-25,2017-06-27,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2016-5531),CVE-2016-5531,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-25)，可能已修复
80001,high,2016-10-25,2017-06-27,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2016-5535),CVE-2016-5535,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-25)，可能已修复
60319,high,2021-04-27,2021-06-04,根据nacos权限绕过漏洞(CVE-2021-29441)原理，通过跟目标机的数据交互情况进行漏洞验证,************,9.8,3.0,True,False,nacos auth/users 权限绕过漏洞(CVE-2021-29441)【原理扫描】,CVE-2021-29441,2,10,9.92,4,2.0,0,10,2,5.2,5.2,5.2,10.4608,78.71733333333333,高危漏洞(级别:high); 严重程度评分高(9.8分); 重要业务网段资产; 漏洞详情: 根据nacos权限绕过漏洞(CVE-2021-29441)原理，通过跟目标机的数据交互情况进行漏洞验证...,漏洞发现时间较久远(2021-04-27)，可能已修复
80001,high,2015-12-09,2017-06-26,,************;************,9.8,4.0,False,False,Oracle Fusion Middleware WebLogic Server远程安全漏洞(CVE-2016-0638),CVE-2016-0638,2,10,9.92,121,10.0,0,10,1,5.2,5.2,2.6,4.0504,73.37533333333333,高危漏洞(级别:high); 严重程度评分高(9.8分),漏洞未经确认验证; 漏洞发现时间较久远(2015-12-09)，可能已修复
71074,high,2024-06-06,2025-01-22,,************;************,9.1,4.0,False,False,Oracle MySQL Kerberos 安全漏洞(CVE-2024-37371),CVE-2024-37371,2,10,9.64,48,10.0,0,10,1,5.2,5.2,2.6,4.0168,73.34733333333334,高危漏洞(级别:high); 严重程度评分高(9.1分),漏洞未经确认验证; 漏洞发现时间较久远(2024-06-06)，可能已修复
71074,high,2016-05-24,2016-11-23,,*************,9.1,4.0,False,False,Apache Tomcat Security Manager 安全限制绕过漏洞(CVE-2016-5018),CVE-2016-5018,2,10,9.64,25,10.0,0,10,1,5.2,5.2,2.6,4.0168,73.34733333333334,高危漏洞(级别:high); 严重程度评分高(9.1分),漏洞未经确认验证; 漏洞发现时间较久远(2016-05-24)，可能已修复
71074,high,2021-07-21,2023-03-14,,10.228.16.18;************;10.228.29.239,9.1,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-22945),CVE-2021-22945,2,10,9.64,15,7.5,0,10,1,5.2,5.2,2.6,3.8668,73.22233333333334,高危漏洞(级别:high); 严重程度评分高(9.1分),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-21)，可能已修复
71074,high,2017-04-17,2017-04-09,,*************,9.1,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2017-5648),CVE-2017-5648,2,10,9.64,25,10.0,0,10,1,5.2,5.2,2.6,4.0168,73.34733333333334,高危漏洞(级别:high); 严重程度评分高(9.1分),漏洞未经确认验证; 漏洞发现时间较久远(2017-04-17)，可能已修复
71074,high,2023-08-07,2024-01-20,,************;************,8.8,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2023-39975),CVE-2023-39975,2,10,9.52,48,10.0,0,10,1,5.2,5.2,2.6,4.0024,73.33533333333334,高危漏洞(级别:high); 严重程度评分高(8.8分),漏洞未经确认验证; 漏洞发现时间较久远(2023-08-07)，可能已修复
71074,high,2016-02-24,2016-03-04,,*************,8.8,4.0,False,False,Apache Tomcat session-persistence 远程代码执行漏洞(CVE-2016-0714),CVE-2016-0714,2,10,9.52,25,10.0,0,10,1,5.2,5.2,2.6,4.0024,73.33533333333334,高危漏洞(级别:high); 严重程度评分高(8.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-02-24)，可能已修复
71074,high,2015-07-01,2016-03-04,,*************,8.8,4.0,False,False,Apache Tomcat Manager和Host Manager应用程序安全漏洞(CVE-2015-5351),CVE-2015-5351,2,10,9.52,25,10.0,0,10,1,5.2,5.2,2.6,4.0024,73.33533333333334,高危漏洞(级别:high); 严重程度评分高(8.8分),漏洞未经确认验证; 漏洞发现时间较久远(2015-07-01)，可能已修复
60726,high,2021-11-01,2023-03-15,,************,8.8,3.0,False,False,Nacos 默认secret.key密钥漏洞(CVE-2021-43116)【原理扫描】,CVE-2021-43116,2,10,9.52,4,2.0,0,10,2,5.2,5.2,5.2,10.3648,78.63733333333333,高危漏洞(级别:high); 严重程度评分高(8.8分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-11-01)，可能已修复
80001,high,2016-10-25,2017-06-27,,************;************,8.8,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2016-3505),CVE-2016-3505,2,10,9.52,121,10.0,0,10,1,5.2,5.2,2.6,4.0024,73.33533333333334,高危漏洞(级别:high); 严重程度评分高(8.8分),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-25)，可能已修复
71074,high,2022-05-13,2022-05-25,,10.228.26.65,8.6,4.0,False,False,Apache Tomcat 代码问题漏洞(CVE-2022-25762),CVE-2022-25762,2,10,9.44,15,7.5,0,10,2,5.2,5.2,5.2,11.0056,79.17133333333334,高危漏洞(级别:high); 严重程度评分高(8.6分); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-05-13)，可能已修复
80001,high,2017-08-08,2017-07-24,,************;************,8.6,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-10147),CVE-2017-10147,2,10,9.44,121,10.0,0,10,1,5.2,5.2,2.6,3.9928,73.32733333333333,高危漏洞(级别:high); 严重程度评分高(8.6分),漏洞未经确认验证; 漏洞发现时间较久远(2017-08-08)，可能已修复
71074,high,2015-08-02,2016-02-10,,************;*************,8.5,4.0,False,False,Openssh MaxAuthTries限制绕过漏洞(CVE-2015-5600),CVE-2015-5600,2,10,9.4,13,6.5,0,10,1,5.2,5.2,2.6,3.778,73.14833333333334,高危漏洞(级别:high); 严重程度评分高(8.5分),漏洞未经确认验证; 漏洞发现时间较久远(2015-08-02)，可能已修复
80001,high,2018-07-19,2018-07-20,,************;************,8.3,4.0,False,False,Oracle WebLogic Server JSF 组件安全漏洞(CVE-2018-2935),CVE-2018-2935,2,10,9.32,121,10.0,0,10,1,5.2,5.2,2.6,3.9784,73.31533333333333,高危漏洞(级别:high); 严重程度评分高(8.3分),漏洞未经确认验证; 漏洞发现时间较久远(2018-07-19)，可能已修复
80001,high,2020-07-14,2020-07-16,,************;************,8.2,4.0,False,False,Oracle Fusion Middleware WebLogic Server Web Container组件安全漏洞(CVE-2020-14588),CVE-2020-14588,2,10,9.28,121,10.0,0,10,1,5.2,5.2,2.6,3.9736,73.31133333333334,高危漏洞(级别:high); 严重程度评分高(8.2分),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
80001,high,2019-10-17,2019-10-21,,************;************,8.1,4.0,False,False,Oracle WebLogic Server Console 组件代码执行漏洞(CVE-2019-2891),CVE-2019-2891,2,10,9.24,121,10.0,0,10,1,5.2,5.2,2.6,3.9688,73.30733333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2019-10-17)，可能已修复
80001,high,2016-07-04,2017-04-25,,************;************,8.1,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2016-1181),CVE-2016-1181,2,10,9.24,121,10.0,0,10,1,5.2,5.2,2.6,3.9688,73.30733333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-04)，可能已修复
71074,high,2015-07-01,2016-03-04,,*************,8.1,4.0,False,False,Apache Tomcat 会话固定漏洞(CVE-2015-5346),CVE-2015-5346,2,10,9.24,25,10.0,0,10,1,5.2,5.2,2.6,3.9688,73.30733333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2015-07-01)，可能已修复
71074,high,2016-07-18,2016-07-26,,*************,8.1,4.0,False,False,Apache Tomcat HTTP_PROXY环境变量安全漏洞(CVE-2016-5388),CVE-2016-5388,2,10,9.24,25,10.0,0,10,1,5.2,5.2,2.6,3.9688,73.30733333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-18)，可能已修复
71074,high,2022-10-13,2024-05-17,,10.228.16.18;************;10.228.29.239,8.1,4.0,False,False,Oracle MySQL cURL 组件输入验证错误漏洞(CVE-2022-42915),CVE-2022-42915,2,10,9.24,15,7.5,0,10,1,5.2,5.2,2.6,3.8188,73.18233333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2022-10-13)，可能已修复
71074,high,2021-07-20,2021-07-22,,10.228.16.18;10.228.29.239,8.1,4.0,False,False,Oracle MySQL Server安全漏洞(CVE-2021-22901),CVE-2021-22901,2,10,9.24,9,4.5,0,10,1,5.2,5.2,2.6,3.6388000000000003,73.03233333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
71074,high,2019-10-14,2021-07-22,,10.228.16.18;10.228.29.239,8.1,4.0,False,False,Oracle MySQL Server安全漏洞(CVE-2019-17543),CVE-2019-17543,2,10,9.24,9,4.5,0,10,1,5.2,5.2,2.6,3.6388000000000003,73.03233333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2019-10-14)，可能已修复
71074,high,2022-07-20,2022-07-21,,10.228.16.18;************;10.228.29.239,8.1,4.0,False,False,Oracle MySQL cURL 组件输入验证错误漏洞(CVE-2022-27778),CVE-2022-27778,2,10,9.24,15,7.5,0,10,1,5.2,5.2,2.6,3.8188,73.18233333333333,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-20)，可能已修复
71074,high,2016-01-15,2023-09-08,,************;*************,8.1,4.0,False,False,OpenSSH roaming_common.c堆缓冲区溢出漏洞(CVE-2016-0778),CVE-2016-0778,2,10,9.24,13,6.5,0,10,1,5.2,5.2,2.6,3.7588000000000004,73.13233333333334,高危漏洞(级别:high); 严重程度评分高(8.1分),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-15)，可能已修复
71074,high,2016-12-19,2016-12-26,,************;*************,7.8,4.0,False,False,OpenSSH 安全限制绕过漏洞(CVE-2016-10012),CVE-2016-10012,2,10,9.120000000000001,13,6.5,0,10,1,5.2,5.2,2.6,3.7444,73.12033333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-12-19)，可能已修复
71074,high,2020-07-24,2020-07-30,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,7.8,4.0,False,False,OpenSSH 命令注入漏洞(CVE-2020-15778),CVE-2020-15778,2,10,9.120000000000001,6,3.0,0,10,1,5.2,5.2,2.6,3.5344,72.94533333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-24)，可能已修复
71074,high,2022-02-22,2022-02-23,,10.227.132.243,7.8,4.0,False,False,MariaDB内存错误引用漏洞(CVE-2022-24050),CVE-2022-24050,2,10,9.120000000000001,51,10.0,0,10,1,5.2,5.2,2.6,3.9544,73.29533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-02-22)，可能已修复
71074,high,2013-12-03,2015-05-08,,*************,7.8,4.0,False,False,Apache Tomcat拒绝服务漏洞(CVE-2014-0230),CVE-2014-0230,2,10,9.120000000000001,25,10.0,0,10,1,5.2,5.2,2.6,3.9544,73.29533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2013-12-03)，可能已修复
71074,high,2022-02-22,2022-02-23,,10.227.132.243,7.8,4.0,False,False,MariaDB权限提升漏洞(CVE-2022-24051),CVE-2022-24051,2,10,9.120000000000001,51,10.0,0,10,1,5.2,5.2,2.6,3.9544,73.29533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-02-22)，可能已修复
71074,high,2022-10-19,2023-03-29,,10.227.132.243;************;10.229.98.20;10.227.129.210;************;10.228.16.18;10.228.18.217;*************,7.8,4.0,False,False,nginx 缓冲区错误漏洞(CVE-2022-41741),CVE-2022-41741,2,10,9.120000000000001,2,1.0,0,10,1,5.2,5.2,2.6,3.4144,72.84533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-10-19)，可能已修复
71074,high,2017-05-04,2021-06-08,,************,7.8,4.0,False,False,Pivotal RabbitMQ和RabbitMQ for PCF 信息泄露漏洞(CVE-2017-4966),CVE-2017-4966,2,10,9.120000000000001,47,10.0,0,10,2,5.2,5.2,5.2,11.228800000000001,79.35733333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2017-05-04)，可能已修复
71074,high,2022-02-22,2022-02-23,,10.227.132.243,7.8,4.0,False,False,MariaDB堆栈缓冲区溢出漏洞(CVE-2022-24048),CVE-2022-24048,2,10,9.120000000000001,51,10.0,0,10,1,5.2,5.2,2.6,3.9544,73.29533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-02-22)，可能已修复
71074,high,2022-02-22,2022-02-23,,10.227.132.243,7.8,4.0,False,False,MariaDB堆缓冲区溢出漏洞(CVE-2022-24052),CVE-2022-24052,2,10,9.120000000000001,51,10.0,0,10,1,5.2,5.2,2.6,3.9544,73.29533333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-02-22)，可能已修复
71074,high,2015-11-24,2017-01-17,,************;*************,7.8,4.0,False,False,OpenSSH do_setup_env函数权限提升漏洞(CVE-2015-8325),CVE-2015-8325,2,10,9.120000000000001,13,6.5,0,10,1,5.2,5.2,2.6,3.7444,73.12033333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2015-11-24)，可能已修复
71074,high,2021-05-25,2021-06-08,,10.229.98.20;10.227.129.210;************;10.228.16.18;10.228.18.217,7.7,4.0,False,False,nginx 安全漏洞(CVE-2021-23017),CVE-2021-23017,2,10,9.08,1,0.5,0,10,1,5.2,5.2,2.6,3.3796000000000004,72.81633333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-05-25)，可能已修复
71074,high,2022-04-13,2022-05-24,,10.228.16.18,7.6,4.0,False,False,Python 命令注入漏洞(CVE-2015-20107),CVE-2015-20107,2,10,9.04,14,7.0,0,10,2,5.2,5.2,5.2,10.8496,79.04133333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-13)，可能已修复
71074,high,2018-08-01,2018-08-23,,*************,7.5,4.0,False,False,Apache Tomcat拒绝服务漏洞(CVE-2018-1336),CVE-2018-1336,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-08-01)，可能已修复
71074,high,2023-12-21,2025-07-11,,10.228.18.217,7.5,4.0,False,False,ProFTPd 安全漏洞(CVE-2023-51713),CVE-2023-51713,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-12-21)，可能已修复
71074,high,2023-01-31,2023-07-23,,************;************,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-4899),CVE-2022-4899,2,10,9.0,48,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-01-31)，可能已修复
71074,high,2023-04-18,2023-04-22,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21912),CVE-2023-21912,2,10,9.0,8,4.0,0,10,1,5.2,5.2,2.6,3.58,72.98333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-04-18)，可能已修复
71074,high,2022-10-20,2023-04-22,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.5,4.0,False,False,Oracle MySQL curl安全漏洞(CVE-2022-43551),CVE-2022-43551,2,10,9.0,8,4.0,0,10,1,5.2,5.2,2.6,3.58,72.98333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-10-20)，可能已修复
71074,high,2018-11-08,2018-11-13,,10.227.129.210;************,7.5,4.0,False,False,nginx 安全漏洞(CVE-2018-16844),CVE-2018-16844,2,10,9.0,7,3.5,0,10,1,5.2,5.2,2.6,3.5500000000000003,72.95833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-11-08)，可能已修复
71074,high,2023-04-18,2023-04-22,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-0215),CVE-2023-0215,2,10,9.0,8,4.0,0,10,1,5.2,5.2,2.6,3.58,72.98333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-04-18)，可能已修复
71074,high,2023-02-01,2023-03-01,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 拒绝服务漏洞(CVE-2023-24998),CVE-2023-24998,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-02-01)，可能已修复
71074,high,2021-08-26,2025-07-03,,10.228.16.18,7.5,4.0,False,False,Python 安全漏洞(CVE-2021-3737),CVE-2021-3737,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-08-26)，可能已修复
71074,high,2023-09-27,2025-07-04,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2023-5157),CVE-2023-5157,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-09-27)，可能已修复
80001,high,2021-04-21,2021-04-21,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2157),CVE-2021-2157,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-21)，可能已修复
71074,high,2017-06-06,2017-06-01,,*************,7.5,4.0,False,False,Apache Tomcat安全绕过漏洞(CVE-2017-5664),CVE-2017-5664,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-06-06)，可能已修复
71074,high,2018-01-21,2019-04-08,,************;*************,7.5,4.0,False,False,OpenSSH多个拒绝服务漏洞(CVE-2016-10708),CVE-2016-10708,2,10,9.0,13,6.5,0,10,1,5.2,5.2,2.6,3.73,73.10833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-01-21)，可能已修复
80001,high,2018-08-23,2023-08-31,,************;************,7.5,4.0,False,False,Oracle WebLogic Server Sample apps (Spring Framework)组件访问控制错误漏洞(CVE-2018-15756),CVE-2018-15756,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-08-23)，可能已修复
80001,high,2020-07-14,2020-07-16,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server 安全漏洞(CVE-2020-2967),CVE-2020-2967,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
71074,high,2016-10-18,2016-12-14,,*************,7.5,4.0,False,False,Apache Tomcat信息泄露漏洞(CVE-2016-8745),CVE-2016-8745,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-18)，可能已修复
80001,high,2020-07-14,2020-07-16,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server 安全漏洞(CVE-2020-14589),CVE-2020-14589,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
71074,high,2016-07-04,2016-07-06,,*************,7.5,4.0,False,False,Apache Tomcat拒绝服务漏洞(CVE-2016-3092),CVE-2016-3092,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-04)，可能已修复
76960,high,2016-10-12,2016-10-25,,10.228.18.217,7.5,3.0,False,False,"OpenSSL ""SSL-Death-Alert"" 拒绝服务漏洞(CVE-2016-8610)【原理扫描】",CVE-2016-8610,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2016-10-12)，可能已修复
71074,high,2016-08-12,2016-11-23,,*************,7.5,4.0,False,False,Apache Tomcat Security Manager 安全限制绕过漏洞(CVE-2016-6796),CVE-2016-6796,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-12)，可能已修复
71074,high,2016-08-12,2016-11-23,,*************,7.5,4.0,False,False,Apache Tomcat 安全限制绕过漏洞(CVE-2016-6797),CVE-2016-6797,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-12)，可能已修复
71074,high,2022-11-23,2025-07-11,,10.228.18.217,7.5,4.0,False,False,ProFTPd 安全漏洞(CVE-2021-46854),CVE-2021-46854,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-11-23)，可能已修复
71074,high,2014-04-01,2014-03-27,,*************,7.5,4.0,False,False,Apache Commons FileUpload 和 Tomcat拒绝服务漏洞(CVE-2014-0050),CVE-2014-0050,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2014-04-01)，可能已修复
60324,high,2021-04-27,2021-07-05,根据nacos弱身份认证漏洞原理，通过跟目标机的数据交互情况进行漏洞验证,************,7.5,3.0,True,False,nacos derby 弱身份认证漏洞(CVE-2021-29442)【原理扫描】,CVE-2021-29442,2,10,9.0,4,2.0,0,10,2,5.2,5.2,5.2,10.24,78.53333333333333,高危漏洞(级别:high); 重要业务网段资产; 漏洞详情: 根据nacos弱身份认证漏洞原理，通过跟目标机的数据交互情况进行漏洞验证,漏洞发现时间较久远(2021-04-27)，可能已修复
80001,high,2020-04-15,2020-04-15,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2020-2828),CVE-2020-2828,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
80001,high,2014-04-15,2017-09-06,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server组件安全漏洞(CVE-2014-0107),CVE-2014-0107,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2014-04-15)，可能已修复
71074,high,2025-01-09,2025-07-11,,10.228.18.217,7.5,4.0,False,False,ProFTPd 安全漏洞(CVE-2024-57392),CVE-2024-57392,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证
71074,high,2020-03-20,2020-03-20,,10.228.16.18,7.5,4.0,False,False,Python 代码问题漏洞(CVE-2020-10735),CVE-2020-10735,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2020-03-20)，可能已修复
80001,high,2015-02-28,2021-07-22,,************;************,7.5,4.0,False,False,Oracle WebLogic Server Third Party Tools 组件安全漏洞(CVE-2015-0254),CVE-2015-0254,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2015-02-28)，可能已修复
80001,high,2016-01-20,2017-09-06,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件任意代码执行漏洞(CVE-2016-0572),CVE-2016-0572,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-20)，可能已修复
80001,high,2013-02-19,2017-06-27,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2013-2186),CVE-2013-2186,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2013-02-19)，可能已修复
80001,high,2014-04-30,2017-06-27,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2014-0114),CVE-2014-0114,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2014-04-30)，可能已修复
80001,high,2014-04-15,2017-06-27,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 任意代码执行漏洞(CVE-2014-2470),CVE-2014-2470,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2014-04-15)，可能已修复
80001,high,2016-01-20,2017-09-06,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server组件远程安全漏洞(CVE-2016-0573),CVE-2016-0573,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-20)，可能已修复
71074,high,2019-10-21,2019-11-07,,10.228.18.217,7.5,4.0,False,False,ProFTPD 安全漏洞(CVE-2019-18217),CVE-2019-18217,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-10-21)，可能已修复
71074,high,2019-11-25,2019-12-12,,10.228.18.217,7.5,4.0,False,False,ProFTPD 授权问题漏洞(CVE-2019-19271),CVE-2019-19271,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-11-25)，可能已修复
71074,high,2019-11-25,2019-12-12,,10.228.18.217,7.5,4.0,False,False,ProFTPD 资源管理错误漏洞(CVE-2019-19272),CVE-2019-19272,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-11-25)，可能已修复
71074,high,2019-11-25,2019-12-12,,10.228.18.217,7.5,4.0,False,False,ProFTPD 授权问题漏洞(CVE-2019-19270),CVE-2019-19270,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2019-11-25)，可能已修复
71074,high,2019-08-15,2020-01-08,,10.227.129.210;************,7.5,4.0,False,False,HTTP/2 安全漏洞(CVE-2019-9511),CVE-2019-9511,2,10,9.0,7,3.5,0,10,1,5.2,5.2,2.6,3.5500000000000003,72.95833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-08-15)，可能已修复
71074,high,2019-08-15,2020-01-08,,10.227.129.210;************,7.5,4.0,False,False,HTTP/2 安全漏洞(CVE-2019-9513),CVE-2019-9513,2,10,9.0,7,3.5,0,10,1,5.2,5.2,2.6,3.5500000000000003,72.95833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-08-15)，可能已修复
71074,high,2022-11-09,2024-05-17,,10.228.16.18,7.5,4.0,False,False,Python不受控制的资源消耗漏洞(CVE-2022-45061),CVE-2022-45061,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-11-09)，可能已修复
80001,high,2016-01-20,2017-09-06,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server组件远程安全漏洞(CVE-2016-0574),CVE-2016-0574,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-20)，可能已修复
71074,high,2022-10-13,2024-05-17,,10.228.16.18;************;10.228.29.239,7.5,4.0,False,False,Oracle MySQL cURL 组件输入验证错误漏洞(CVE-2022-42916),CVE-2022-42916,2,10,9.0,15,7.5,0,10,1,5.2,5.2,2.6,3.79,73.15833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-10-13)，可能已修复
80001,high,2020-10-20,2020-10-21,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2020-14820),CVE-2020-14820,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-20)，可能已修复
71074,high,2021-06-01,2021-06-08,,************;************,7.5,4.0,False,False,RabbitMQ输入验证错误漏洞(CVE-2021-22116),CVE-2021-22116,2,10,9.0,2,1.0,0,10,1,5.2,5.2,2.6,3.4000000000000004,72.83333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-06-01)，可能已修复
80001,high,2016-01-20,2017-09-06,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server组件信息泄露漏洞(CVE-2016-0577),CVE-2016-0577,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-20)，可能已修复
71074,high,2022-12-13,2024-05-17,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-4450),CVE-2022-4450,2,10,9.0,8,4.0,0,10,1,5.2,5.2,2.6,3.58,72.98333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-12-13)，可能已修复
71074,high,2021-07-21,2021-10-21,,10.228.16.18;************;10.228.29.239,7.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-22926),CVE-2021-22926,2,10,9.0,15,7.5,0,10,1,5.2,5.2,2.6,3.79,73.15833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-21)，可能已修复
80001,high,2021-07-20,2021-07-22,,************;************,7.5,4.0,False,False,Oracle Fusion Middleware WebLogic 安全漏洞(CVE-2021-2378),CVE-2021-2378,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
80001,high,2021-07-20,2021-07-22,,************;************,7.5,4.0,False,False,Oracle WebLogic Server安全漏洞(CVE-2021-2376),CVE-2021-2376,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
71247,high,2016-01-29,2017-09-12,"根据SSL/TLS协议信息泄露漏洞(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.240.2.221;10.228.29.100;10.227.129.151,7.5,3.0,True,False,SSL/TLS协议信息泄露漏洞(CVE-2016-2183)【原理扫描】,CVE-2016-2183,2,10,9.0,2,1.0,0,10,1,5.2,5.2,2.6,3.4000000000000004,72.83333333333333,"高危漏洞(级别:high); 漏洞详情: 根据SSL/TLS协议信息泄露漏洞(CVE-2016-2183)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2016-01-29)，可能已修复
71074,high,2017-04-17,2017-09-19,,*************,7.5,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2017-5647),CVE-2017-5647,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-04-17)，可能已修复
80001,high,2017-10-19,2017-10-23,,************;************,7.5,4.0,False,False,Oracle WebLogic Server WLS Security 组件安全漏洞(CVE-2017-10271),CVE-2017-10271,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-10-19)，可能已修复
71074,high,2024-01-19,2024-03-21,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2024-23672),CVE-2024-23672,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-01-19)，可能已修复
71074,high,2024-01-25,2024-03-21,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 输入验证错误漏洞(CVE-2024-24549),CVE-2024-24549,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-01-25)，可能已修复
71074,high,2023-06-25,2024-02-27,,10.228.16.18,7.5,4.0,False,False,Python 安全漏洞(CVE-2023-36632),CVE-2023-36632,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-06-25)，可能已修复
71074,high,2023-08-23,2024-02-27,,************,7.5,4.0,False,False,MongoDB Server 信任管理问题漏洞(CVE-2023-1409),CVE-2023-1409,2,10,9.0,47,10.0,0,10,2,5.2,5.2,5.2,11.200000000000001,79.33333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-08-23)，可能已修复
71074,high,2021-05-05,2024-02-27,,************,7.5,4.0,False,False,MongoDB 缓冲区错误漏洞(CVE-2021-32040),CVE-2021-32040,2,10,9.0,47,10.0,0,10,2,5.2,5.2,5.2,11.200000000000001,79.33333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-05-05)，可能已修复
71074,high,2021-10-25,2022-01-20,,10.228.16.18;************;10.228.29.239,7.5,4.0,False,False,Oracle MySQL Server 信息泄露漏洞(CVE-2021-22946),CVE-2021-22946,2,10,9.0,15,7.5,0,10,1,5.2,5.2,2.6,3.79,73.15833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-10-25)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,7.5,4.0,False,False,Oracle WebLogic Server WLS 组件安全漏洞(CVE-2019-2650),CVE-2019-2650,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
71074,high,2025-01-22,2025-01-22,,************;************,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21521),CVE-2025-21521,2,10,9.0,48,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证
71074,high,2016-08-07,2017-01-17,,************;*************,7.5,4.0,False,False,OpenSSH auth_password函数拒绝服务漏洞(CVE-2016-6515),CVE-2016-6515,2,10,9.0,13,6.5,0,10,1,5.2,5.2,2.6,3.73,73.10833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-07)，可能已修复
71074,high,2023-10-03,2024-01-20,,************;************,7.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-5363),CVE-2023-5363,2,10,9.0,48,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-03)，可能已修复
71074,high,2018-11-08,2018-11-13,,10.227.129.210;************,7.5,4.0,False,False,nginx 安全漏洞(CVE-2018-16843),CVE-2018-16843,2,10,9.0,7,3.5,0,10,1,5.2,5.2,2.6,3.5500000000000003,72.95833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-11-08)，可能已修复
71074,high,2018-08-01,2018-08-23,,*************,7.5,4.0,False,False,Apache Tomcat安全限制绕过漏洞(CVE-2018-8034),CVE-2018-8034,2,10,9.0,25,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2018-08-01)，可能已修复
71074,high,2023-10-23,2023-12-05,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 输入验证错误漏洞(CVE-2023-46589),CVE-2023-46589,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-10-23)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,7.5,4.0,False,False,Oracle WebLogic Server WLS - Web Services 组件安全漏洞(CVE-2019-2649),CVE-2019-2649,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,7.5,4.0,False,False,Oracle WebLogic Server WLS - Web Services 组件安全漏洞(CVE-2019-2648),CVE-2019-2648,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
71074,high,2023-05-08,2023-10-25,,10.228.16.18,7.5,4.0,False,False,Python输入验证错误漏洞（CVE-2023-24329）,CVE-2023-24329,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-05-08)，可能已修复
71074,high,2023-09-29,2023-10-24,,10.228.26.65,7.5,4.0,False,False,HTTP/2拒绝服务漏洞（CVE-2023-44487）,CVE-2023-44487,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-09-29)，可能已修复
80001,high,2019-04-16,2019-04-24,,************;************,7.5,4.0,False,False,Oracle WebLogic Server WLS - Web Services 组件安全漏洞(CVE-2019-2647),CVE-2019-2647,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
71074,high,2022-01-31,2025-07-04,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2021-46669),CVE-2021-46669,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-01-31)，可能已修复
71074,high,2023-10-17,2023-10-11,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,7.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-0464),CVE-2023-0464,2,10,9.0,6,3.0,0,10,1,5.2,5.2,2.6,3.52,72.93333333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,high,2024-11-29,2025-07-11,,10.228.18.217,7.5,4.0,False,False,ProFTPd 安全漏洞(CVE-2024-48651),CVE-2024-48651,2,10,9.0,11,5.5,0,10,2,5.2,5.2,5.2,10.66,78.88333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证
71074,high,2022-10-06,2025-05-20,,************,7.5,4.0,False,False,RabbitMQ 安全特征问题漏洞(CVE-2022-31008),CVE-2022-31008,2,10,9.0,2,1.0,0,10,2,5.2,5.2,5.2,10.120000000000001,78.43333333333334,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-06)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27457),CVE-2022-27457,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27455),CVE-2022-27455,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27451),CVE-2022-27451,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27446),CVE-2022-27446,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27444),CVE-2022-27444,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27382),CVE-2022-27382,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27385),CVE-2022-27385,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27379),CVE-2022-27379,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27456),CVE-2022-27456,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27452),CVE-2022-27452,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27449),CVE-2022-27449,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27448),CVE-2022-27448,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27447),CVE-2022-27447,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27376),CVE-2022-27376,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-14,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27445),CVE-2022-27445,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-14)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-27387),CVE-2022-27387,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27386),CVE-2022-27386,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27384),CVE-2022-27384,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27383),CVE-2022-27383,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27381),CVE-2022-27381,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27380),CVE-2022-27380,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB SQL注入漏洞(CVE-2022-27378),CVE-2022-27378,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-04-12,2022-05-11,,10.227.132.243,7.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2022-27377),CVE-2022-27377,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-12)，可能已修复
71074,high,2022-03-09,2022-05-11,,10.228.16.18;************;10.227.132.243;10.228.29.239,7.5,4.0,False,False,Oracle MySQL/MariaDB Packaging (OpenSSL)全漏洞(CVE-2022-0778),CVE-2022-0778,2,10,9.0,3,1.5,0,10,1,5.2,5.2,2.6,3.43,72.85833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-03-09)，可能已修复
71074,high,2022-05-11,2022-05-25,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 代码问题漏洞(CVE-2022-29885),CVE-2022-29885,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-05-11)，可能已修复
71074,high,2022-02-09,2022-05-24,,10.228.16.18,7.5,4.0,False,False,Python 注入漏洞(CVE-2022-0391),CVE-2022-0391,2,10,9.0,14,7.0,0,10,2,5.2,5.2,5.2,10.840000000000002,79.03333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-02-09)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32081),CVE-2022-32081,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32084),CVE-2022-32084,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32088),CVE-2022-32088,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32091),CVE-2022-32091,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32087),CVE-2022-32087,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32086),CVE-2022-32086,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32085),CVE-2022-32085,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
71074,high,2022-07-01,2022-07-07,,10.227.132.243,7.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-32083),CVE-2022-32083,2,10,9.0,51,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-01)，可能已修复
80001,high,2021-10-19,2021-10-21,,************;************,7.5,4.0,False,False,Oracle WebLogic Server 输入验证错误漏洞(CVE-2021-35620),CVE-2021-35620,2,10,9.0,121,10.0,0,10,1,5.2,5.2,2.6,3.94,73.28333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-10-19)，可能已修复
71074,high,2022-03-25,2022-07-21,,10.228.16.18;************;10.227.132.243;10.228.29.239,7.5,4.0,False,False,Oracle MySQL zlib 组件输入验证错误漏洞(CVE-2018-25032),CVE-2018-25032,2,10,9.0,3,1.5,0,10,1,5.2,5.2,2.6,3.43,72.85833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-03-25)，可能已修复
71074,high,2022-10-31,2022-11-08,,10.228.26.65,7.5,4.0,False,False,Apache Tomcat 环境问题漏洞(CVE-2022-42252),CVE-2022-42252,2,10,9.0,15,7.5,0,10,2,5.2,5.2,5.2,10.9,79.08333333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-31)，可能已修复
71074,high,2021-03-19,2021-03-19,,10.228.16.18,7.4,4.0,False,False,Python开放重定向漏洞（CVE-2021-28861）,CVE-2021-28861,2,10,8.96,14,7.0,0,10,2,5.2,5.2,5.2,10.830400000000001,79.02533333333334,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-03-19)，可能已修复
71074,high,2021-03-25,2024-05-16,,10.228.16.18;10.228.29.239,7.4,4.0,False,False,Oracle MySQL Server 远程安全漏洞(CVE-2021-3450),CVE-2021-3450,2,10,8.96,9,4.5,0,10,1,5.2,5.2,2.6,3.6052000000000004,73.00433333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-03-25)，可能已修复
80001,high,2017-04-24,2017-04-25,,************;************,7.4,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-3506),CVE-2017-3506,2,10,8.96,121,10.0,0,10,1,5.2,5.2,2.6,3.9352,73.27933333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-04-24)，可能已修复
71074,high,2021-07-21,2023-03-14,,10.228.16.18;************;10.228.29.239,7.4,4.0,False,False,Oracle MySQL Server 缓冲区溢出漏洞(CVE-2021-3712),CVE-2021-3712,2,10,8.96,15,7.5,0,10,1,5.2,5.2,2.6,3.7852,73.15433333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-21)，可能已修复
71074,high,2023-02-10,2023-07-23,,************;************,7.4,4.0,False,False,Oracle MySQL Cluster 安全漏洞(CVE-2023-0361),CVE-2023-0361,2,10,8.96,48,10.0,0,10,1,5.2,5.2,2.6,3.9352,73.27933333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-02-10)，可能已修复
71074,high,2021-06-24,2021-06-24,,10.227.132.243;10.229.98.20;10.227.129.210;************;10.228.16.18;10.228.18.217,7.4,4.0,False,False,Nginx 信任管理问题漏洞(CVE-2021-3618),CVE-2021-3618,2,10,8.96,1,0.5,0,10,1,5.2,5.2,2.6,3.3652,72.80433333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-06-24)，可能已修复
71074,high,2023-02-07,2024-05-17,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.4,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-0286),CVE-2023-0286,2,10,8.96,8,4.0,0,10,1,5.2,5.2,2.6,3.5752,72.97933333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-02-07)，可能已修复
80001,high,2019-03-26,2021-04-21,,************;************,7.3,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2019-10086),CVE-2019-10086,2,10,8.92,121,10.0,0,10,1,5.2,5.2,2.6,3.9304,73.27533333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-03-26)，可能已修复
71074,high,2016-12-19,2016-12-26,,************;*************,7.3,4.0,False,False,OpenSSH 远程代码执行漏洞(CVE-2016-10009),CVE-2016-10009,2,10,8.92,13,6.5,0,10,1,5.2,5.2,2.6,3.7204,73.10033333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-12-19)，可能已修复
80001,high,2020-10-20,2020-10-21,,************;************,7.2,4.0,False,False,Oracle WebLogic Server Console 安全漏洞(CVE-2020-14883),CVE-2020-14883,2,10,8.88,121,10.0,0,10,1,5.2,5.2,2.6,3.9256,73.27133333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-20)，可能已修复
71074,high,2022-10-18,2022-10-20,,************,7.2,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-21600),CVE-2022-21600,2,10,8.88,47,10.0,0,10,2,5.2,5.2,5.2,11.1712,79.30933333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-18)，可能已修复
80001,high,2021-01-19,2021-01-21,,************;************,7.2,4.0,False,False,Oracle WebLogic Server 访问控制错误漏洞(CVE-2021-2109),CVE-2021-2109,2,10,8.88,121,10.0,0,10,1,5.2,5.2,2.6,3.9256,73.27133333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-19)，可能已修复
80001,high,2020-04-15,2020-04-15,,************;************,7.2,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2020-2798),CVE-2020-2798,2,10,8.88,121,10.0,0,10,1,5.2,5.2,2.6,3.9256,73.27133333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
80001,high,2019-10-15,2019-10-21,,************;************,7.2,4.0,False,False,Oracle WebLogic Server WLS - Web Services 组件安全漏洞(CVE-2019-2890),CVE-2019-2890,2,10,8.88,121,10.0,0,10,1,5.2,5.2,2.6,3.9256,73.27133333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2019-10-15)，可能已修复
80001,high,2020-01-15,2020-01-15,,************;************,7.2,4.0,False,False,Oracle WebLogic Server WSL Core 组件访问控制错误漏洞(CVE-2020-2549),CVE-2020-2549,2,10,8.88,121,10.0,0,10,1,5.2,5.2,2.6,3.9256,73.27133333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2020-01-15)，可能已修复
71074,high,2024-12-28,2022-04-22,,*************,7.2,4.0,False,False,Eclipse Jetty 安全漏洞(CVE-2024-13009),CVE-2024-13009,2,10,8.88,3,1.5,0,10,2,5.2,5.2,5.2,10.151200000000001,78.45933333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证
71074,high,2022-01-19,2022-01-20,,************,7.1,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21351),CVE-2022-21351,2,10,8.84,47,10.0,0,10,2,5.2,5.2,5.2,11.1616,79.30133333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,high,2022-01-19,2022-01-20,,************,7.1,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21278),CVE-2022-21278,2,10,8.84,47,10.0,0,10,2,5.2,5.2,5.2,11.1616,79.30133333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,high,2021-05-05,2023-09-07,,************,7.1,4.0,False,False,MongoDB Server 安全漏洞(CVE-2021-32036),CVE-2021-32036,2,10,8.84,47,10.0,0,10,2,5.2,5.2,5.2,11.1616,79.30133333333333,高危漏洞(级别:high); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-05-05)，可能已修复
71074,high,2022-12-08,2023-03-29,,10.227.132.243;************;10.229.98.20;10.227.129.210;************;10.228.16.18;10.228.18.217;*************,7.1,4.0,False,False,nginx 越界写入漏洞（CVE-2022-41742）,CVE-2022-41742,2,10,8.84,2,1.0,0,10,1,5.2,5.2,2.6,3.3808000000000002,72.81733333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2022-12-08)，可能已修复
71074,high,2016-08-12,2016-12-06,,*************,7.1,4.0,False,False,Apache Tomcat 安全限制绕过漏洞(CVE-2016-6816),CVE-2016-6816,2,10,8.84,25,10.0,0,10,1,5.2,5.2,2.6,3.9208,73.26733333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-12)，可能已修复
71074,high,2023-04-18,2023-04-22,,10.228.16.18;************;10.228.29.239;10.229.27.93,7.1,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21980),CVE-2023-21980,2,10,8.84,8,4.0,0,10,1,5.2,5.2,2.6,3.5608,72.96733333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-04-18)，可能已修复
71074,high,2021-09-26,2021-10-08,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,7.0,4.0,False,False,OpenSSH 安全漏洞(CVE-2021-41617),CVE-2021-41617,2,10,8.8,6,3.0,0,10,1,5.2,5.2,2.6,3.4960000000000004,72.91333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2021-09-26)，可能已修复
60092,high,2017-12-01,2019-09-29,,10.227.129.151,7.0,3.0,False,False,Microsoft Windows CredSSP 远程执行代码漏洞(CVE-2018-0886)【原理扫描】,CVE-2018-0886,2,10,8.8,1,0.5,0,10,1,5.2,5.2,2.6,3.3460000000000005,72.78833333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2017-12-01)，可能已修复
71074,high,2023-12-24,2024-04-09,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,7.0,4.0,False,False,OpenSSH 安全漏洞(CVE-2023-51767),CVE-2023-51767,2,10,8.8,6,3.0,0,10,1,5.2,5.2,2.6,3.4960000000000004,72.91333333333333,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2023-12-24)，可能已修复
71074,high,2016-12-19,2016-12-26,,************;*************,7.0,4.0,False,False,OpenSSH 远程权限提升漏洞(CVE-2016-10010),CVE-2016-10010,2,10,8.8,13,6.5,0,10,1,5.2,5.2,2.6,3.706,73.08833333333334,高危漏洞(级别:high),漏洞未经确认验证; 漏洞发现时间较久远(2016-12-19)，可能已修复
71074,middle,2015-08-23,2016-02-10,,************;*************,6.9,4.0,False,False,OpenSSH sshd mm_answer_pam_free_ctx释放后重利用漏洞(CVE-2015-6564),CVE-2015-6564,1,6,6.359999999999999,13,6.5,0,10,1,4.2,4.2,2.1,3.1132000000000004,52.075466666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2015-08-23)，可能已修复
71074,middle,2019-01-16,2019-04-08,,************;************;************;************;10.228.16.18;************;************;************;************;************;************;************;************,6.8,4.0,False,False,OpenSSH 欺骗安全漏洞(CVE-2019-6110),CVE-2019-6110,1,6,6.32,4,2.0,0,10,1,4.2,4.2,2.1,2.8384000000000005,51.892266666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-16)，可能已修复
71074,middle,2025-02-18,2025-02-24,,************;************;************;************;10.228.16.18;************;************;************;************;************;************;************;************,6.8,4.0,False,False,OpenSSH 安全漏洞(CVE-2025-26465 ),CVE-2025-26465,1,6,6.32,4,2.0,0,10,1,4.2,4.2,2.1,2.8384000000000005,51.892266666666664,中危漏洞(级别:middle),漏洞未经确认验证
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-4267),CVE-2014-4267,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-2479),CVE-2014-2479,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-4254),CVE-2014-4254,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-4255),CVE-2014-4255,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-2480),CVE-2014-2480,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,6.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-2481),CVE-2014-2481,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
80001,middle,2014-10-15,2017-06-27,,************;************,6.8,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件拒绝服务漏洞(CVE-2014-6499),CVE-2014-6499,1,6,6.32,121,10.0,0,10,1,4.2,4.2,2.1,3.3184,52.212266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-10-15)，可能已修复
71074,middle,2007-08-27,2024-09-09,,10.228.16.18,6.8,4.0,False,False,Python tarfile 模块路径遍历漏洞(CVE-2007-4559),CVE-2007-4559,1,6,6.32,14,7.0,0,10,2,4.2,4.2,4.2,8.9968,55.99786666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2007-08-27)，可能已修复
71074,middle,2019-01-16,2019-04-08,,************;************;************;************;10.228.16.18;************;************;************;************;************;************;************;************,6.8,4.0,False,False,OpenSSH 欺骗安全漏洞(CVE-2019-6109),CVE-2019-6109,1,6,6.32,4,2.0,0,10,1,4.2,4.2,2.1,2.8384000000000005,51.892266666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-16)，可能已修复
80001,middle,2019-01-16,2019-01-21,,************;************,6.7,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2019-2452),CVE-2019-2452,1,6,6.279999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.3136,52.209066666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-16)，可能已修复
71074,middle,2025-06-19,2025-07-01,,************;************,6.7,4.0,False,False,RabbitMQ 跨站脚本漏洞(CVE-2025-50200),CVE-2025-50200,1,6,6.279999999999999,2,1.0,0,10,1,4.2,4.2,2.1,2.7736,51.849066666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2025-04-15,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21577),CVE-2025-21577,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2025-03-25,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-30687),CVE-2025-30687,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2022-07-06,2024-05-17,,10.228.16.18;************;10.228.29.239,6.5,4.0,False,False,Oracle MySQL cURL 组件输入验证错误漏洞(CVE-2022-35260),CVE-2022-35260,1,6,6.199999999999999,15,7.5,0,10,1,4.2,4.2,2.1,3.1540000000000004,52.102666666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-07-06)，可能已修复
80001,middle,2019-01-16,2019-01-21,,************;************,6.5,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2019-2418),CVE-2019-2418,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-16)，可能已修复
71074,middle,2023-12-18,2023-12-26,,************;************;************;************;************;10.228.16.18;************;************;*************;************;************;************;************;************;************,6.5,4.0,False,False,OpenSSH 安全漏洞(CVE-2023-51385),CVE-2023-51385,1,6,6.199999999999999,3,1.5,0,10,1,4.2,4.2,2.1,2.794,51.86266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-12-18)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21500),CVE-2025-21500,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2022-07-19,2022-07-21,,************,6.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21569),CVE-2022-21569,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71247,middle,2001-01-01,2025-03-24,,10.240.2.221;10.228.29.100;10.227.129.151,6.5,3.0,False,False,目标使用过期的TLS1.1 版协议【原理扫描】,,1,6,6.199999999999999,2,1.0,0,5,1,4.2,4.2,2.1,2.464,51.64266666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
71074,middle,2021-08-23,2022-05-24,,10.228.16.18,6.5,4.0,False,False,Python 资源管理错误漏洞(CVE-2021-3733),CVE-2021-3733,1,6,6.199999999999999,14,7.0,0,10,2,4.2,4.2,4.2,8.968,55.97866666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-08-23)，可能已修复
71247,middle,2001-01-01,2025-03-24,,10.228.29.100;10.227.129.151;10.240.2.221;10.228.18.217;*************,6.5,3.0,False,False,目标使用过期的TLS1.0 版协议【原理扫描】,,1,6,6.199999999999999,1,0.5,0,5,1,4.2,4.2,2.1,2.434,51.62266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2001-01-01)，可能已修复
80001,middle,2020-09-19,2025-05-12,,************;************,6.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2020-5421),CVE-2020-5421,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-09-19)，可能已修复
71074,middle,2023-11-14,2023-11-14,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-6129),CVE-2023-6129,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-11-14)，可能已修复
71074,middle,2021-10-20,2025-03-04,,************,6.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-35597),CVE-2021-35597,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-10-20)，可能已修复
71074,middle,2023-10-17,2023-10-19,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-22079),CVE-2023-22079,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,middle,2023-10-17,2023-10-19,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-22059),CVE-2023-22059,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,middle,2023-04-18,2023-04-22,,************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21946),CVE-2023-21946,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-04-18)，可能已修复
71074,middle,2022-10-18,2022-10-20,,************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-21635),CVE-2022-21635,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-18)，可能已修复
80001,middle,2017-10-19,2017-10-23,,************;************,6.5,4.0,False,False,Oracle Weblogic Server Web Container 组件安全漏洞(CVE-2017-10152),CVE-2017-10152,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2017-10-19)，可能已修复
71074,middle,2021-07-21,2023-03-14,,10.228.16.18;************;10.228.29.239,6.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-22922),CVE-2021-22922,1,6,6.199999999999999,15,7.5,0,10,1,4.2,4.2,2.1,3.1540000000000004,52.102666666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-21)，可能已修复
71074,middle,2022-10-18,2022-10-20,,************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-39408),CVE-2022-39408,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-18)，可能已修复
71074,middle,2022-10-18,2022-10-20,,************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-39410),CVE-2022-39410,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-10-18)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21868),CVE-2023-21868,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
80001,middle,2018-10-16,2019-03-13,,************;************,6.5,4.0,False,False,Oracle WebLogic Server WLS 组件安全漏洞(CVE-2018-3249),CVE-2018-3249,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,6.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21358),CVE-2022-21358,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20985),CVE-2024-20985,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20963),CVE-2024-20963,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-10-14,2024-10-22,,*************,6.5,4.0,False,False,Eclipse Jetty 安全漏洞(CVE-2024-8184),CVE-2024-8184,1,6,6.199999999999999,3,1.5,0,10,2,4.2,4.2,4.2,8.308,55.538666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证
80001,middle,2018-10-16,2019-03-13,,************;************,6.5,4.0,False,False,Oracle WebLogic Server WLS 组件安全漏洞(CVE-2018-3248),CVE-2018-3248,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
80001,middle,2020-07-14,2020-07-16,,************;************,6.5,4.0,False,False,Oracle Fusion Middleware WebLogic Server WebCenter Sites 安全漏洞(CVE-2020-14652),CVE-2020-14652,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
71074,middle,2025-03-25,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2025-30688),CVE-2025-30688,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20960),CVE-2024-20960,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20977),CVE-2024-20977,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20973),CVE-2024-20973,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-10-15,2024-10-17,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21196),CVE-2024-21196,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20962),CVE-2024-20962,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
80001,middle,2021-01-19,2021-01-21,,************;************,6.5,4.0,False,False,Oracle WebLogic Server 访问控制错误漏洞(CVE-2021-1995),CVE-2021-1995,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-19)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20961),CVE-2024-20961,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2022-04-19,2022-04-21,,10.228.16.18;************;10.228.29.239,6.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21454),CVE-2022-21454,1,6,6.199999999999999,15,7.5,0,10,1,4.2,4.2,2.1,3.1540000000000004,52.102666666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2018-02-23,2018-03-16,,*************,6.5,4.0,False,False,Apache Tomcat安全绕过漏洞(CVE-2018-1305),CVE-2018-1305,1,6,6.199999999999999,25,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-02-23)，可能已修复
80001,middle,2019-01-03,2021-04-21,,************;************,6.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2019-3740),CVE-2019-3740,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-03)，可能已修复
80001,middle,2021-04-21,2021-04-21,,************;************,6.5,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2294),CVE-2021-2294,1,6,6.199999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-21)，可能已修复
71074,middle,2024-10-15,2024-10-17,,************;************,6.5,4.0,False,False,Oracle MySQL curl安全漏洞(CVE-2024-7264),CVE-2024-7264,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-10-15,2024-10-17,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21230),CVE-2024-21230,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2022-12-12,2025-07-04,,10.227.132.243,6.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2022-47015),CVE-2022-47015,1,6,6.199999999999999,51,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-12-12)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21522),CVE-2025-21522,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2022-07-19,2022-07-21,,************,6.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21556),CVE-2022-21556,1,6,6.199999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.328000000000001,56.218666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71074,middle,2025-04-15,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2025-21574),CVE-2025-21574,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2025-04-15,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21575),CVE-2025-21575,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2025-01-22,2025-01-22,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21501),CVE-2025-21501,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2021-04-20,2021-04-22,,10.228.16.18;10.228.29.239,6.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-2202),CVE-2021-2202,1,6,6.199999999999999,9,4.5,0,10,1,4.2,4.2,2.1,2.9739999999999998,51.98266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-20)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21518),CVE-2025-21518,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-05-29,2025-01-02,,10.228.26.65,6.5,4.0,False,False,F5 Nginx 安全漏洞(CVE-2024-32760),CVE-2024-32760,1,6,6.199999999999999,15,7.5,0,10,2,4.2,4.2,4.2,9.028,56.01866666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-05-29)，可能已修复
71074,middle,2025-03-25,2025-04-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-30682),CVE-2025-30682,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-07-16,2024-07-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21171),CVE-2024-21171,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-07-16)，可能已修复
71074,middle,2023-10-17,2023-10-11,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,6.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-2650),CVE-2023-2650,1,6,6.199999999999999,6,3.0,0,10,1,4.2,4.2,2.1,2.884,51.922666666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,middle,2024-07-16,2024-07-20,,************;************,6.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21177),CVE-2024-21177,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-07-16)，可能已修复
71074,middle,2023-06-21,2024-01-20,,************;************,6.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2023-36054),CVE-2023-36054,1,6,6.199999999999999,48,10.0,0,10,1,4.2,4.2,2.1,3.304,52.202666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-06-21)，可能已修复
71074,middle,2019-08-15,2020-01-08,,10.227.129.210;************,6.5,4.0,False,False,HTTP/2 安全漏洞(CVE-2019-9516),CVE-2019-9516,1,6,6.199999999999999,7,3.5,0,10,1,4.2,4.2,2.1,2.914,51.94266666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-08-15)，可能已修复
71074,middle,2016-03-10,2016-08-15,,************;*************,6.4,4.0,False,False,OpenSSH <=7.2p1 xauth命令注入漏洞(CVE-2016-3115),CVE-2016-3115,1,6,6.16,13,6.5,0,10,1,4.2,4.2,2.1,3.0892000000000004,52.059466666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-03-10)，可能已修复
71074,middle,2013-12-03,2015-03-13,,*************,6.4,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2014-0227),CVE-2014-0227,1,6,6.16,25,10.0,0,10,1,4.2,4.2,2.1,3.2992000000000004,52.199466666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2013-12-03)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,6.3,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21482),CVE-2022-21482,1,6,6.119999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.3088,56.205866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,6.3,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21489),CVE-2022-21489,1,6,6.119999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.3088,56.205866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,6.3,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21483),CVE-2022-21483,1,6,6.119999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.3088,56.205866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,6.3,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21490),CVE-2022-21490,1,6,6.119999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.3088,56.205866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-07-19,2022-07-21,,************,6.3,4.0,False,False,Oracle MySQL Cluster 输入验证错误漏洞(CVE-2022-21550),CVE-2022-21550,1,6,6.119999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.3088,56.205866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71074,middle,2016-02-24,2016-03-04,,*************,6.3,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2016-0763),CVE-2016-0763,1,6,6.119999999999999,25,10.0,0,10,1,4.2,4.2,2.1,3.2944,52.196266666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-02-24)，可能已修复
71074,middle,2020-05-19,2025-04-07,,************,6.1,4.0,False,False,jQuery跨站脚本漏洞（CVE-2020-7656）,CVE-2020-7656,1,6,6.039999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.2896,56.19306666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2020-05-19)，可能已修复
80001,middle,2020-07-14,2020-07-16,,************;************,6.1,4.0,False,False,Oracle Fusion Middleware WebLogic Server 安全漏洞(CVE-2020-14572),CVE-2020-14572,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
80001,middle,2016-04-21,2017-06-26,,************;************,6.1,4.0,False,False,Oracle Fusion Middleware WebLogic Server远程安全漏洞(CVE-2016-0700),CVE-2016-0700,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-04-21)，可能已修复
80001,middle,2020-04-15,2020-04-15,,************;************,6.1,4.0,False,False,Oracle WebLogic Server Console 组件安全漏洞(CVE-2020-2811),CVE-2020-2811,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
71074,middle,2021-12-06,2024-10-15,,************,6.1,4.0,False,False,Nacos 跨站脚本漏洞(CVE-2021-44667),CVE-2021-44667,1,6,6.039999999999999,4,2.0,0,10,2,4.2,4.2,4.2,8.3296,55.553066666666666,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-12-06)，可能已修复
71074,middle,2022-06-23,2022-07-12,,10.228.26.65,6.1,4.0,False,False,Apache Tomcat 跨站脚本漏洞(CVE-2022-34305),CVE-2022-34305,1,6,6.039999999999999,15,7.5,0,10,2,4.2,4.2,4.2,8.989600000000001,55.993066666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-06-23)，可能已修复
71074,middle,2019-04-19,2024-05-17,,*************;************,6.1,4.0,False,False,jQuery跨站脚本漏洞（CVE-2019-11358）,CVE-2019-11358,1,6,6.039999999999999,2,1.0,0,10,1,4.2,4.2,2.1,2.7448,51.82986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-19)，可能已修复
80001,middle,2018-07-19,2018-07-20,,************;************,6.1,4.0,False,False,Oracle WebLogic Server Console 组件安全漏洞(CVE-2018-2987),CVE-2018-2987,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-07-19)，可能已修复
80001,middle,2016-04-21,2017-06-26,,************;************,6.1,4.0,False,False,Oracle Fusion Middleware WebLogic Server远程安全漏洞(CVE-2016-3416),CVE-2016-3416,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-04-21)，可能已修复
71074,middle,2020-04-29,2025-04-07,,************;*************;************,6.1,4.0,False,False,jQuery跨站脚本漏洞（CVE-2020-11022）,CVE-2020-11022,1,6,6.039999999999999,1,0.5,0,10,1,4.2,4.2,2.1,2.7148,51.809866666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-29)，可能已修复
80001,middle,2016-08-27,2019-08-16,,************;************,6.1,4.0,False,False,Oracle WebLogic Server Console(jQuery)组件跨站脚本执行漏洞(CVE-2016-7103),CVE-2016-7103,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-27)，可能已修复
80001,middle,2017-08-08,2017-07-24,,************;************,6.1,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-10178),CVE-2017-10178,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2017-08-08)，可能已修复
71074,middle,2018-11-08,2018-11-13,,10.227.129.210;************,6.1,4.0,False,False,nginx 安全漏洞(CVE-2018-16845),CVE-2018-16845,1,6,6.039999999999999,7,3.5,0,10,1,4.2,4.2,2.1,2.8948,51.92986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-11-08)，可能已修复
71074,middle,2020-03-30,2024-05-17,,*************;************;************,6.1,4.0,False,False,jQuery跨站脚本漏洞（CVE-2020-11023）,CVE-2020-11023,1,6,6.039999999999999,1,0.5,0,10,1,4.2,4.2,2.1,2.7148,51.809866666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-03-30)，可能已修复
80001,middle,2020-10-21,2020-10-21,,************;************,6.1,4.0,False,False,Oracle WebLogic Server 跨站脚本漏洞(CVE-2020-11022),CVE-2020-11022,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-10-21)，可能已修复
80001,middle,2021-04-21,2021-04-21,,************;************,6.1,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2142),CVE-2021-2142,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-21)，可能已修复
71074,middle,2018-01-22,2020-11-26,,************,6.1,4.0,False,False,jQuery 跨站脚本漏洞(CVE-2012-6708),CVE-2012-6708,1,6,6.039999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.2896,56.19306666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2018-01-22)，可能已修复
71074,middle,2018-01-18,2025-06-03,,*************;************,6.1,4.0,False,False,jQuery 跨站脚本漏洞(CVE-2015-9251),CVE-2015-9251,1,6,6.039999999999999,2,1.0,0,10,1,4.2,4.2,2.1,2.7448,51.82986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-01-18)，可能已修复
71074,middle,2017-05-11,2021-06-08,,************,6.1,4.0,False,False,Pivotal RabbitMQ 跨站脚本漏洞(CVE-2017-4967),CVE-2017-4967,1,6,6.039999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.2896,56.19306666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2017-05-11)，可能已修复
71074,middle,2017-05-11,2021-06-08,,************,6.1,4.0,False,False,Pivotal RabbitMQ和Pivotal RabbitMQ for PCF 跨站脚本漏洞(CVE-2017-4965),CVE-2017-4965,1,6,6.039999999999999,47,10.0,0,10,2,4.2,4.2,4.2,9.2896,56.19306666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2017-05-11)，可能已修复
71074,middle,2023-08-25,2023-10-25,,10.228.26.65,6.1,4.0,False,False,Apache Tomcat 输入验证错误漏洞(CVE-2023-41080),CVE-2023-41080,1,6,6.039999999999999,15,7.5,0,10,2,4.2,4.2,4.2,8.989600000000001,55.993066666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-08-25)，可能已修复
71074,middle,2021-04-20,2021-04-22,,10.228.16.18;10.228.29.239,6.1,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-2307),CVE-2021-2307,1,6,6.039999999999999,9,4.5,0,10,1,4.2,4.2,2.1,2.9548,51.96986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-20)，可能已修复
80001,middle,2019-04-19,2019-10-21,,************;************,6.1,4.0,False,False,Oracle WebLogic Server Console(jQuery)组件跨站脚本执行漏洞(CVE-2019-11358),CVE-2019-11358,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-19)，可能已修复
80001,middle,2018-10-16,2019-03-13,,************;************,6.1,4.0,False,False,Oracle WebLogic Server WLS 组件安全漏洞(CVE-2018-3250),CVE-2018-3250,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-10-16)，可能已修复
80001,middle,2016-04-21,2017-06-26,,************;************,6.1,4.0,False,False,Oracle Fusion Middleware WebLogic Server远程安全漏洞(CVE-2016-0675),CVE-2016-0675,1,6,6.039999999999999,121,10.0,0,10,1,4.2,4.2,2.1,3.2848,52.18986666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-04-21)，可能已修复
60478,middle,2005-12-05,2025-02-07,,10.229.98.20;10.228.18.217,5.9,3.0,False,False,SSL证书使用了弱hash算法(CVE-2005-4900)【原理扫描】,CVE-2005-4900,1,6,5.96,1,0.5,0,10,1,4.2,4.2,2.1,2.7052,51.803466666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2005-12-05)，可能已修复
71074,middle,2020-07-01,2021-11-17,,************;************;************;************;************;10.228.16.18;************;************;*************;************;************;************;************;************;************,5.9,4.0,False,False,OpenSSH信息泄露漏洞(CVE-2020-14145),CVE-2020-14145,1,6,5.96,3,1.5,0,10,1,4.2,4.2,2.1,2.7652,51.843466666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-01)，可能已修复
71074,middle,2015-12-16,2016-11-23,,*************,5.9,4.0,False,False,Apache Tomcat 信息泄露漏洞(CVE-2016-0762),CVE-2016-0762,1,6,5.96,25,10.0,0,10,1,4.2,4.2,2.1,3.2752000000000003,52.18346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2015-12-16)，可能已修复
71074,middle,2018-03-06,2018-08-23,,*************,5.9,4.0,False,False,Apache Tomcat安全限制绕过漏洞(CVE-2018-1304),CVE-2018-1304,1,6,5.96,25,10.0,0,10,1,4.2,4.2,2.1,3.2752000000000003,52.18346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-03-06)，可能已修复
71074,middle,2022-12-06,2024-05-17,,10.228.16.18;************;10.228.29.239;10.229.27.93,5.9,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2022-4304),CVE-2022-4304,1,6,5.96,8,4.0,0,10,1,4.2,4.2,2.1,2.9152,51.943466666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-12-06)，可能已修复
71074,middle,2023-11-20,2024-01-02,,************;************;************;************;************;10.228.16.18;************;************;*************;************;************;************;************;************;************,5.9,4.0,False,False,OpenSSH 安全漏洞(CVE-2023-48795),CVE-2023-48795,1,6,5.96,3,1.5,0,10,1,4.2,4.2,2.1,2.7652,51.843466666666664,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-11-20)，可能已修复
71074,middle,2023-07-18,2023-07-23,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,5.9,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-22053),CVE-2023-22053,1,6,5.96,6,3.0,0,10,1,4.2,4.2,2.1,2.8552,51.90346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-07-18)，可能已修复
71074,middle,2021-07-20,2021-07-22,,10.228.16.18;10.228.29.239,5.9,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-2356),CVE-2021-2356,1,6,5.96,9,4.5,0,10,1,4.2,4.2,2.1,2.9452000000000003,51.96346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
71074,middle,2021-03-25,2021-05-06,,10.228.16.18;10.228.29.239,5.9,4.0,False,False,Oracle MySQL Server 远程安全漏洞(CVE-2021-3449),CVE-2021-3449,1,6,5.96,9,4.5,0,10,1,4.2,4.2,2.1,2.9452000000000003,51.96346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-03-25)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.9,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21457),CVE-2022-21457,1,6,5.96,47,10.0,0,10,2,4.2,4.2,4.2,9.2704,56.18026666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2021-02-16,2021-05-06,,10.228.16.18;10.228.29.239,5.9,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2021-23841),CVE-2021-23841,1,6,5.96,9,4.5,0,10,1,4.2,4.2,2.1,2.9452000000000003,51.96346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-02-16)，可能已修复
71074,middle,2021-01-06,2023-03-14,,10.228.16.18;************;10.228.29.239,5.9,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-22947),CVE-2021-22947,1,6,5.96,15,7.5,0,10,1,4.2,4.2,2.1,3.1252,52.083466666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-01-06)，可能已修复
71074,middle,2016-07-13,2019-01-21,,************;*************,5.9,4.0,False,False,OpenSSH 用户枚举漏洞(CVE-2016-6210),CVE-2016-6210,1,6,5.96,13,6.5,0,10,1,4.2,4.2,2.1,3.0652000000000004,52.04346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-13)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,5.9,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-21875),CVE-2023-21875,1,6,5.96,47,10.0,0,10,2,4.2,4.2,4.2,9.2704,56.18026666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
71074,middle,2021-07-20,2021-07-22,,10.228.16.18;10.228.29.239,5.9,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2021-2390),CVE-2021-2390,1,6,5.96,9,4.5,0,10,1,4.2,4.2,2.1,2.9452000000000003,51.96346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
71074,middle,2021-07-20,2021-07-22,,10.228.16.18;10.227.132.243;10.228.29.239,5.9,4.0,False,False,Oracle MySQL/MariaDB Server 输入验证错误漏洞(CVE-2021-2389),CVE-2021-2389,1,6,5.96,1,0.5,0,10,1,4.2,4.2,2.1,2.7052,51.803466666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-07-20)，可能已修复
71074,middle,2023-10-17,2023-10-11,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,5.9,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-1255),CVE-2023-1255,1,6,5.96,6,3.0,0,10,1,4.2,4.2,2.1,2.8552,51.90346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,middle,2019-01-21,2019-04-08,,************;************;************;************;10.228.16.18;************;************;************;************;************;************;************;************,5.9,4.0,False,False,OpenSSH 安全漏洞(CVE-2019-6111),CVE-2019-6111,1,6,5.96,4,2.0,0,10,1,4.2,4.2,2.1,2.7952,51.86346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-21)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,5.9,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21352),CVE-2022-21352,1,6,5.96,47,10.0,0,10,2,4.2,4.2,4.2,9.2704,56.18026666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
80001,middle,2021-04-21,2021-04-21,,************;************,5.9,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2211),CVE-2021-2211,1,6,5.96,121,10.0,0,10,1,4.2,4.2,2.1,3.2752000000000003,52.18346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-21)，可能已修复
71247,middle,2013-03-15,2016-03-18,"根据SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
",10.228.29.100;10.227.129.151,5.9,3.0,True,False,SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)【原理扫描】,CVE-2013-2566,1,6,5.96,1,0.5,0,10,1,4.2,4.2,2.1,2.7052,51.803466666666665,"中危漏洞(级别:middle); 漏洞详情: 根据SSL/TLS RC4 信息泄露漏洞(CVE-2013-2566)原理，通过发送精心构造的数据包到目标服务，根据目标的响应情况，验证漏洞是否存在
...",漏洞发现时间较久远(2013-03-15)，可能已修复
71074,middle,2024-07-16,2024-07-20,,************;************,5.9,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21166),CVE-2024-21166,1,6,5.96,48,10.0,0,10,1,4.2,4.2,2.1,3.2752000000000003,52.18346666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-07-16)，可能已修复
80001,middle,2011-03-10,2017-09-06,,************;************,5.8,4.0,False,False,Oracle Fusion Middleware ‘Oracle WebLogic Server’组件安全漏洞(CVE-2011-1411),CVE-2011-1411,1,6,5.92,121,10.0,0,10,1,4.2,4.2,2.1,3.2704,52.18026666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2011-03-10)，可能已修复
80001,middle,2017-08-08,2017-07-24,,************;************,5.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2017-10148),CVE-2017-10148,1,6,5.92,121,10.0,0,10,1,4.2,4.2,2.1,3.2704,52.18026666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2017-08-08)，可能已修复
80001,middle,2014-07-17,2017-06-26,,************;************,5.8,4.0,False,False,Oracle WebLogic Server 远程安全漏洞(CVE-2014-4256),CVE-2014-4256,1,6,5.92,121,10.0,0,10,1,4.2,4.2,2.1,3.2704,52.18026666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2014-07-17)，可能已修复
71074,middle,2021-03-10,2022-05-24,,10.228.16.18,5.7,4.0,False,False,Python 信息泄露漏洞(CVE-2021-3426),CVE-2021-3426,1,6,5.88,14,7.0,0,10,2,4.2,4.2,4.2,8.8912,55.92746666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-03-10)，可能已修复
71074,middle,2022-07-19,2022-07-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21528),CVE-2022-21528,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71074,middle,2017-04-04,2025-07-11,,10.228.18.217,5.5,4.0,False,False,ProFTPD 安全漏洞(CVE-2017-7418),CVE-2017-7418,1,6,5.8,11,5.5,0,10,2,4.2,4.2,4.2,8.692,55.794666666666664,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2017-04-04)，可能已修复
71074,middle,2022-08-27,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-38791),CVE-2022-38791,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-08-27)，可能已修复
71074,middle,2022-05-25,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-31624),CVE-2022-31624,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-25)，可能已修复
71074,middle,2022-05-25,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-31623),CVE-2022-31623,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-25)，可能已修复
71074,middle,2025-03-25,2025-04-20,,************;************,5.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2025-30693),CVE-2025-30693,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-07-16,2024-07-20,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21163),CVE-2024-21163,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-07-16)，可能已修复
71074,middle,2025-03-25,2025-04-20,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-30695),CVE-2025-30695,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2024-01-19,2024-05-17,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-0727),CVE-2024-0727,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-19)，可能已修复
71074,middle,2016-12-19,2016-12-26,,************;*************,5.5,4.0,False,False,OpenSSH 本地信息泄露漏洞(CVE-2016-10011),CVE-2016-10011,1,6,5.8,13,6.5,0,10,1,4.2,4.2,2.1,3.046,52.03066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-12-19)，可能已修复
71074,middle,2022-05-25,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-31622),CVE-2022-31622,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-25)，可能已修复
71074,middle,2021-10-19,2021-10-21,,10.228.16.18;************;10.227.132.243;10.228.29.239,5.5,4.0,False,False,Oracle MySQL/MariaDB Server 输入验证错误漏洞(CVE-2021-35604),CVE-2021-35604,1,6,5.8,3,1.5,0,10,1,4.2,4.2,2.1,2.746,51.830666666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-10-19)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,5.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21301),CVE-2022-21301,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,5.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21378),CVE-2022-21378,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,middle,2024-04-16,2024-04-21,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21015),CVE-2024-21015,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-04-16)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20967),CVE-2024-20967,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-01-16,2024-01-20,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20969),CVE-2024-20969,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-16)，可能已修复
71074,middle,2022-01-19,2022-01-20,,10.228.16.18;************;10.228.29.239,5.5,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21367),CVE-2022-21367,1,6,5.8,15,7.5,0,10,1,4.2,4.2,2.1,3.1060000000000003,52.07066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 资源管理错误漏洞(CVE-2021-46668),CVE-2021-46668,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 输入验证错误漏洞(CVE-2021-46667),CVE-2021-46667,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46666),CVE-2021-46666,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46665),CVE-2021-46665,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46664),CVE-2021-46664,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46663),CVE-2021-46663,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2021-08-17,2022-02-23,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46662),CVE-2021-46662,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-08-17)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21425),CVE-2022-21425,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21440),CVE-2022-21440,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21459),CVE-2022-21459,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21478),CVE-2022-21478,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2022-04-19,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21479),CVE-2022-21479,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-04-19)，可能已修复
71074,middle,2021-01-05,2022-04-21,,************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2021-22570),CVE-2021-22570,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-01-05)，可能已修复
71074,middle,2022-07-19,2022-07-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21527),CVE-2022-21527,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21497),CVE-2025-21497,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2022-07-19,2022-07-21,,************,5.5,4.0,False,False,Oracle MySQL 输入验证错误漏洞(CVE-2022-21509),CVE-2022-21509,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-07-19)，可能已修复
71074,middle,2023-04-18,2023-04-22,,************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21929),CVE-2023-21929,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-04-18)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21872),CVE-2023-21872,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,5.5,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-21880),CVE-2023-21880,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21877),CVE-2023-21877,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
71074,middle,2023-01-18,2023-01-19,,************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-21869),CVE-2023-21869,1,6,5.8,47,10.0,0,10,2,4.2,4.2,4.2,9.232000000000001,56.15466666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-01-18)，可能已修复
71074,middle,2022-01-31,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 代码问题漏洞(CVE-2021-46661),CVE-2021-46661,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-01-31)，可能已修复
80001,middle,2019-04-16,2019-04-24,,************;************,5.5,4.0,False,False,Oracle WebLogic Server WLS Core 组件安全漏洞(CVE-2019-2618),CVE-2019-2618,1,6,5.8,121,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-04-16)，可能已修复
71074,middle,2022-05-25,2025-07-04,,10.227.132.243,5.5,4.0,False,False,MariaDB 安全漏洞(CVE-2022-31621),CVE-2022-31621,1,6,5.8,51,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2022-05-25)，可能已修复
80001,middle,2019-07-16,2019-08-16,,************;************,5.5,4.0,False,False,Oracle WebLogic Server WLS Core 组件访问控制错误漏洞(CVE-2019-2824),CVE-2019-2824,1,6,5.8,121,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-07-16)，可能已修复
80001,middle,2019-07-16,2019-08-16,,************;************,5.5,4.0,False,False,Oracle WebLogic Server WSL Core 组件访问控制错误漏洞(CVE-2019-2827),CVE-2019-2827,1,6,5.8,121,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-07-16)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21559),CVE-2025-21559,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2025-01-22,2025-01-22,,************;************,5.5,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21555),CVE-2025-21555,1,6,5.8,48,10.0,0,10,1,4.2,4.2,2.1,3.256,52.17066666666667,中危漏洞(级别:middle),漏洞未经确认验证
80001,middle,2016-04-21,2017-06-26,,************;************,5.4,4.0,False,False,Oracle Fusion Middleware WebLogic Server远程安全漏洞(CVE-2016-0696),CVE-2016-0696,1,6,5.76,121,10.0,0,10,1,4.2,4.2,2.1,3.2512000000000003,52.16746666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-04-21)，可能已修复
80001,middle,2020-07-14,2020-07-16,,************;************,5.4,4.0,False,False,Oracle Fusion Middleware WebLogic Server 安全漏洞(CVE-2020-2966),CVE-2020-2966,1,6,5.76,121,10.0,0,10,1,4.2,4.2,2.1,3.2512000000000003,52.16746666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-07-14)，可能已修复
80001,middle,2019-01-16,2019-01-21,,************;************,5.4,4.0,False,False,Oracle WebLogic Server WLS - Web Services 组件安全漏洞(CVE-2019-2395),CVE-2019-2395,1,6,5.76,121,10.0,0,10,1,4.2,4.2,2.1,3.2512000000000003,52.16746666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-16)，可能已修复
71074,middle,2021-06-28,2025-05-20,,************,5.4,4.0,False,False,RabbitMQ跨站脚本漏洞(CVE-2021-32718),CVE-2021-32718,1,6,5.76,2,1.0,0,10,2,4.2,4.2,4.2,8.1424,55.428266666666666,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2021-06-28)，可能已修复
80001,middle,2018-07-19,2018-07-20,,************;************,5.4,4.0,False,False,Oracle WebLogic Server SAML 组件安全漏洞(CVE-2018-2998),CVE-2018-2998,1,6,5.76,121,10.0,0,10,1,4.2,4.2,2.1,3.2512000000000003,52.16746666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2018-07-19)，可能已修复
71074,middle,2025-01-22,2025-01-22,,************;************,5.4,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2025-21540),CVE-2025-21540,1,6,5.76,48,10.0,0,10,1,4.2,4.2,2.1,3.2512000000000003,52.16746666666667,中危漏洞(级别:middle),漏洞未经确认验证
71074,middle,2023-10-17,2023-10-11,,10.229.27.93;10.228.16.18;************;************;10.228.29.239,5.3,4.0,False,False,Oracle MySQL Server 安全漏洞(CVE-2023-0466),CVE-2023-0466,1,6,5.72,6,3.0,0,10,1,4.2,4.2,2.1,2.8264,51.88426666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-17)，可能已修复
71074,middle,2023-09-14,2023-11-15,,10.228.26.65,5.3,4.0,False,False,Apache Tomcat 安全漏洞(CVE-2023-42795),CVE-2023-42795,1,6,5.72,15,7.5,0,10,2,4.2,4.2,4.2,8.9128,55.94186666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-09-14)，可能已修复
71074,middle,2023-10-10,2023-11-15,,10.228.26.65,5.3,4.0,False,False,Apache Tomcat 输入验证错误漏洞(CVE-2023-45648),CVE-2023-45648,1,6,5.72,15,7.5,0,10,2,4.2,4.2,4.2,8.9128,55.94186666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-10-10)，可能已修复
71074,middle,2019-01-10,2022-01-24,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,5.3,4.0,False,False,OpenSSH 访问限制绕过漏洞(CVE-2018-20685),CVE-2018-20685,1,6,5.72,6,3.0,0,10,1,4.2,4.2,2.1,2.8264,51.88426666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-01-10)，可能已修复
80001,middle,2020-04-15,2020-04-15,,************;************,5.3,4.0,False,False,Oracle WebLogic Server Console 组件安全漏洞(CVE-2020-2766),CVE-2020-2766,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-04-15)，可能已修复
71074,middle,2024-01-17,2024-01-20,,************;************,5.3,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20964),CVE-2024-20964,1,6,5.72,48,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-01-17)，可能已修复
71074,middle,2024-05-29,2025-01-02,,10.228.26.65,5.3,4.0,False,False,F5 Nginx 安全漏洞(CVE-2024-34161),CVE-2024-34161,1,6,5.72,15,7.5,0,10,2,4.2,4.2,4.2,8.9128,55.94186666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-05-29)，可能已修复
71074,middle,2024-05-29,2025-01-02,,10.228.26.65,5.3,4.0,False,False,F5 Nginx 安全漏洞(CVE-2024-35200),CVE-2024-35200,1,6,5.72,15,7.5,0,10,2,4.2,4.2,4.2,8.9128,55.94186666666667,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2024-05-29)，可能已修复
71074,middle,2024-04-16,2024-04-21,,************;************,5.3,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-20994),CVE-2024-20994,1,6,5.72,48,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2024-04-16)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,5.3,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21254),CVE-2022-21254,1,6,5.72,47,10.0,0,10,2,4.2,4.2,4.2,9.2128,56.141866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
80001,middle,2019-10-15,2019-10-21,,************;************,5.3,4.0,False,False,Oracle WebLogic Server EJB Container 组件安全漏洞(CVE-2019-2888),CVE-2019-2888,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2019-10-15)，可能已修复
71074,middle,2022-01-19,2022-01-20,,************,5.3,4.0,False,False,Oracle MySQL Server 输入验证错误漏洞(CVE-2022-21302),CVE-2022-21302,1,6,5.72,47,10.0,0,10,2,4.2,4.2,4.2,9.2128,56.141866666666665,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2022-01-19)，可能已修复
71074,middle,2021-09-15,2021-11-17,,************;************;************;************;************;10.228.16.18;************;*************;************;************;************;************;************;************;************,5.3,4.0,False,False,OpenSSH 安全漏洞(CVE-2016-20012),CVE-2016-20012,1,6,5.72,6,3.0,0,10,1,4.2,4.2,2.1,2.8264,51.88426666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-09-15)，可能已修复
71074,middle,2020-01-09,2021-11-17,,10.227.129.210;************,5.3,4.0,False,False,NGINX 环境问题漏洞(CVE-2019-20372),CVE-2019-20372,1,6,5.72,7,3.5,0,10,1,4.2,4.2,2.1,2.8564,51.904266666666665,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2020-01-09)，可能已修复
71074,middle,2023-10-20,2024-05-17,,************;************,5.3,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2023-5678),CVE-2023-5678,1,6,5.72,48,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2023-10-20)，可能已修复
71074,middle,2023-12-07,2024-05-17,,10.228.16.18,5.3,4.0,False,False,Python信息泄露漏洞（CVE-2023-40217）,CVE-2023-40217,1,6,5.72,14,7.0,0,10,2,4.2,4.2,4.2,8.8528,55.90186666666666,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证; 漏洞发现时间较久远(2023-12-07)，可能已修复
80001,middle,2016-07-21,2017-06-27,,************;************,5.3,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2016-3445),CVE-2016-3445,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-07-21)，可能已修复
80001,middle,2021-04-21,2021-04-21,,************;************,5.3,4.0,False,False,Oracle WebLogic Server 安全漏洞(CVE-2021-2204),CVE-2021-2204,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2021-04-21)，可能已修复
80001,middle,2016-10-25,2017-06-27,,************;************,5.3,4.0,False,False,Oracle Fusion Middleware Oracle WebLogic Server组件远程安全漏洞(CVE-2016-5488),CVE-2016-5488,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-10-25)，可能已修复
71074,middle,2024-10-15,2024-10-17,,************;************,5.3,4.0,False,False,Oracle MySQL 安全漏洞(CVE-2024-21238),CVE-2024-21238,1,6,5.72,48,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证
60001,middle,2018-08-15,2018-08-23,根据OpenSSH 用户枚举漏洞原理，通过从目标机获取敏感信息进行漏洞验证。,************;************;************;************;************;************;************;*************;************;************;************;************;************,5.3,3.0,True,False,OpenSSH 用户枚举漏洞(CVE-2018-15473)【原理扫描】,CVE-2018-15473,1,6,5.72,1,0.5,0,10,1,2.3,2.3,1.2,2.1364,51.42426666666667,中危漏洞(级别:middle); 漏洞详情: 根据OpenSSH 用户枚举漏洞原理，通过从目标机获取敏感信息进行漏洞验证。,漏洞发现时间较久远(2018-08-15)，可能已修复
80001,middle,2017-10-19,2017-10-23,,************;************,5.3,4.0,False,False,Oracle Weblogic Server Web Container组件安全漏洞(CVE-2017-10336),CVE-2017-10336,1,6,5.72,121,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2017-10-19)，可能已修复
71074,middle,2016-08-12,2016-11-23,,*************,5.3,4.0,False,False,Apache Tomcat 安全限制绕过漏洞(CVE-2016-6794),CVE-2016-6794,1,6,5.72,25,10.0,0,10,1,4.2,4.2,2.1,3.2464,52.16426666666666,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-08-12)，可能已修复
71074,middle,2016-01-19,2016-03-07,,************;*************,5.3,4.0,False,False,OpenSSH 拒绝服务漏洞(CVE-2016-1907),CVE-2016-1907,1,6,5.72,13,6.5,0,10,1,4.2,4.2,2.1,3.0364,52.02426666666667,中危漏洞(级别:middle),漏洞未经确认验证; 漏洞发现时间较久远(2016-01-19)，可能已修复
71074,middle,2024-10-14,2024-10-22,,*************,5.3,4.0,False,False,Eclipse Jetty 安全漏洞(CVE-2024-9823),CVE-2024-9823,1,6,5.72,3,1.5,0,10,2,4.2,4.2,4.2,8.1928,55.461866666666666,中危漏洞(级别:middle); 重要业务网段资产,漏洞未经确认验证
