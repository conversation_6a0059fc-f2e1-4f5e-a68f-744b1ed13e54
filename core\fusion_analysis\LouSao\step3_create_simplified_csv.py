import pandas as pd
import numpy as np
from step2_vuln_risk_score import step2

def create_simplified_csv():
    """
    创建简化版本的漏洞详细评分CSV文件
    去除不重要的字段，保留核心字段，并将数值格式化为一位小数
    """
    try:
        print("正在从step2获取漏洞评分数据...")
        # 直接调用step2方法获取数据，而不是读取CSV文件
        df, ip_risk_summary = step2()
        print(f"成功获取 {len(df)} 行数据，包含 {len(df.columns)} 个字段")
        
        # 定义要保留的重要字段
        important_columns = [
            'plugin_id',           # 插件ID - 漏洞标识
            'vuln_level',          # 漏洞级别 - 风险等级
            'target_ip',           # 目标IP - 受影响资产
            'severity_score',      # 严重程度评分 - CVSS评分
            'i18n_name',           # 漏洞名称 - 漏洞描述
            'cve_id',              # CVE编号 - 标准漏洞编号
            'final_score',         # 最终评分 - 综合风险评分
            'risk_reason'          # 风险原因 - 风险分析
        ]
        
        # 检查哪些字段存在于原始数据中
        existing_columns = [col for col in important_columns if col in df.columns]
        missing_columns = [col for col in important_columns if col not in df.columns]
        
        print(f"找到的重要字段: {existing_columns}")
        if missing_columns:
            print(f"缺失的字段: {missing_columns}")
        
        # 选择存在的重要列
        df_simplified = df[existing_columns].copy()
        
        # 格式化数值字段为一位小数
        numeric_columns = ['severity_score', 'final_score']
        for col in numeric_columns:
            if col in df_simplified.columns:
                print(f"正在格式化数值字段: {col}")
                # 转换为数值类型，无法转换的设为NaN
                df_simplified[col] = pd.to_numeric(df_simplified[col], errors='coerce')
                # 保留一位小数
                df_simplified[col] = df_simplified[col].round(1)
        
        # 处理空值 - 将NaN替换为空字符串
        df_simplified = df_simplified.fillna('')
        
        # 保存简化后的CSV文件

        # 显示数据统计
        print("\n📈 数据统计:")
        if 'vuln_level' in df_simplified.columns:
            level_counts = df_simplified['vuln_level'].value_counts()
            for level, count in level_counts.items():
                print(f"  {level}级漏洞: {count} 个")
        
        # print(df_simplified.head(10).to_string(index=False, max_colwidth=50))
        return df_simplified
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        print("请检查step2_vuln_risk_score模块是否正常工作")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 开始简化漏洞详细评分CSV文件...")
    print("=" * 50)
    
    result = create_simplified_csv()
    print(result.head(5).to_string())



    
