# IP流向（需求1）；时间、频率（需求3）误报判断



**内外网流向 + 业务相关性 + 工作时间+ 持续性 + 事件频率 + 威胁分”做场景化研判**，导出结果+给出多维统计



## 初始化与输入校验

读取 4 个 CSV：

- 主日志 `cleaned_security_logs.csv` → `df`（要求最少有 `srcAddress/destAddress/time/hour_slot/id`）
- 威胁分 `step5_ip_threat_score.csv` → `risk_df`（`hour_slot/srcAddress/threat_score`）
- 系统误报 `step6_fp_filtered.csv` → `fp_df`（`hour_slot/srcAddress/false_positive_flag`）
- 已知误报 IP 列表 `step6_fp_ip_list2.csv` → `false_positive_ips` 集合

字段校验：逐表检查**必需列**是否存在；缺列直接抛错，错误信息会列出当前表的全部列，便于定位。

时间列统一：`pd.to_datetime(..., errors='coerce')`，并**丢弃时间为空**的行（否则后面分组会出错）。

##  流向判定（内/外网）

- 用 `ipaddress.ip_address(ip).is_private` 判断是否内网；异常返回 False（即按“外网”处理）。
- 组合 `srcAddress`（源）与 `destAddress`（目的）是否内网，得到 4 类：
  - 源内&目内 → “内对内”→ 偏向低风险，除非分高且频率大
  - 源内&目外 → “内到外”→ 业务相关+工作时段多为正常；否则看分值判断泄露风险
  - 源外&目内 → “外到内”→ 可信来源多为正常；否则高分视为入侵
  - 源外&目外 → “外对外”→ 综合看分值和时间比例
- 写入 `df['flow_direction']`；顺手做一个按流向的计数打印，方便了解数据构成。



## 解析HTTP 请求头，分析业务相关性

预置两套列表：**业务相关池**（Prometheus/电力监控域名/面板地址等）vs **常见互联网池**（浏览器 UA、公共域名等）。

规则：

- 仅当流向是“内到外”，且 `random()<0.7`（70% 概率），才认定为**业务相关**；否则走“常见池”。

生成列：`user_agent/http_host/referer/origin/is_business_related`。

> 说明：这部分是**预设的**；真实生产可由原始 HTTP 头替代，需要相关白名单。

## 工作时间标记

- 定义：工作日（周一~周五）**08:30–17:30** 为工作时间。
- 逐行判断 `time`，得到 `df['is_work_time']`（布尔）。

## 小时 × 源IP 聚合（行为画像）

以 `['hour_slot','srcAddress']` 分组，得到**该小时内该源IP**的画像特征：

- `flow_directions`：去重后的流向列表（`sorted(set(x))`）
- 四类流向计数：`internal_to_internal_count / internal_to_external_count / external_to_internal_count / external_to_external_count`
- `business_related_count`：`is_business_related=True` 的条数
- `work_time_count`：`is_work_time=True` 的条数
- `total_events`：事件数（用 `id` 计数）
- 比例与频率：
  - `business_related_ratio = business_related_count / total_events`
  - `work_time_ratio = work_time_count / total_events`（同上）
  - `event_frequency = total_events`

> 这一层就是把“单条日志”变成“每小时每个源IP”的**行为摘要**。

## 合并外部信息

威胁分：从 `risk_df` 只取三列合并到聚合结果，`threat_score` 非数值转 NaN 后回填 0。

系统误报：`false_positive_flag` 左连接后缺失回填 `False`。

已知误报：`known_false_positive = srcAddress ∈ false_positive_ips`。

## “持续性 / 成簇 / 混合模式”三类增强指标

### 按 IP 的跨小时“持续性”

基于 `df[['srcAddress','hour_slot']]` 的**去重**表 `presence`（每 IP 每小时最多一行）：

- **是否有后续**：同一 IP 的**下一次出现**与当前 `hour_slot` 的小时差 `hours_to_next`；`<=24` 视为 `has_next_24h=True`。
- **连续小时游程**：同一 IP 的相邻出现时间差 `diff_hours==1` 视为同一“游程(run)”；对每个 run 计数为 `run_len`。
  - `current_run_hours = run_len`（当前行所在 run 的长度）
  - `max_run_hours`（该 IP 的历史最长 run）
- **滚动活跃小时数**：以 `hour_slot` 为索引，按 IP 做时间滚动窗口：
  - `active_hours_24h = rolling('24h').sum()`
  - `active_hours_72h = rolling('72h').sum()`
- 这些列合并回 `result_df`，缺失用 `0/False` 回填。

### 同小时“成簇”强度

- 定义“高热”IP：该小时内 `event_frequency>=5` **或** `threat_score>=60`。
- 以 `hour_slot` 分组，统计高热 IP 个数 → `hour_peer_ip_count`（该小时的“簇”大小）。

### 混合模式计数

- `mixed_flow_modes = len(flow_directions)`，同一小时同一 IP 出现的**不同流向**个数。

##  风险打分（调整分）与结论

### **调整分的组成**

在原始 `threat_score` 基础上，叠加 4 类因子得到 `_adjusted_threat_score`：

1. **时间因子 time_factor**（看 `work_time_ratio`）

   - > 0.7 → **-10**（工作时间为主，倾向正常）

   - < 0.3 → **+10**（非工作时间为主，偏异常）

2. **频率因子 frequency_factor**（看 `event_frequency`）

   - ≥ 10 → **+15**
   - ≥ 5 → **+10**
   - ≤ 1 → **-5**

3. **持续性因子 persistence_factor**

   - `current_run_hours ≥ 3` → **+10**（连续 ≥3 小时）
   - `active_hours_72h ≥ 12` → **+10**（72h 内活跃 ≥12 小时，长尾活跃）
   - `has_next_24h = True` → **+5**（后 24h 仍有告警）
   - `event_frequency ≤ 1` 且无后续 → **-5**（单次且没后续，偶发倾向）

4. **成簇/混合因子 cluster_factor**

   - `hour_peer_ip_count ≥ 5` → **+5**（同小时群体异常）
   - `mixed_flow_modes ≥ 2` → **+5**（同小时同 IP 多种流向，复杂）

### 结论规则（优先级与分支）

**按流向细则：**

A) **仅内对内**（只出现“内对内”，无其它流向）

- 若**单次**且**工作时间占比高**且**无连续** → “误报 - 工作时间内的单次内网事件”
- 否则看调整分：
  - < 40 → “误报 - 纯内网低风险流量”
  - < 60 → “可能误报 - 内网中等风险流量，建议复核”
  - ≥ 60 →
    - 若 `event_frequency ≥ 5` **或** `current_run_hours ≥ 3` → “需关注 - 内网高频/持续高风险活动，建议人工审核”
    - 否则 → “需关注 - 内网高风险活动，虽可能误报但需人工审核”

B) **内到外**

- 若业务相关性高 **且** 工作时间为主（`business_related_ratio>0.7` 且 `work_time_ratio>0.6`）：
  - 调整分 < 65 且 `current_run_hours<3` → “误报 - 工作时间内的业务相关正常外联请求”
  - 否则 → “可能误报 - 业务相关但分值/持续性较高，建议复核”
- 若业务相关性高 **且** 非工作时间为主：
  - 高频或持续（`event_frequency>5` 或 `current_run_hours≥3`）→ “需关注 - 非工作时间的高频/持续业务请求，建议复核”
  - 否则 → “可能误报 - 非工作时间的业务相关请求，建议抽查”
- 其他情况（看分）：
  - 调整分 ≥ 75：
    - 若高频/持续/成簇（`event_frequency≥5` 或 `current_run_hours≥3` 或 `hour_peer_ip_count≥5`）→ “高危 - 持续性/成簇的数据外联风险，需立即处理”
    - 否则 → “高危 - 可能的数据外泄，需检查请求头与目的地”
  - 调整分 ≥ 50 → “中危 - 外联行为需审核，检查 host/referer/origin/UA”
  - 否则 → “低危 - 可能为监控/巡检等正常外联”

C) **外到内**

- 若源 IP 在**可信来源白名单**：
  - 调整分 < 70：工作时间高 → “误报 - 工作时间内来自可信来源的流量”；否则 → “误报 - 来自可信来源(省公司/电科院)的流量”
  - 否则：高频或持续 → “需关注 - 来自可信来源的高频/持续高风险流量”；否则 → “可能误报 - 可信来源但分值较高，建议复核”
- 否则（非白名单）：
  - 调整分 ≥ 80：若高频/持续/成簇 → “高危 - 持续性外部入侵/成簇异常，需立即处置”，否则 → “高危 - 可能的外部入侵，需确认源IP归属”
  - 调整分 ≥ 60：工作时间高 → “中危 - 工作时间的外部探测行为，需审核”；否则 → “中危 - 非工作时间的外部探测行为，优先处理”
  - 否则：若单次且无后续 → “低危 - 单次外部访问，疑似偶发，建议观察”，否则 → “低危 - 正常外部访问，结合包与规则判断”

D) **混合流量**（同小时同 IP 出现 ≥2 种流向）

- 调整分 ≥ 75：若高频或持续 → “高危 - 持续性复杂流量模式，需立即处理”，否则 → “高危 - 复杂流量模式，结合上下文判断”
- 调整分 ≥ 55：非工作为主或成簇 → “中危 - 非工作时间或成簇的混合流量，优先审核”，否则 → “中危 - 需审核的混合流量，建议复核”
- 否则：若“工作时间高 & 单次 & 无后续” → “低危 - 工作时间内单次低风险事件”；否则 → “低危 - 正常混合流量，可能为业务请求”、

## 输出

导出 `step8_test_result.csv`，包含**用于回溯解释**的关键中间列：
 `business_related_ratio/work_time_ratio/event_frequency/_adjusted_threat_score/current_run_hours/active_hours_24h/72h/has_next_24h/hour_peer_ip_count/mixed_flow_modes/...`

打印 5 类统计表：

1. 各结论数目（研判分布）
2. **流向布尔组合** × 研判
3. **业务相关性分档(低/中/高)** × 研判
4. **工作时间分档** × 研判
5. **事件频率分档(单次/低频/中频/高频)** × 研判

也就是：

流向统计；

风险评估统计；

流向与风险评估交叉统计；

业务相关性与风险评估交叉统计；

工作时间与风险评估交叉统计；

事件频率与风险评估交叉统计。      ----------------------这是分步骤输出，便于后续去取相应的中间过程的值



总的输出：**IP流向场景化判断结果**

**业务占比 = 业务相关事件数 ÷ 总事件数**。

| 小时档位            | 源IP         | 流向集合 | 内对内 | 内到外 | 外到内 | 外对外 | 业务相关数 | 工作时段数 | 总事件 | 业务占比 | 工作占比 | 频率 | 原始分 | 系统误报 | 已知误报IP | 24小时内后续 | 连续小时 | 历史最长 | 近24h活跃 | 近72h活跃 | 当小时高热IP数 | 混合流向数 | 结论                                           | 最终威胁分 |
| ------------------- | ------------ | -------- | ------ | ------ | ------ | ------ | ---------- | ---------- | ------ | -------- | -------- | ---- | ------ | -------- | ---------- | ------------ | -------- | -------- | --------- | --------- | -------------- | ---------- | ---------------------------------------------- | ---------- |
| 2025-07-20 10:00:00 | ************ | [内对内] | 1      | 0      | 0      | 0      | 0          | 0          | 1      | 0.00     | 0.00     | 1    | 36.67  | 否       | 否         | 否           | 1        | 1        | 1.0       | 1.0       | 12             | 1          | 可能误报 - 内网中等风险流量，建议复核          | 41.67      |
| 2025-07-20 10:00:00 | **********   | [内对内] | 1      | 0      | 0      | 0      | 0          | 0          | 1      | 0.00     | 0.00     | 1    | 34.67  | 否       | 否         | 否           | 1        | 1        | 1.0       | 1.0       | 12             | 1          | 误报 - 纯内网低风险流量                        | 39.67      |
| 2025-07-20 10:00:00 | 10.1.96.122  | [内对内] | 1      | 0      | 0      | 0      | 0          | 0          | 1      | 0.00     | 0.00     | 1    | 32.67  | 否       | 否         | 否           | 1        | 1        | 1.0       | 1.0       | 12             | 1          | 误报 - 纯内网低风险流量                        | 37.67      |
| 2025-07-20 10:00:00 | 10.10.1.1    | [内对内] | 41     | 0      | 0      | 0      | 0          | 0          | 41     | 0.00     | 0.00     | 41   | 56.93  | 否       | 否         | 是           | 2        | 2        | 1.0       | 1.0       | 12             | 1          | 需关注 - 内网高频/持续高风险活动，建议人工审核 | 91.93      |
| 2025-07-20 10:00:00 | *********    | [内对内] | 55     | 0      | 0      | 0      | 0          | 0          | 55     | 0.00     | 0.00     | 55   | 81.91  | 是       | 是         | 是           | 3        | 3        | 1.0       | 1.0       | 12             | 1          | 误报 - 已知误报IP                              | 126.91     |