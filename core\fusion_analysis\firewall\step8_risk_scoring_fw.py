import pandas as pd
import numpy as np
import os
from step7_behavior_analysis_fw import main as get_behavior_analysis_data
from step6_false_positive_fw import get_trusted_source_whitelist

def calculate_adjusted_threat_score(df):
    """
    计算调整后的威胁分数
    """
    print("正在计算调整后的威胁分数...")
    
    # 初始化调整分为原始威胁分
    df['_adjusted_threat_score'] = df['threat_score']
    
    # 1. 时间因子
    df['time_factor'] = 0
    df.loc[df['work_time_ratio'] > 0.7, 'time_factor'] = -10  # 工作时间为主，倾向正常
    df.loc[df['work_time_ratio'] < 0.3, 'time_factor'] = 10   # 非工作时间为主，偏异常
    
    # 2. 频率因子
    df['frequency_factor'] = 0
    df.loc[df['event_frequency'] >= 10, 'frequency_factor'] = 15
    df.loc[(df['event_frequency'] >= 5) & (df['event_frequency'] < 10), 'frequency_factor'] = 10
    df.loc[df['event_frequency'] <= 1, 'frequency_factor'] = -5
    
    # 3. 持续性因子
    df['persistence_factor'] = 0
    df.loc[df['current_run_hours'] >= 3, 'persistence_factor'] += 10  # 连续≥3小时
    df.loc[df['active_hours_72h'] >= 12, 'persistence_factor'] += 10  # 72h内活跃≥12小时
    df.loc[df['has_next_24h'] == True, 'persistence_factor'] += 5    # 后24h仍有告警
    df.loc[(df['event_frequency'] <= 1) & (df['has_next_24h'] == False), 'persistence_factor'] -= 5  # 单次且没后续
    
    # 4. 成簇/混合因子
    df['cluster_factor'] = 0
    df.loc[df['hour_peer_ip_count'] >= 5, 'cluster_factor'] += 5  # 同小时群体异常
    df.loc[df['mixed_flow_modes'] >= 2, 'cluster_factor'] += 5    # 同小时同IP多种流向
    
    # 计算最终调整分
    df['_adjusted_threat_score'] = df['_adjusted_threat_score'] + \
                                 df['time_factor'] + \
                                 df['frequency_factor'] + \
                                 df['persistence_factor'] + \
                                 df['cluster_factor']
    
    # 限制在0-100范围内
    df['_adjusted_threat_score'] = df['_adjusted_threat_score'].clip(0, 100)
    
    return df

def generate_conclusion(df):
    """
    生成结论
    """
    print("正在生成结论...")
    
    # 初始化结论列
    df['conclusion'] = ""

    # 获取可信来源白名单（使用动态白名单系统）
    trusted_sources = get_trusted_source_whitelist()

    # 按流向细则处理
    for idx, row in df.iterrows():
        flow_directions = row['flow_directions']
        adjusted_score = row['_adjusted_threat_score']
        
        # A) 仅内对内（只出现"内对内"，无其它流向）
        if flow_directions == ['内对内']:
            if row['event_frequency'] <= 1 and row['work_time_ratio'] > 0.7 and row['current_run_hours'] < 3:
                df.loc[idx, 'conclusion'] = "误报 - 工作时间内的单次内网事件"
            elif adjusted_score < 40:
                df.loc[idx, 'conclusion'] = "误报 - 纯内网低风险流量"
            elif adjusted_score < 60:
                df.loc[idx, 'conclusion'] = "可能误报 - 内网中等风险流量，建议复核"
            else:  # >= 60
                if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3:
                    df.loc[idx, 'conclusion'] = "需关注 - 内网高频/持续高风险活动，建议人工审核"
                else:
                    df.loc[idx, 'conclusion'] = "需关注 - 内网高风险活动，虽可能误报但需人工审核"
        
        # B) 内到外
        elif '内到外' in flow_directions and len(flow_directions) == 1:
            if row['business_related_ratio'] > 0.7 and row['work_time_ratio'] > 0.6:
                if adjusted_score < 65 and row['current_run_hours'] < 3:
                    df.loc[idx, 'conclusion'] = "误报 - 工作时间内的业务相关正常外联请求"
                else:
                    df.loc[idx, 'conclusion'] = "可能误报 - 业务相关但分值/持续性较高，建议复核"
            elif row['business_related_ratio'] > 0.7 and row['work_time_ratio'] <= 0.6:
                if row['event_frequency'] > 5 or row['current_run_hours'] >= 3:
                    df.loc[idx, 'conclusion'] = "需关注 - 非工作时间的高频/持续业务请求，建议复核"
                else:
                    df.loc[idx, 'conclusion'] = "可能误报 - 非工作时间的业务相关请求，建议抽查"
            else:  # 其他情况（看分）
                if adjusted_score >= 75:
                    if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3 or row['hour_peer_ip_count'] >= 5:
                        df.loc[idx, 'conclusion'] = "高危 - 持续性/成簇的数据外联风险，需立即处理"
                    else:
                        df.loc[idx, 'conclusion'] = "高危 - 可能的数据外泄，需检查请求头与目的地"
                elif adjusted_score >= 50:
                    df.loc[idx, 'conclusion'] = "中危 - 外联行为需审核，检查 host/referer/origin/UA"
                else:
                    df.loc[idx, 'conclusion'] = "低危 - 可能为监控/巡检等正常外联"
        
        # C) 外到内
        elif '外到内' in flow_directions and len(flow_directions) == 1:
            if row['srcAddress'] in trusted_sources:
                if adjusted_score < 70:
                    if row['work_time_ratio'] > 0.6:
                        df.loc[idx, 'conclusion'] = "误报 - 工作时间内来自可信来源的流量"
                    else:
                        df.loc[idx, 'conclusion'] = "误报 - 来自可信来源(省公司/电科院)的流量"
                else:
                    if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3:
                        df.loc[idx, 'conclusion'] = "需关注 - 来自可信来源的高频/持续高风险流量"
                    else:
                        df.loc[idx, 'conclusion'] = "可能误报 - 可信来源但分值较高，建议复核"
            else:  # 非白名单
                if adjusted_score >= 80:
                    if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3 or row['hour_peer_ip_count'] >= 5:
                        df.loc[idx, 'conclusion'] = "高危 - 持续性外部入侵/成簇异常，需立即处置"
                    else:
                        df.loc[idx, 'conclusion'] = "高危 - 可能的外部入侵，需确认源IP归属"
                elif adjusted_score >= 60:
                    if row['work_time_ratio'] > 0.6:
                        df.loc[idx, 'conclusion'] = "中危 - 工作时间的外部探测行为，需审核"
                    else:
                        df.loc[idx, 'conclusion'] = "中危 - 非工作时间的外部探测行为，优先处理"
                else:
                    if row['event_frequency'] <= 1 and not row['has_next_24h']:
                        df.loc[idx, 'conclusion'] = "低危 - 单次外部访问，疑似偶发，建议观察"
                    else:
                        df.loc[idx, 'conclusion'] = "低危 - 正常外部访问，结合包与规则判断"
        
        # D) 混合流量（同小时同IP出现≥2种流向）
        elif len(flow_directions) >= 2:
            if adjusted_score >= 75:
                if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3:
                    df.loc[idx, 'conclusion'] = "高危 - 持续性复杂流量模式，需立即处理"
                else:
                    df.loc[idx, 'conclusion'] = "高危 - 复杂流量模式，结合上下文判断"
            elif adjusted_score >= 55:
                if row['work_time_ratio'] <= 0.5 or row['hour_peer_ip_count'] >= 5:
                    df.loc[idx, 'conclusion'] = "中危 - 非工作时间或成簇的混合流量，优先审核"
                else:
                    df.loc[idx, 'conclusion'] = "中危 - 需审核的混合流量，建议复核"
            else:
                if row['work_time_ratio'] > 0.7 and row['event_frequency'] <= 1 and not row['has_next_24h']:
                    df.loc[idx, 'conclusion'] = "低危 - 工作时间内单次低风险事件"
                else:
                    df.loc[idx, 'conclusion'] = "低危 - 正常混合流量，可能为业务请求"
        
        # 其他情况（外对外或未知）
        else:
            if adjusted_score >= 70:
                df.loc[idx, 'conclusion'] = "需关注 - 外对外高风险流量，需确认源目IP归属"
            else:
                df.loc[idx, 'conclusion'] = "低危 - 外对外流量，可能为正常转发"
    
    return df

def generate_statistics(df):
    """
    生成统计表并返回统计结果
    """
    print("正在生成统计表...")
    
    # 1. 各结论数目（研判分布）
    conclusion_counts = df['conclusion'].value_counts()
    print("\n1. 各结论数目（研判分布）:")
    for conclusion, count in conclusion_counts.items():
        print(f"{conclusion}: {count}条记录")
    
    # 2. 流向布尔组合 × 研判
    flow_conclusion = pd.crosstab(
        df['flow_directions'].apply(lambda x: ', '.join(x)),
        df['conclusion']
    )
    print("\n2. 流向布尔组合 × 研判:")
    print(flow_conclusion)
    
    # 3. 业务相关性分档 × 研判
    # 将业务相关性比例分为低/中/高三档
    df['business_related_level'] = pd.cut(
        df['business_related_ratio'], 
        bins=[0, 0.3, 0.7, 1], 
        labels=['低(0-30%)', '中(30-70%)', '高(70-100%)'],
        include_lowest=True
    )
    business_conclusion = pd.crosstab(df['business_related_level'], df['conclusion'])
    print("\n3. 业务相关性分档 × 研判:")
    print(business_conclusion)
    
    # 4. 工作时间分档 × 研判
    df['work_time_level'] = pd.cut(
        df['work_time_ratio'], 
        bins=[0, 0.3, 0.7, 1], 
        labels=['非工作为主(0-30%)', '混合(30-70%)', '工作为主(70-100%)'],
        include_lowest=True
    )
    work_time_conclusion = pd.crosstab(df['work_time_level'], df['conclusion'])
    print("\n4. 工作时间分档 × 研判:")
    print(work_time_conclusion)
    
    # 5. 事件频率分档 × 研判
    df['frequency_level'] = pd.cut(
        df['event_frequency'], 
        bins=[0, 1, 3, 10, float('inf')], 
        labels=['单次', '低频(2-3)', '中频(4-10)', '高频(>10)'],
        include_lowest=True
    )
    frequency_conclusion = pd.crosstab(df['frequency_level'], df['conclusion'])
    print("\n5. 事件频率分档 × 研判:")
    print(frequency_conclusion)
    
    # 返回统计结果字典
    statistics_result = {
        'conclusion_counts': conclusion_counts,
        'flow_conclusion': flow_conclusion,
        'business_conclusion': business_conclusion,
        'work_time_conclusion': work_time_conclusion,
        'frequency_conclusion': frequency_conclusion
    }
    
    return df, statistics_result

def main():
    # 从step7_behavior_analysis获取处理后的数据
    df = get_behavior_analysis_data()
    
    if df is None or df.empty:
        print("错误: 未获取到step7行为分析数据")
        return None, None
    
    print(f"成功获取到 {len(df)} 条行为分析数据")
    
    # 检查必需列是否存在
    required_columns = ['hour_slot', 'srcAddress', 'flow_directions', 'business_related_ratio', 
                      'work_time_ratio', 'event_frequency', 'threat_score', 'current_run_hours', 
                      'active_hours_24h', 'active_hours_72h', 'has_next_24h', 'hour_peer_ip_count', 
                      'mixed_flow_modes']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 输入文件缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return None, None
    
    # 处理flow_directions列（如果是字符串形式）
    if df['flow_directions'].dtype == 'object':
        # 检查第一个非空值是否为字符串
        first_value = df['flow_directions'].dropna().iloc[0] if not df['flow_directions'].dropna().empty else None
        if isinstance(first_value, str):
            df['flow_directions'] = df['flow_directions'].apply(eval)  # 将字符串转换为列表
        # 如果已经是列表格式，则不需要转换
    
    # 计算调整后的威胁分数
    df = calculate_adjusted_threat_score(df)
    
    # 生成结论
    df = generate_conclusion(df)
    
    # 生成统计表
    df, statistics_result = generate_statistics(df)

    return df, statistics_result

if __name__ == "__main__":
    main()