#!/usr/bin/env python
# encoding:utf-8
"""
集成测试脚本 - 验证step4_work_time.py与step3_business_analysis.py的集成
"""

import pandas as pd
from step4_work_time import get_business_analysis_data, mark_work_time

def test_step4_integration():
    """
    测试step4与step3的集成
    """
    print("=== Step4 集成测试开始 ===")
    
    # 1. 获取业务相关性分析数据
    print("\n1. 获取业务相关性分析数据...")
    df = get_business_analysis_data()
    
    if df is None:
        print("❌ 获取数据失败")
        return False
    
    print(f"✅ 成功获取 {len(df)} 条记录")
    
    # 2. 检查数据结构
    print("\n2. 检查数据结构...")
    expected_columns = [
        'id', 'time', 'srcAddress', 'destAddress', 'flow_direction',
        'user_agent', 'http_host', 'referer', 'origin', 'is_business_related'
    ]
    
    missing_columns = [col for col in expected_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 缺少必需列: {missing_columns}")
        return False
    
    print("✅ 数据结构检查通过")
    print(f"   当前列数: {len(df.columns)}")
    print(f"   主要列: {list(df.columns[:10])}...")
    
    # 3. 检查时间格式
    print("\n3. 检查时间格式...")
    if not pd.api.types.is_datetime64_any_dtype(df['time']):
        print("❌ 时间列格式不正确")
        return False
    
    print("✅ 时间格式检查通过")
    
    # 4. 标记工作时间
    print("\n4. 标记工作时间...")
    df_with_work_time = mark_work_time(df.copy())
    
    if 'is_work_time' not in df_with_work_time.columns:
        print("❌ 工作时间标记失败")
        return False
    
    print("✅ 工作时间标记完成")
    
    # 5. 统计结果
    print("\n5. 统计分析结果...")
    
    # 流向统计
    flow_stats = df['flow_direction'].value_counts()
    print("\n流向统计:")
    for direction, count in flow_stats.items():
        print(f"  {direction}: {count}条记录")
    
    # 业务相关性统计
    business_stats = df['is_business_related'].value_counts()
    print("\n业务相关性统计:")
    print(f"  业务相关: {business_stats.get(True, 0)}条记录")
    print(f"  非业务相关: {business_stats.get(False, 0)}条记录")
    
    # 工作时间统计
    work_time_stats = df_with_work_time['is_work_time'].value_counts()
    print("\n工作时间统计:")
    print(f"  工作时间: {work_time_stats.get(True, 0)}条记录")
    print(f"  非工作时间: {work_time_stats.get(False, 0)}条记录")
    
    print("\n=== Step4 集成测试成功 ===")
    return True

if __name__ == "__main__":
    success = test_step4_integration()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败！")