#!/usr/bin/env python
# encoding:utf-8
"""
集成测试脚本：验证step6_false_positive.py与step5_temp.py和step4_work_time.py之间的集成
"""

import pandas as pd
from step6_false_positive import get_false_positive_data, process_false_positives, main

def test_step6_integration():
    """
    测试step6误报分析与前置步骤的集成
    """
    print("=== Step6 误报分析集成测试 ===")
    
    try:
        # 1. 测试获取数据
        print("\n1. 测试数据获取...")
        work_time_df, threat_score_df = get_false_positive_data()
        
        print(f"工作时间数据: {len(work_time_df)} 条记录")
        print(f"威胁评分数据: {len(threat_score_df)} 条记录")
        
        # 2. 检查数据结构
        print("\n2. 检查数据结构...")
        print("工作时间数据列:", list(work_time_df.columns))
        print("威胁评分数据列:", list(threat_score_df.columns))
        
        # 检查必需列
        required_work_time_cols = ['hour_slot', 'srcAddress', 'flow_direction', 'is_business_related', 'is_work_time']
        required_threat_cols = ['hour_slot', 'srcAddress', 'threat_score']
        
        missing_work_cols = [col for col in required_work_time_cols if col not in work_time_df.columns]
        missing_threat_cols = [col for col in required_threat_cols if col not in threat_score_df.columns]
        
        if missing_work_cols:
            print(f"警告: 工作时间数据缺少列: {missing_work_cols}")
        else:
            print("✓ 工作时间数据结构检查通过")
            
        if missing_threat_cols:
            print(f"警告: 威胁评分数据缺少列: {missing_threat_cols}")
        else:
            print("✓ 威胁评分数据结构检查通过")
        
        # 3. 检查时间格式
        print("\n3. 检查时间格式...")
        if 'hour_slot' in work_time_df.columns:
            print(f"工作时间数据时间格式示例: {work_time_df['hour_slot'].iloc[0]}")
        if 'hour_slot' in threat_score_df.columns:
            print(f"威胁评分数据时间格式示例: {threat_score_df['hour_slot'].iloc[0]}")
        
        # 4. 测试误报分析
        print("\n4. 测试误报分析...")
        fp_df, known_fps = process_false_positives(work_time_df, threat_score_df)
        
        print(f"误报分析结果: {len(fp_df)} 条记录")
        print(f"已知误报IP数量: {len(known_fps)}")
        
        # 5. 统计分析结果
        print("\n5. 统计分析结果...")
        
        # 流向统计
        if 'flow_direction' in work_time_df.columns:
            flow_stats = work_time_df['flow_direction'].value_counts()
            print("\n流向分布:")
            for direction, count in flow_stats.items():
                print(f"  {direction}: {count}条")
        
        # 业务相关性统计
        if 'is_business_related' in work_time_df.columns:
            business_stats = work_time_df['is_business_related'].value_counts()
            print("\n业务相关性分布:")
            for related, count in business_stats.items():
                status = "业务相关" if related else "非业务相关"
                print(f"  {status}: {count}条")
        
        # 工作时间统计
        if 'is_work_time' in work_time_df.columns:
            work_time_stats = work_time_df['is_work_time'].value_counts()
            print("\n工作时间分布:")
            for is_work, count in work_time_stats.items():
                status = "工作时间" if is_work else "非工作时间"
                print(f"  {status}: {count}条")
        
        # 威胁等级统计
        if 'threat_level' in threat_score_df.columns:
            threat_stats = threat_score_df['threat_level'].value_counts()
            print("\n威胁等级分布:")
            for level, count in threat_stats.items():
                print(f"  {level}: {count}条")
        
        # 误报统计
        if 'false_positive_flag' in fp_df.columns:
            fp_stats = fp_df['false_positive_flag'].value_counts()
            print("\n误报分布:")
            for is_fp, count in fp_stats.items():
                status = "误报" if is_fp else "非误报"
                print(f"  {status}: {count}条")
        
        # 威胁分数统计
        if 'threat_score' in fp_df.columns:
            threat_scores = fp_df['threat_score']
            print("\n威胁分数统计:")
            print(f"  平均分: {threat_scores.mean():.2f}")
            print(f"  最高分: {threat_scores.max():.2f}")
            print(f"  最低分: {threat_scores.min():.2f}")
        
        print("\n=== 集成测试成功 ===")
        return True
        
    except Exception as e:
        print(f"\n集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_function():
    """
    测试main函数
    """
    print("\n=== 测试main函数 ===")
    
    try:
        result = main()
        if result is not None:
            print(f"main函数返回结果: {len(result)} 条记录")
            print("✓ main函数测试通过")
            return True
        else:
            print("✗ main函数返回None")
            return False
    except Exception as e:
        print(f"main函数测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 运行集成测试
    success1 = test_step6_integration()
    success2 = test_main_function()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败！")