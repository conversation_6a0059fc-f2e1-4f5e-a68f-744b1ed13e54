import os
import time
import pandas as pd
from datetime import datetime

# 导入各个步骤的模块
from step1_parse_vuln_data import get_security_logs_from_db, parse_vuln_data
from step2_vuln_risk_score import calculate_comprehensive_score, generate_ip_risk_summary
from step3_create_simplified_csv import create_simplified_csv

def run_full_workflow():
    """
    运行完整的漏洞数据分析工作流程
    包括数据解析、风险评分计算和报告生成
    """
    start_time = time.time()
    print(f"\n{'='*60}")
    print(f"🚀 开始运行绿盟漏洞数据分析工作流 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    # 步骤1: 解析漏洞数据
    print("\n📋 步骤1: 从数据库获取并解析漏洞数据")
    print("-" * 40)
    try:
        print("正在从数据库获取漏洞数据...")
        raw_data = get_security_logs_from_db()
        
        print("正在解析漏洞数据...")
        df = parse_vuln_data(raw_data)
        
        step1_output = "step1_parsed_vuln_data.csv"
        df.to_csv(step1_output, index=False, encoding='utf-8-sig')
        print(f"✅ 数据解析完成！")
        print(f"📊 共解析 {len(df)} 条漏洞记录")
        print(f"💾 数据已保存至: {step1_output}")
    except Exception as e:
        print(f"❌ 步骤1失败: {e}")
        return
    
    # 步骤2: 计算风险评分
    print("\n📊 步骤2: 计算漏洞风险评分")
    print("-" * 40)
    try:
        df_scored = calculate_comprehensive_score(df)
        ip_risk_summary = generate_ip_risk_summary(df_scored)
        
        step2_output1 = "step2_vuln_detailed_scores.csv"
        step2_output2 = "step2_vuln_ip_risk_summary.csv"
        
        df_scored.to_csv(step2_output1, index=False, encoding='utf-8-sig')
        ip_risk_summary.to_csv(step2_output2, index=False, encoding='utf-8-sig')
        
        print(f"✅ 风险评分计算完成！")
        print(f"📊 共处理 {len(df_scored)} 条漏洞记录")
        print(f"📊 涉及 {len(ip_risk_summary)} 个IP地址")
        print(f"💾 详细评分已保存至: {step2_output1}")
        print(f"💾 IP风险汇总已保存至: {step2_output2}")
        
        # 验证评分范围
        print("\n=== 评分范围验证 ===")
        for level in ['high', 'medium', 'low']:
            level_data = df_scored[df_scored['vuln_level'] == level]
            if len(level_data) == 0:
                level_data = df_scored[df_scored['vuln_level'] == 'middle']
                if len(level_data) > 0 and level == 'medium':
                    print(f"{level}级别 (标记为middle): {len(level_data)} 条记录")
                else:
                    continue
            else:
                print(f"{level}级别: {len(level_data)} 条记录")
            
            scores = level_data['final_score']
            print(f"  评分范围: {scores.min():.1f} - {scores.max():.1f}")
            print(f"  平均评分: {scores.mean():.1f}")
    except Exception as e:
        print(f"❌ 步骤2失败: {e}")
        return
    
    # 步骤3: 创建简化CSV报告
    print("\n📑 步骤3: 创建简化版漏洞报告")
    print("-" * 40)
    try:
        # 为了确保step3能正确读取step2的输出，我们需要复制文件
        df_scored.to_csv('vuln_detailed_scores.csv', index=False, encoding='utf-8-sig')
        ip_risk_summary.to_csv('vuln_ip_risk_summary.csv', index=False, encoding='utf-8-sig')
        
        df_simplified = create_simplified_csv()
        if df_simplified is not None:
            print("✅ 简化报告创建成功！")
        else:
            print("❌ 简化报告创建失败")
    except Exception as e:
        print(f"❌ 步骤3失败: {e}")
        return
    
    # 总结
    end_time = time.time()
    duration = end_time - start_time
    print(f"\n{'='*60}")
    print(f"✅ 工作流程全部完成！用时: {duration:.2f} 秒")
    print(f"生成的文件:")
    print(f"  1. {step1_output} - 原始漏洞数据解析结果")
    print(f"  2. {step2_output1} - 详细风险评分数据")
    print(f"  3. {step2_output2} - IP风险汇总数据")
    print(f"  4. step3_vuln_detailed_scores_simplified.csv - 简化版漏洞报告")
    print(f"{'='*60}\n")

if __name__ == "__main__":
    run_full_workflow()