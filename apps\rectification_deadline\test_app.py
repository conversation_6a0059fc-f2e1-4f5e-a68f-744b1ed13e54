#!/usr/bin/env python
# encoding:utf-8

"""
测试整改截止时间查询APP
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_query_overdue():
    """测试查询超期记录功能"""
    try:
        from main.run import query_overdue
        
        print("=== 测试整改截止时间查询APP ===")
        
        # 测试默认参数
        print("\n1. 测试默认参数 (limit=10):")
        result = await query_overdue()
        print(f"状态: {result['status']}")
        if result['status'] == 0:
            data = result['result']
            print(f"超期记录数量: {data['overdue_count']}")
            print(f"当前日期: {data['current_date']}")
            print(f"查询限制: {data['query_limit']}")
            
            if data['records']:
                print("\n超期记录详情:")
                for i, record in enumerate(data['records'][:3], 1):  # 只显示前3条
                    print(f"  {i}. {record['serial_number']} - {record['vuln_name']}")
                    print(f"     截止时间: {record['rect_deadline']}")
                    print(f"     超期天数: {record['days_overdue']}天")
                    print(f"     责任单位: {record['responsible_dept']}")
                    print()
            else:
                print("  当前没有超期记录")
        else:
            print(f"错误: {result['result']}")
        
        # 测试自定义limit参数
        print("\n2. 测试自定义参数 (limit=5):")
        result = await query_overdue(5)
        print(f"状态: {result['status']}")
        if result['status'] == 0:
            data = result['result']
            print(f"超期记录数量: {data['overdue_count']}")
            print(f"查询限制: {data['query_limit']}")
        else:
            print(f"错误: {result['result']}")
            
        # 测试无效参数
        print("\n3. 测试无效参数 (limit=-1):")
        result = await query_overdue(-1)
        print(f"状态: {result['status']}")
        if result['status'] == 0:
            data = result['result']
            print(f"超期记录数量: {data['overdue_count']}")
            print(f"查询限制: {data['query_limit']}")
        else:
            print(f"错误: {result['result']}")
            
    except ImportError as e:
        print(f"导入失败: {e}")
        print("请确保在正确的环境中运行此测试")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_query_overdue()) 