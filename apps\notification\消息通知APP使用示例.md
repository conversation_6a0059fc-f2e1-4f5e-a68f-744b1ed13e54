# 消息通知APP使用示例

## 1. 基本使用方法

### 方式1：用户提供TOKEN（推荐）

在剧本编辑器中配置消息通知APP时，提供所有必要参数：

```json
{
  "script_type": "s60000_pending",
  "result_data": "{{上一个APP的输出}}",
  "execution_id": "{{剧本执行ID}}",
  "token": "W5_TOKEN_abc123def456"
}
```

**优点：**
- 可控性强，使用指定的TOKEN
- 不依赖系统用户配置
- 执行速度快，无需查询数据库

### 方式2：自动获取系统TOKEN

不提供token参数，让APP自动获取：

```json
{
  "script_type": "s60000_pending", 
  "result_data": "{{上一个APP的输出}}",
  "execution_id": "{{剧本执行ID}}"
}
```

**优点：**
- 配置简单，无需手动管理TOKEN
- 自动处理TOKEN过期问题
- 适合系统内部调用

## 2. 不同剧本类型的配置示例

### S60000待处理剧本
```json
{
  "script_type": "s60000_pending",
  "result_data": "{\"processed_count\": 5}",
  "token": "用户TOKEN"
}
```

### 邮件剧本
```json
{
  "script_type": "email",
  "result_data": "{\"mails_count\": 3}",
  "token": "用户TOKEN"
}
```

### 奇安信天眼和IDS剧本
```json
{
  "script_type": "qianxin_ids",
  "result_data": "{\"total\": 10}",
  "token": "用户TOKEN"
}
```

### 绿盟漏扫剧本
```json
{
  "script_type": "nsfocus_scan",
  "result_data": "{\"total_tasks\": 2, \"total_vulns\": 8}",
  "token": "用户TOKEN"
}
```

### S60000反馈截止剧本
```json
{
  "script_type": "s60000_feedback",
  "result_data": "{\"ddl_entry_count\": 1}",
  "token": "用户TOKEN"
}
```

## 3. 在剧本中的集成方式

### 步骤1：添加业务APP
首先添加具体的业务APP（如S60000查询、邮件检查等）

### 步骤2：添加消息通知APP
在业务APP后面添加"消息通知处理"APP

### 步骤3：配置参数映射
- `script_type`: 根据业务类型选择
- `result_data`: 使用`{{上一个APP的输出}}`
- `execution_id`: 使用`{{剧本执行ID}}`
- `token`: 可选，提供用户TOKEN或留空自动获取

### 步骤4：配置条件执行（可选）
可以设置条件，只有在特定情况下才执行通知APP

## 4. 错误处理

### 常见错误及解决方案

#### 错误1：TOKEN获取失败
```json
{
  "status": 1,
  "result": "TOKEN获取失败: 未找到ID为1的用户，请确保数据库中存在系统用户"
}
```
**解决方案：** 在数据库中创建ID为1的系统用户

#### 错误2：JSON解析失败
```json
{
  "status": 1,
  "result": "JSON解析失败: Expecting value: line 1 column 1 (char 0)"
}
```
**解决方案：** 检查result_data参数是否为有效的JSON格式

#### 错误3：HTTP请求失败
```json
{
  "status": 1,
  "result": "HTTP请求失败，状态码: 401"
}
```
**解决方案：** 检查TOKEN是否有效，或提供正确的TOKEN

## 5. 最佳实践

### 1. TOKEN管理
- 推荐使用用户提供的TOKEN方式
- 定期检查TOKEN有效性
- 为不同环境使用不同的TOKEN

### 2. 参数配置
- 使用变量映射而不是硬编码
- 添加必要的错误处理逻辑
- 记录执行日志便于调试

### 3. 性能优化
- 避免频繁的TOKEN获取操作
- 合理设置超时时间
- 监控APP执行性能

### 4. 安全考虑
- 不在日志中输出完整TOKEN
- 使用HTTPS进行API调用
- 定期更新TOKEN

## 6. 调试技巧

### 查看执行日志
```bash
# 查看APP执行日志
tail -f logs/app.log | grep "消息通知"
```

### 测试APP功能
```bash
# 运行测试脚本
python test_simple_token.py
```

### 手动测试API
```bash
# 测试通知处理接口
curl -X POST http://localhost:8888/api/v1/soar/notification/process \
-H "Content-Type: application/json" \
-H "token: W5_TOKEN_D10C13E39B6CDAA4BF6B531E8479BD1E" \
-d '{
  "script_type": "s60000_pending",
  "result": {"processed_count": 1}
}'
```

```bash
# 测试通知处理接口
curl -X POST http://localhost:8888/api/v1/soar/notification/process \
-H "Content-Type: application/json" \
-d '{
  "script_type": "s60000_pending",
  "result": {"processed_count": 5}
}'
```

## 7. 常见问题FAQ

**Q: 什么时候使用用户提供的TOKEN？**
A: 当你有明确的TOKEN且希望精确控制权限时使用。

**Q: 什么时候使用自动获取的系统TOKEN？**
A: 当你希望简化配置且信任系统自动管理TOKEN时使用。

**Q: APP执行失败怎么办？**
A: 查看返回的错误信息，根据错误类型进行相应的处理。

**Q: 如何确认通知是否发送成功？**
A: 检查APP返回结果中的`notification_created`字段。

这个使用示例提供了完整的配置和使用指导，帮助用户正确使用消息通知APP。
