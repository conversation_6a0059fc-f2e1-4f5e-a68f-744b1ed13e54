# mysqldump -u root -p --no-data --single-transaction --routines --triggers --databases w5_db > schema_only.sql
import re
import os
import sys

# ----------------------------
# 配置
# ----------------------------
INPUT_FILE = "schema_only.sql"
OUTPUT_FILE = "sync_schema.sql"

if len(sys.argv) > 1:
    INPUT_FILE = sys.argv[1]

# ----------------------------
# 主函数
# ----------------------------
def main():
    print(f"🔍 正在处理: {INPUT_FILE}")

    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"❌ 错误: 文件 '{INPUT_FILE}' 不存在！")
        return
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return

    # 1. 清理：移除 /*! ... */、-- 注释、DROP TABLE
    content = re.sub(r'/\*\!.*?\*/', '', content, flags=re.DOTALL)
    content = re.sub(r'--.*?$', '', content, flags=re.MULTILINE)
    content = re.sub(r'#.*?$', '', content, flags=re.MULTILINE)
    content = re.sub(r'DROP\s+TABLE\s+IF\s+EXISTS\s+`?\w+`?;\s*', '', content, flags=re.IGNORECASE)
    content = re.sub(r'\n\s*\n', '\n', content)  # 去空行

    # 2. 匹配完整的 CREATE TABLE ... );
    create_pattern = re.compile(
        r'(CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?[`"]?(\w+)[`"]?\s*\([\s\S]*?\)\s*ENGINE\s*=[^;]*;)',
        re.IGNORECASE
    )

    matches = create_pattern.finditer(content)
    tables = []

    for match in matches:
        full_sql = match.group(1).strip()
        table_name = match.group(2)

        # 提取字段定义（括号内）
        body_match = re.search(r'\(\s*([\s\S]*?)\s*\)\s*ENGINE', full_sql, re.IGNORECASE)
        if not body_match:
            continue

        body = body_match.group(1).strip()
        
        # 智能字段解析
        lines = []
        current_line = ""
        paren_count = 0
        
        for char in body:
            if char == '(':
                paren_count += 1
                current_line += char
            elif char == ')':
                paren_count -= 1
                current_line += char
            elif char == ',' and paren_count == 0:
                lines.append(current_line.strip())
                current_line = ""
            else:
                current_line += char
        
        if current_line.strip():
            lines.append(current_line.strip())

        fields = []
        indexes = []  # 存储索引信息
        primary_keys = []  # 存储主键信息

        for line in lines:
            line = re.sub(r'\s+', ' ', line.strip())
            if not line:
                continue

            upper_line = line.upper()
            
            # 处理主键
            if upper_line.startswith('PRIMARY KEY'):
                primary_keys.append(line)
                continue
                
            # 处理索引（INDEX, KEY, UNIQUE KEY, UNIQUE INDEX）
            index_keywords = ('KEY ', 'INDEX ', 'UNIQUE KEY', 'UNIQUE INDEX', 'FULLTEXT KEY', 'FULLTEXT INDEX')
            if any(upper_line.startswith(k) for k in index_keywords):
                indexes.append(line)
                continue
                
            # 处理外键
            if upper_line.startswith('CONSTRAINT') or upper_line.startswith('FOREIGN KEY'):
                indexes.append(line)
                continue

            # 提取普通字段
            col_match = re.match(r"^[`\"']?(\w+)[`\"']?\s+", line)
            if col_match:
                col_name = col_match.group(1)
                col_def = re.sub(r"^[`\"']?\w+[`\"']?\s+", "", line, count=1)
                fields.append((col_name, col_def))
            else:
                print(f"⚠️ 忽略无法解析的行: {line}")

        tables.append({
            'name': table_name,
            'fields': fields,
            'indexes': indexes,
            'primary_keys': primary_keys,
            'sql': full_sql
        })

    if not tables:
        print("❌ 未找到任何表，请检查 SQL 格式")
        print("💡 确保文件中有 'CREATE TABLE `table_name` (...)' 结构")
        return

    print(f"✅ 成功提取 {len(tables)} 个表:")

    # 3. 生成安全同步 SQL
    output_lines = []
    output_lines.append("-- ****************************************************")
    output_lines.append("-- 🔐 安全数据库结构同步脚本（自动生成）")
    output_lines.append("-- 功能: 创建表（若不存在），添加字段（忽略错误），修改字段定义，添加索引")
    output_lines.append("-- 注意: 可重复执行，不会报错")
    output_lines.append("-- ****************************************************")
    output_lines.append("")
    output_lines.append("-- 先创建所有表（如果不存在）")
    output_lines.append("")

    # 先创建所有表
    for tbl in tables:
        print(f"   📂 `{tbl['name']}` → {len(tbl['fields'])} 个字段, {len(tbl['indexes'])} 个索引, {len(tbl['primary_keys'])} 个主键")

        create_stmt = re.sub(
            r'CREATE\s+TABLE\s+(?!IF\s+NOT\s+EXISTS\s+)',
            'CREATE TABLE IF NOT EXISTS ',
            tbl['sql'],
            flags=re.IGNORECASE
        )
        output_lines.append(create_stmt + "\n")

    output_lines.append("-- ****************************************************")
    output_lines.append("-- 第一步: 添加字段（如果不存在，忽略错误）")
    output_lines.append("-- ****************************************************")
    output_lines.append("")

    # 第一步：先尝试添加字段（使用特殊注释忽略错误）
    for tbl in tables:
        output_lines.append(f"-- 为表 `{tbl['name']}` 添加字段（如果不存在）")
        
        for col_name, col_def in tbl['fields']:
            add_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD COLUMN `{col_name}` {col_def} */;"
            output_lines.append(add_stmt)
            
        output_lines.append("")

    output_lines.append("-- ****************************************************")
    output_lines.append("-- 第二步: 修改字段定义（确保字段使用最新定义）")
    output_lines.append("-- ****************************************************")
    output_lines.append("")

    # 第二步：修改字段定义（确保使用最新的定义）
    for tbl in tables:
        output_lines.append(f"-- 修改表 `{tbl['name']}` 的字段定义")
        
        for col_name, col_def in tbl['fields']:
            modify_stmt = f"ALTER TABLE `{tbl['name']}` MODIFY COLUMN `{col_name}` {col_def};"
            output_lines.append(modify_stmt)
            
        output_lines.append("")

    output_lines.append("-- ****************************************************")
    output_lines.append("-- 第三步: 添加索引（如果不存在，忽略错误）")
    output_lines.append("-- ****************************************************")
    output_lines.append("")

    # 第三步：处理索引
    for tbl in tables:
        if tbl['indexes'] or tbl['primary_keys']:
            output_lines.append(f"-- 为表 `{tbl['name']}` 添加索引")
            
            # 处理主键
            for pk in tbl['primary_keys']:
                # 提取主键字段
                pk_match = re.search(r'\(([^)]+)\)', pk)
                if pk_match:
                    pk_columns = pk_match.group(1).replace('`', '').replace(' ', '')
                    # 使用 IGNORE 关键字避免重复主键错误
                    add_pk_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD {pk} */;"
                    output_lines.append(add_pk_stmt)
            
            # 处理其他索引
            for index in tbl['indexes']:
                # 判断索引类型
                index_upper = index.upper()
                if index_upper.startswith('UNIQUE KEY') or index_upper.startswith('UNIQUE INDEX'):
                    # 提取索引名和字段
                    unique_match = re.search(r'UNIQUE\s+(?:KEY|INDEX)\s+[`"]?(\w+)[`"]?\s*\(([^)]+)\)', index, re.IGNORECASE)
                    if unique_match:
                        index_name = unique_match.group(1)
                        add_index_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD {index} */;"
                        output_lines.append(add_index_stmt)
                elif index_upper.startswith('FULLTEXT KEY') or index_upper.startswith('FULLTEXT INDEX'):
                    fulltext_match = re.search(r'FULLTEXT\s+(?:KEY|INDEX)\s+[`"]?(\w+)[`"]?\s*\(([^)]+)\)', index, re.IGNORECASE)
                    if fulltext_match:
                        index_name = fulltext_match.group(1)
                        add_index_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD {index} */;"
                        output_lines.append(add_index_stmt)
                elif index_upper.startswith('KEY') or index_upper.startswith('INDEX'):
                    normal_match = re.search(r'(?:KEY|INDEX)\s+[`"]?(\w+)[`"]?\s*\(([^)]+)\)', index, re.IGNORECASE)
                    if normal_match:
                        index_name = normal_match.group(1)
                        add_index_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD {index} */;"
                        output_lines.append(add_index_stmt)
                elif index_upper.startswith('CONSTRAINT') or index_upper.startswith('FOREIGN KEY'):
                    # 外键约束
                    add_fk_stmt = f"/*!999999 ALTER TABLE `{tbl['name']}` ADD {index} */;"
                    output_lines.append(add_fk_stmt)
            
            output_lines.append("")

    output_lines.append("-- ****************************************************")
    output_lines.append("-- 脚本执行完成")
    output_lines.append("-- ****************************************************")

    # 写入文件
    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write("\n".join(output_lines))
        print(f"\n🎉 同步脚本已生成: {OUTPUT_FILE}")
        print("💡 使用三步策略：")
        print("   1. 先尝试添加字段（忽略已存在错误）")
        print("   2. 再修改字段定义（定义相同时自动跳过）")
        print("   3. 最后添加索引（忽略已存在错误）")
    except Exception as e:
        print(f"❌ 写入失败: {e}")

if __name__ == "__main__":
    main()