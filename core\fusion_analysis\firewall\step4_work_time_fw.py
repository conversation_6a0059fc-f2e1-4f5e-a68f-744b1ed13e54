import pandas as pd
import os
from step3_business_analysis_fw import main as get_business_analysis_data

def mark_work_time(df):
    """
    标记工作时间
    工作日（周一~周五）08:30–17:30为工作时间
    """
    print("正在标记工作时间...")
    
    # 确保time列是datetime类型
    if not pd.api.types.is_datetime64_any_dtype(df['time']):
        df['time'] = pd.to_datetime(df['time'], errors='coerce')
    
    # 判断是否为工作时间
    def is_work_time(time):
        if pd.isna(time):
            return False
        
        # 判断是否为工作日（周一至周五）
        is_weekday = time.dayofweek < 5  # 0-4 表示周一至周五
        
        # 判断是否在工作时间段（8:30-17:30）
        hour, minute = time.hour, time.minute
        is_work_hours = (hour > 6 or (hour == 6 and minute >= 30)) and (hour < 20 or (hour == 20 and minute <= 30))
        
        return is_weekday and is_work_hours
    
    df['is_work_time'] = df['time'].apply(is_work_time)
    
    # 统计工作时间和非工作时间的记录数
    work_time_counts = df['is_work_time'].value_counts()
    print("\n工作时间统计:")
    print(f"工作时间: {work_time_counts.get(True, 0)}条记录")
    print(f"非工作时间: {work_time_counts.get(False, 0)}条记录")
    
    return df

def main():
    # 从step3_business_analysis获取处理后的数据
    print("从step3_business_analysis获取处理后的数据...")
    df = get_business_analysis_data()
    
    if df is None or df.empty:
        print("错误: 未获取到任何数据")
        return
    
    # 检查必需列是否存在
    required_columns = ['time', 'id']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return
    
    # 处理时间列
    df['time'] = pd.to_datetime(df['time'], errors='coerce')
    # 丢弃时间为空的行
    df = df.dropna(subset=['time'])
    
    # 标记工作时间
    df = mark_work_time(df)
    
    # 保存结果
    # output_file = "step4_work_time.csv"
    # df.to_csv(output_file, index=False)
    # print(f"\n结果已保存至 {output_file}")

    return df

if __name__ == "__main__":
    main()