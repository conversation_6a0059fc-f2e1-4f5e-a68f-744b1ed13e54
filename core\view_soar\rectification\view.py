#!/usr/bin/env python
# encoding:utf-8

from . import *
import uuid
from core.model import Rectification, RescanRecord, OperationLog
from datetime import datetime, date
from flask import request
from flask import send_file, make_response
from flasgger import swag_from
import pandas as pd
import os
from werkzeug.utils import secure_filename
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_TAB_ALIGNMENT, WD_PARAGRAPH_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
from docx.enum.text import WD_LINE_SPACING
from bs4 import BeautifulSoup
import base64
from io import BytesIO



@r.route("/post/rectification/save", methods=['POST'])
def save_rectification():
    """
    新建或编辑整改单
    如果JSON体中提供了 rect_id，则为编辑模式；否则为新建模式。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_id:
              type: string
              description: "整改单ID。提供则为编辑，不提供则为新建。"
            message_id: { type: string, description: "消息ID" }
            serial_number: { type: string, description: "整改单编号" }
            vuln_name: { type: string, description: "隐患名称" }
            retest_status: { type: boolean, description: "复测状态" }
            source_unit: { type: string, description: "来源单位" }
            source_code: { type: string, description: "来源编号" }
            issue_date: { type: string, format: "date", description: "下发日期YYYY-MM-DD" }
            rect_type: { type: string, description: "整改单类型" }
            description: { type: string, description: "隐患描述" }
            vuln_type: { type: string, description: "隐患类型" }
            vuln_level: { type: string, description: "隐患级别" }
            vuln_count: { type: integer, description: "隐患个数" }
            ip: { type: string, description: "隐患IP" }
            responsible_dept: { type: string, description: "责任单位/部门" }
            is_responded: { type: boolean, description: "是否反馈" }
            response_date: { type: string, format: "date", description: "反馈日期YYYY-MM-DD" }
            is_fixed: { type: boolean, description: "是否整改" }
            fix_details: { type: string, description: "整改详情" }
            note: { type: string, description: "备注" }
            rect_deadline: { type: string, format: "date", description: "整改截止日期YYYY-MM-DD" }
            remind_time: { type: string, format: "date", description: "提醒日期YYYY-MM-DD" }
            inspection_content: { type: string, description: "督察内容" }
            inspection_overview: { type: string, description: "督察实施概况" }
            system_name: { type: string, description: "系统名称" }
            existing_hazard: { type: string, description: "存在隐患" }
            rectification_suggestion: { type: string, description: "整改建议" }
            rectification_time_requirement: { type: string, description: "整改时间要求" }
            inspection_executor: { type: string, description: "督察执行负责人" }
            rescan_situation: { type: string, description: "复查情况" }
            inspection_rescan_person: { type: string, description: "督察复查人员" }
    responses:
      200:
        description: 操作成功
      400:
        description: "请求参数错误或日期格式无效"
      404:
        description: "未找到要编辑的整改单"
      409:
        description: "数据冲突，如唯一键重复"
      500:
        description: "服务器内部错误"
    """
    data = request.json
    try:
        if not data:
            return Response.re(ErrMsg(400, "请求体不能为空"))
        
        rect_id = data.get('rect_id')
        now = datetime.now()

        # 处理日期字段 - 保持字符串格式，让ORM自动处理
        for date_field in ['issue_date', 'response_date', 'rect_deadline', 'remind_time']:
            if data.get(date_field) and data[date_field]:
                try:
                    # 验证日期格式，但不转换类型
                    datetime.strptime(data[date_field], '%Y-%m-%d')
                except (ValueError, TypeError):
                    return Response.re(ErrMsg(400, f"日期格式无效: {date_field}. 请使用YYYY-MM-DD 格式."))

        if rect_id:
            # --- 编辑模式 ---
            rect = Rectification.where('rect_id', rect_id).first()
            if not rect:
                return Response.re(ErrMsg(404, "未找到要更新的整改单"))

            # 逐一检查并更新字段
            if 'message_id' in data: rect.message_id = data.get('message_id')
            if 'serial_number' in data: rect.serial_number = data.get('serial_number')
            if 'source_unit' in data: rect.source_unit = data.get('source_unit')
            if 'source_code' in data: rect.source_code = data.get('source_code')
            if 'issue_date' in data: rect.issue_date = data.get('issue_date')
            if 'rect_type' in data: rect.rect_type = data.get('rect_type')
            if 'description' in data: rect.description = data.get('description')
            if 'vuln_name' in data: rect.vuln_name = data.get('vuln_name')            
            if 'vuln_type' in data: rect.vuln_type = data.get('vuln_type')
            if 'vuln_level' in data: rect.vuln_level = data.get('vuln_level')
            if 'vuln_count' in data: rect.vuln_count = data.get('vuln_count')
            if 'ip' in data: rect.ip = data.get('ip')
            if 'responsible_dept' in data: rect.responsible_dept = data.get('responsible_dept')
            if 'is_responded' in data: rect.is_responded = data.get('is_responded')
            if 'response_date' in data: rect.response_date = data.get('response_date') or None
            if 'is_fixed' in data: rect.is_fixed = data.get('is_fixed')
            if 'fix_details' in data: rect.fix_details = data.get('fix_details')
            if 'retest_status' in data: rect.retest_status = data.get('retest_status')
            if 'note' in data: rect.note = data.get('note')
            if 'rect_deadline' in data: rect.rect_deadline = data.get('rect_deadline') or None
            if 'remind_time' in data: rect.remind_time = data.get('remind_time') or None
            if 'inspection_content' in data: rect.inspection_content = data.get('inspection_content')
            if 'inspection_overview' in data: rect.inspection_overview = data.get('inspection_overview')
            if 'system_name' in data: rect.system_name = data.get('system_name')
            if 'existing_hazard' in data: rect.existing_hazard = data.get('existing_hazard')
            if 'rectification_suggestion' in data: rect.rectification_suggestion = data.get('rectification_suggestion')
            if 'rectification_time_requirement' in data: rect.rectification_time_requirement = data.get('rectification_time_requirement')
            if 'inspection_executor' in data: rect.inspection_executor = data.get('inspection_executor')
            if 'rescan_situation' in data: rect.rescan_situation = data.get('rescan_situation')
            if 'inspection_rescan_person' in data: rect.inspection_rescan_person = data.get('inspection_rescan_person')

            rect.update_time = now

            # 避免MassAssignmentError报错
            rect.save() 
            logger.info(f"已更新整改单: {rect_id}")
                        # 记录操作日志
            add_operation_log("系统用户", "编辑整改单", rect_id, f"更新整改单: {rect.serial_number}")

        else:
            # --- 新建模式 ---
            if 'serial_number' not in data or not data['serial_number']:
                 return Response.re(ErrMsg(400, "新建整改单时，必须提供'serial_number'字段。"))

            rect = Rectification()

            new_rect_id = str(uuid.uuid4())
            rect.rect_id = new_rect_id

            rect.create_time = now
            rect.update_time = now
            
            rect.message_id = data.get('message_id')
            rect.serial_number = data.get('serial_number')
            rect.source_unit = data.get('source_unit')
            rect.source_code = data.get('source_code')
            rect.issue_date = data.get('issue_date')
            rect.rect_type = data.get('rect_type')
            rect.description = data.get('description')
            rect.vuln_name = data.get('vuln_name')
            rect.vuln_type = data.get('vuln_type')
            rect.vuln_level = data.get('vuln_level')
            rect.vuln_count = data.get('vuln_count')
            rect.ip = data.get('ip')
            rect.responsible_dept = data.get('responsible_dept')
            rect.is_responded = data.get('is_responded', False)
            rect.response_date = data.get('response_date') or None
            rect.is_fixed = data.get('is_fixed', False)
            rect.fix_details = data.get('fix_details')
            rect.retest_status = data.get('retest_status', False)
            rect.note = data.get('note')
            rect.rect_deadline = data.get('rect_deadline') or None
            rect.remind_time = data.get('remind_time') or None
            rect.inspection_content = data.get('inspection_content')
            rect.inspection_overview = data.get('inspection_overview')
            rect.system_name = data.get('system_name')
            rect.existing_hazard = data.get('existing_hazard')
            rect.rectification_suggestion = data.get('rectification_suggestion')
            rect.rectification_time_requirement = data.get('rectification_time_requirement')
            rect.inspection_executor = data.get('inspection_executor')
            rect.rescan_situation = data.get('rescan_situation')
            rect.inspection_rescan_person = data.get('inspection_rescan_person')

            rect.save()
            rect_id = new_rect_id
            logger.info(f"已创建新整改单: {rect_id}")
                        # 记录操作日志
            add_operation_log("系统用户", "新建整改单", rect_id, f"创建整改单: {rect.serial_number}")

            
        return Response.re(data={"rect_id": rect_id})

    except Exception as e:
        error_str = str(e).lower()
        if ('duplicate' in error_str or 'unique' in error_str):
            return Response.re(ErrMsg(409, f"操作失败：数据冲突，可能存在唯一键重复（如整改单编号 '{data.get('serial_number')}'）。"))
        
        logger.error(f"保存整改单失败: {repr(e)}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {repr(e)}"))

@r.route("/post/rectification/import", methods=['POST'])
def import_rectifications_from_excel():
    """
    通过Excel文件批量导入整改单
    ---
    tags:
      - 整改单管理
    consumes:
      - multipart/form-data
    parameters:
      - name: file
        in: formData
        type: file
        required: true
        description: "包含整改单信息的Excel文件 (.xlsx, .xls)"
    responses:
      200:
        description: "导入成功"
        schema:
          type: object
          properties:
            imported_count: { type: integer, description: "成功导入的记录数" }
            failed_count: { type: integer, description: "失败的记录数" }
            total_rows: { type: integer, description: "总行数" }
            import_batch_id: { type: string, description: "本次导入的批次ID" }
            errors:
              type: array
              items:
                type: object
                properties:
                  row_index: { type: integer }
                  error: { type: string }
      400:
        description: "请求错误，如未上传文件或文件格式不受支持"
      500:
        description: "服务器内部错误"
    """
    if 'file' not in request.files:
        return Response.re(ErrMsg(400, "未找到上传的文件部分"))

    file = request.files['file']
    if file.filename == '':
        return Response.re(ErrMsg(400, "未选择任何文件"))

    # 验证文件类型
    allowed_extensions = {'xlsx', 'xls'}
    if '.' not in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
        return Response.re(ErrMsg(400, "文件类型不支持，请上传 .xlsx 或 .xls 文件"))

    try:
        filename = secure_filename(file.filename)
        df = pd.read_excel(file)

        # 定义Excel列标题到数据库字段的映射
        header_map = {
            "整改单编号": "serial_number","来源单位": "source_unit", "来源编号": "source_code", 
            "下发时间": "issue_date","整改单类型": "rect_type", "隐患描述": "description", 
            "隐患名称": "vuln_name", "隐患类型": "vuln_type","隐患级别": "vuln_level", 
            "隐患个数": "vuln_count", "隐患IP": "ip", "责任单位/部门": "responsible_dept", 
            "是否反馈": "is_responded", "反馈时间": "response_date", 
            "是否整改": "is_fixed", "整改详情": "fix_details", "复测状态": "retest_status","备注": "note", 
            "整改截止时间": "rect_deadline", "提醒时间": "remind_time",
            "督察内容": "inspection_content", "督察实施概况": "inspection_overview", "系统名称": "system_name",
            "存在隐患": "existing_hazard", "整改建议": "rectification_suggestion", "整改时间要求": "rectification_time_requirement",
            "督察执行负责人": "inspection_executor", "复查情况": "rescan_situation", "督察复查人员": "inspection_rescan_person"
        }
        
        # 只处理映射中存在的列
        df.rename(columns=header_map, inplace=True)
        
        imported_count = 0
        failed_count = 0
        errors = []
        import_batch_id = str(uuid.uuid4())
        now = datetime.now()

        for index, row in df.iterrows():
            row_index = index + 2  # Excel行号从2开始（1是表头）
            
            try:
                # 检查必需字段
                if 'serial_number' not in row or pd.isna(row['serial_number']):
                    raise ValueError("缺少必需的“整改单编号”")

                rect = Rectification()
                rect.rect_id = str(uuid.uuid4())
                rect.create_time = now
                rect.update_time = now
                
                # 导入元数据
                rect.is_excel_imported = True
                rect.excel_filename = filename
                rect.excel_row_index = row_index
                rect.import_batch_id = import_batch_id

                # 逐一赋值
                for col_name, field_name in header_map.items():
                    # 检查DataFrame中是否存在该列
                    if field_name in row and pd.notna(row[field_name]):
                        value = row[field_name]
                        # 日期和布尔值转换
                        if field_name in ['issue_date', 'response_date', 'rect_deadline', 'remind_time']:
                            if isinstance(value, datetime):
                                value = value.strftime('%Y-%m-%d')  # 转换为字符串格式
                            elif isinstance(value, str):
                                # 如果是字符串，验证格式但不转换类型
                                try:
                                    datetime.strptime(str(value).split(' ')[0], '%Y-%m-%d')
                                except ValueError:
                                    logger.warning(f"第 {row_index} 行 {field_name} 日期格式无效: {value}")
                                    value = None
                            else:
                                # 其他类型（如pandas Timestamp）转换为字符串
                                try:
                                    value = value.strftime('%Y-%m-%d')
                                except:
                                    value = None
                        elif field_name in ['is_responded', 'is_fixed', 'retest_status']: # 新增字段适配
                            value = str(value).lower() in ['true', '1', '是', 'yes']

                        setattr(rect, field_name, value)

                rect.save()
                imported_count += 1

            except Exception as row_error:
                failed_count += 1
                errors.append({"row_index": row_index, "error": repr(row_error)})
                logger.warning(f"导入Excel第 {row_index} 行失败: {repr(row_error)}")

        return Response.re(data={
            "imported_count": imported_count,
            "failed_count": failed_count,
            "total_rows": len(df),
            "import_batch_id": import_batch_id,
            "errors": errors
        })

    except Exception as e:
        logger.error(f"处理Excel文件失败: {repr(e)}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {repr(e)}"))

@r.route("/post/rectification/delete", methods=['POST'])
def batch_delete_rectification():
    """
    批量删除整改单
    根据提供的ID列表，删除一个或多个整改单记录。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要删除的整改单ID列表"
    responses:
      200:
        description: "删除成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))

        # 删除w5_rescan_record表关联记录
        RescanRecord.where_in('rect_id', rect_ids).delete()
        
        # 删除w5_rectification表记录
        deleted_count = Rectification.where_in('rect_id', rect_ids).delete()

        logger.info(f"成功批量删除 {deleted_count} 条整改单及其关联记录。")
        # 记录操作日志
        for rect_id in rect_ids:
            add_operation_log("系统用户", "删除整改单", rect_id, "批量删除整改单")

        return Response.re(data={"deleted_count": deleted_count})

    except Exception as e:
        logger.error(f"批量删除整改单失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))


@r.route("/post/rectification/fix", methods=['POST'])
def batch_update_fix_status():
    """
    批量更新整改状态
    根据ID列表，批量修改整改单的 '是否已整改' (is_fixed) 状态。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要更新的整改单ID列表"
            is_fixed:
              type: boolean
              description: "目标整改状态 (true 或 false)"
    responses:
      200:
        description: "更新成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')
        is_fixed = data.get('is_fixed')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))
        if not isinstance(is_fixed, bool):
            return Response.re(ErrMsg(400, "参数 is_fixed 必须是布尔值 (true/false)"))

        update_data = {
            "is_fixed": is_fixed,
            "update_time": datetime.now()
        }
        
        updated_count = Rectification.where_in('rect_id', rect_ids).update(update_data)
        
        logger.info(f"成功将 {updated_count} 条整改单的整改状态更新为 {is_fixed}")
                # 记录操作日志
        for rect_id in rect_ids:
            status_text = "已整改" if is_fixed else "未整改"
            add_operation_log("系统用户", "更新整改状态", rect_id, f"整改状态更新为: {status_text}")

        return Response.re(data={"updated_count": updated_count})

    except Exception as e:
        logger.error(f"批量更新整改状态失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))

@r.route("/post/rectification/rescan", methods=['POST'])
def batch_update_rescan_status():
    """
    批量更新复查状态
    根据ID列表，批量修改整改单的 '是否已反馈' (is_responded) 状态，并更新 '上次复测时间'。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要更新的整改单ID列表"
            is_responded:
              type: boolean
              description: "目标复查/反馈状态 (true 或 false)"
    responses:
      200:
        description: "更新成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')
        is_responded = data.get('is_responded')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))
        if not isinstance(is_responded, bool):
            return Response.re(ErrMsg(400, "参数 is_responded 必须是布尔值 (true/false)"))

        now = datetime.now()
        update_data = {
            "is_responded": is_responded,
            "last_rescan_time": now.strftime('%Y-%m-%d'), # 更新上次复测时间
            "update_time": now
        }
        
        updated_count = Rectification.where_in('rect_id', rect_ids).update(update_data)
        
        logger.info(f"成功将 {updated_count} 条整改单的复查状态更新为 {is_responded}")
                # 记录操作日志
        for rect_id in rect_ids:
            status_text = "已反馈" if is_responded else "未反馈"
            add_operation_log("系统用户", "更新复查状态", rect_id, f"复查状态更新为: {status_text}")

        return Response.re(data={"updated_count": updated_count})

    except Exception as e:
        logger.error(f"批量更新复查状态失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))


@r.route("/post/rectification/list", methods=['POST'])
def post_rectification_list():
    """
    查询整改单列表
    支持分页和多条件筛选查询。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: false
        schema:
          type: object
          properties:
            page: { type: integer, default: 1, description: "页码" }
            page_size: { type: integer, default: 10, description: "每页数量" }
            vuln_name: { type: string, description: "按隐患名称模糊查询" }
            retest_status: { type: boolean, description: "按复测状态筛选" }
            rect_type: { type: string, description: "按整改单类型筛选" }
            vuln_level: { type: string, description: "按隐患级别筛选" }
            ip: { type: string, description: "按IP地址模糊查询" }
            responsible_dept: { type: string, description: "按责任单位/部门模糊查询" }
            is_fixed: { type: boolean, description: "按是否整改筛选" }
            is_responded: { type: boolean, description: "按是否反馈/复查筛选" }
            inspection_content: { type: string, description: "按督察内容模糊查询" }
            inspection_overview: { type: string, description: "按督察实施概况模糊查询" }
            system_name: { type: string, description: "按系统名称模糊查询" }
            inspection_executor: { type: string, description: "按督察执行负责人模糊查询" }
            inspection_rescan_person: { type: string, description: "按督察复查人员模糊查询" }
    responses:
      200:
        description: "成功返回整改单列表"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json or {}
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))
        
        query = Rectification.select()
        
        # 字符串模糊匹配筛选
        for field in ['rect_type', 'vuln_level', 'ip', 'responsible_dept', 'vuln_name', 'inspection_content', 'inspection_overview', 'system_name', 'inspection_executor', 'inspection_rescan_person']:
            value = data.get(field)
            if value:
                query = query.where(field, 'like', f'%{value}%')
        
        # 布尔值筛选
        for field in ['is_fixed', 'is_responded', 'retest_status']:
            value = data.get(field)
            if value is not None:
                query = query.where(field, value)

        total_count = query.count()
        total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0
        
        records = query.order_by('create_time', 'desc').offset((page - 1) * page_size).limit(page_size).get()
        
        result_list = []
        for rec in records:
            rec_data = {
                'rect_id': rec.rect_id,
                'serial_number': rec.serial_number,
                'source_unit': rec.source_unit,
                'source_code': rec.source_code,
                'issue_date': rec.issue_date.strftime('%Y-%m-%d') if rec.issue_date else None,
                'rect_type': rec.rect_type,
                'description': rec.description,
                'vuln_name': rec.vuln_name,
                'vuln_type': rec.vuln_type,
                'vuln_level': rec.vuln_level,
                'vuln_count': rec.vuln_count,
                'ip': rec.ip,
                'responsible_dept': rec.responsible_dept,
                'is_responded': rec.is_responded,
                'response_date': rec.response_date.strftime('%Y-%m-%d') if rec.response_date else None,
                'is_fixed': rec.is_fixed,
                'fix_details': rec.fix_details,
                'retest_status': rec.retest_status,
                'note': rec.note,
                'rect_deadline': rec.rect_deadline.strftime('%Y-%m-%d') if rec.rect_deadline else None,
                'remind_time': rec.remind_time.strftime('%Y-%m-%d') if rec.remind_time else None,
                'last_rescan_time': rec.last_rescan_time.strftime('%Y-%m-%d') if rec.last_rescan_time else None,
                'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
                'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None,
                'inspection_content': rec.inspection_content,
                'inspection_overview': rec.inspection_overview,
                'system_name': rec.system_name,
                'existing_hazard': rec.existing_hazard,
                'rectification_suggestion': rec.rectification_suggestion,
                'rectification_time_requirement': rec.rectification_time_requirement,
                'inspection_executor': rec.inspection_executor,
                'rescan_situation': rec.rescan_situation,
                'inspection_rescan_person': rec.inspection_rescan_person
            }
            result_list.append(rec_data)

        response_data = {
            'list': result_list,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        }
        return Response.re(data=response_data)

    except Exception as e:
        logger.error(f"查询整改单列表失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))
    
@r.route("/post/rectification/detail", methods=['POST'])
def post_rectification_detail():
    """
    查询单个整改单详情
    根据整改单ID获取详细信息。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_id:
              type: string
              required: true
              description: "整改单ID"
    responses:
      200:
        description: "成功返回整改单详情"
      400:
        description: "请求参数错误"
      404:
        description: "整改单不存在"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json or {}
        rect_id = data.get('rect_id')
        message_id = data.get('message_id')
        
        # 校验必要参数
        if not rect_id and not message_id:
            return Response.re(ErrMsg(400, "缺少整改单ID参数或者消息ID参数"))
        
        # 查询数据库
        if message_id:
            rec = Rectification.where('message_id', message_id).first()
        else:
            rec = Rectification.where('rect_id', rect_id).first()
        if not rec:
            return Response.re(ErrMsg(404, "整改单不存在"))
        
        # 构建响应数据
        rec_data = {
            'rect_id': rec.rect_id,
            'message_id': rec.message_id,
            'serial_number': rec.serial_number,
            'source_unit': rec.source_unit,
            'source_code': rec.source_code,
            'issue_date': rec.issue_date.strftime('%Y-%m-%d') if rec.issue_date else None,
            'rect_type': rec.rect_type,
            'description': rec.description,
            'vuln_name': rec.vuln_name,
            'vuln_type': rec.vuln_type,
            'vuln_level': rec.vuln_level,
            'vuln_count': rec.vuln_count,
            'ip': rec.ip,
            'responsible_dept': rec.responsible_dept,
            'is_responded': rec.is_responded,
            'response_date': rec.response_date.strftime('%Y-%m-%d') if rec.response_date else None,
            'is_fixed': rec.is_fixed,
            'fix_details': rec.fix_details,
            'retest_status': rec.retest_status,
            'note': rec.note,
            'rect_deadline': rec.rect_deadline.strftime('%Y-%m-%d') if rec.rect_deadline else None,
            'remind_time': rec.remind_time.strftime('%Y-%m-%d') if rec.remind_time else None,
            'last_rescan_time': rec.last_rescan_time.strftime('%Y-%m-%d') if rec.last_rescan_time else None,
            'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
            'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None,
            'inspection_content': rec.inspection_content,
            'inspection_overview': rec.inspection_overview,
            'system_name': rec.system_name,
            'existing_hazard': rec.existing_hazard,
            'rectification_suggestion': rec.rectification_suggestion,
            'rectification_time_requirement': rec.rectification_time_requirement,
            'inspection_executor': rec.inspection_executor,
            'rescan_situation': rec.rescan_situation,
            'inspection_rescan_person': rec.inspection_rescan_person
        }
        
        return Response.re(data=rec_data)

    except Exception as e:
        logger.error(f"查询整改单详情失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))




@r.route("/post/rectification/draft", methods=['POST'])
def post_rectification_draft():
    """
    编制整改单草稿
    - 必须提供 rect_id 或 message_id
    - 若 ID 不存在（查无数据），返回空 data
    - 若服务异常（DB、代码等），抛出 500
    """
    try:
        # 解析请求体
        data = request.json or {}
        rect_id = data.get('rect_id')
        message_id = data.get('message_id')

        # 🔴 校验：至少提供一个 ID 参数
        if not rect_id and not message_id:
            return Response.re(ErrMsg(400, "缺少必要参数：请提供 rect_id 或 message_id"))

        # 查询数据库
        rec = None
        if message_id:
            rec = Rectification.where('message_id', message_id).first()
        elif rect_id:
            rec = Rectification.where('rect_id', rect_id).first()

        # 🟡 查不到记录？正常情况，返回空 data
        if not rec:
            return Response.re(data={})  # HTTP 200 OK

        # ✅ 查到记录：构建响应数据
        rec_data = {
            'rect_id': rec.rect_id,
            'message_id': rec.message_id,
            'serial_number': rec.serial_number,
            'source_unit': rec.source_unit,
            'source_code': rec.source_code,
            'issue_date': rec.issue_date.strftime('%Y-%m-%d') if rec.issue_date else None,
            'rect_type': rec.rect_type,
            'description': rec.description,
            'vuln_name': rec.vuln_name,
            'vuln_type': rec.vuln_type,
            'vuln_level': rec.vuln_level,
            'vuln_count': rec.vuln_count,
            'ip': rec.ip,
            'responsible_dept': rec.responsible_dept,
            'is_responded': rec.is_responded,
            'response_date': rec.response_date.strftime('%Y-%m-%d') if rec.response_date else None,
            'is_fixed': rec.is_fixed,
            'fix_details': rec.fix_details,
            'retest_status': rec.retest_status,
            'note': rec.note,
            'rect_deadline': rec.rect_deadline.strftime('%Y-%m-%d') if rec.rect_deadline else None,
            'remind_time': rec.remind_time.strftime('%Y-%m-%d') if rec.remind_time else None,
            'last_rescan_time': rec.last_rescan_time.strftime('%Y-%m-%d') if rec.last_rescan_time else None,
            'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
            'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None,
            'inspection_content': rec.inspection_content,
            'inspection_overview': rec.inspection_overview,
            'system_name': rec.system_name,
            'existing_hazard': rec.existing_hazard,
            'rectification_suggestion': rec.rectification_suggestion,
            'rectification_time_requirement': rec.rectification_time_requirement,
            'inspection_executor': rec.inspection_executor,
            'rescan_situation': rec.rescan_situation,
            'inspection_rescan_person': rec.inspection_rescan_person
        }

        return Response.re(data=rec_data)

    except Exception as e:
        
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))



@r.route("/post/rectification/export", methods=['POST'])
def post_rectification_export():
    data = request.json or {}
    rect_id = data.get('rect_id')
  
    # 校验必要参数
    if not rect_id:
        return Response.re(ErrMsg(400, "缺少整改单ID参数"))
    
    # 查询数据库
    rec = Rectification.where('rect_id', rect_id).first()
    if not rec:
        return Response.re(ErrMsg(404, "整改单不存在"))
    
    return Response.re(data=f'/api/v1/soar/get/rectification/export?rect_id={rect_id}')
        






def set_run_font(run, font_name='楷体_GB2312', size=Pt(12), bold=False):
    run.font.name = font_name
    # 🔥 关键：必须设置 eastAsia 字体，否则中文无效
    run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
    run.font.size = size
    run.bold = bold


def set_document_defaults(doc, font_name='楷体_GB2312', font_size=Pt(12), line_spacing=1.5):
    """
    设置文档默认样式：字体、字号、行间距
    """
    style = doc.styles['Normal']
    
    # 设置字体
    style.font.name = font_name
    style._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
    style.font.size = font_size
    style.font.ascii_font = 'KaiTi'  # 或 'SimSun'

    # 设置段落行间距为 1.5 倍
    paragraph_format = style.paragraph_format
    paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    paragraph_format.line_spacing = line_spacing  # float 值，如 1.5

    # 可选：段前段后间距为 0
    paragraph_format.space_before = Pt(0)
    paragraph_format.space_after = Pt(0)

def set_table_border(table, border_size=4, color="auto", space=0):
    """
    设置表格所有边框为实线
    :param table: docx table 对象
    :param border_size: 边框粗细，单位为 twip（1 pt = 20 twip），4 twip ≈ 0.2 pt（细线）
    :param color: 颜色，"auto" 表示自动（黑色）
    :param space: 边框与文字间距
    """
    tbl = table._tbl  # 获取底层 XML 表格对象

    # 创建边框元素
    tblBorders = OxmlElement('w:tblBorders')

    for border_name in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
        border = OxmlElement(f'w:{border_name}')
        border.set(qn('w:val'), 'single')      # 实线
        border.set(qn('w:sz'), str(border_size))  # 宽度（twip）
        border.set(qn('w:space'), str(space))    # 间距
        border.set(qn('w:color'), color)        # 颜色
        tblBorders.append(border)

    tblPr = tbl.tblPr  # 表格属性
    tblPr.append(tblBorders)

def add_html_with_base64_images(cell, html_content):
    """
    将包含 base64 图片的 HTML 添加到 docx 单元格
    """
    if not html_content:
        cell.add_paragraph()
        return

    soup = BeautifulSoup(html_content, 'html.parser')
    p = cell.add_paragraph()

    for elem in soup.descendants:
        # 处理文本
        if isinstance(elem, str) and elem.parent.name not in ['script', 'style']:
            text = str(elem).strip()
            if text:
                p.add_run(text)

        # 处理换行
        elif elem.name == 'br':
            p.add_run().add_break()

        # 处理图片
        elif elem.name == 'img':
            src = elem.get('src', '')
            if src.startswith('data:image'):
                try:
                    header, encoded = src.split(',', 1)
                    data = base64.b64decode(encoded)
                    image_stream = BytesIO(data)

                    if p.text.strip():
                        p = cell.add_paragraph()

                    pic_p = cell.add_paragraph()
                    pic_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    run_pic = pic_p.add_run()
                    run_pic.add_picture(image_stream, width=Cm(10.3))

                    cell.add_paragraph()
                    p = cell.add_paragraph()

                except Exception as e:
                    err_p = cell.add_paragraph()
                    err_p.add_run(f"[图片解析失败: {str(e)}]").italic = True

@r.route("/get/rectification/export", methods=['GET'])
def get_rectification_export():
    try:
        rect_id = request.args.get('rect_id', '').strip()

        if not rect_id:
            return Response.re(ErrMsg(400, "缺少整改单ID参数"))

        rec = Rectification.where('rect_id', rect_id).first()
        if not rec:
            return Response.re(ErrMsg(404, "整改单不存在"))

        doc = Document()
        set_document_defaults(doc, '楷体_GB2312', Pt(12))  # 👈 全局默认楷体
        sec = doc.sections[0]
        sec.page_height = Cm(29.7)
        sec.page_width = Cm(21.0)
        sec.top_margin = Cm(2.5)
        sec.bottom_margin = Cm(2.5)
        sec.left_margin = Cm(2.5)
        sec.right_margin = Cm(2.5)

        # 标题
        title = doc.add_heading('信息安全技术督查整改通知单', level=0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = title.runs[0]
        run.font.name = '黑体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(22)

        # ================== 顶部信息：使用制表符实现两端对齐 ==================
        # 固定督查单位
        SUPERVISION_DEPT = "数字化部、信通分公司"

        # 第一行：督查单位 + 编号（两端对齐）
        p1 = doc.add_paragraph()
        p1.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY  # 两端对齐

        # 设置段落左右缩进为0，确保贴边
        p1.paragraph_format.left_indent = Cm(0)
        p1.paragraph_format.right_indent = Cm(0)

        # 添加制表符位置：右侧对齐（距离右边界 0 厘米）
        p1.paragraph_format.tab_stops.add_tab_stop(Cm(15.5), WD_TAB_ALIGNMENT.RIGHT)  # 调整 15.5 适应你的页面

        # 添加内容
        run1 = p1.add_run(f"督查单位/部门：{SUPERVISION_DEPT}")
        run1.bold = True
        run1.font.name = '楷体_GB2312'
        run1._element.rPr.rFonts.set(qn('w:eastAsia'), '楷体_GB2312')
        run1.font.size = Pt(12)

        # 插入制表符
        p1.add_run().add_tab()

        # 添加编号
        serial_number = rec.serial_number or ""
        run2 = p1.add_run(f"编号：{serial_number}号")
        run2.bold = True
        run2.font.name = '楷体_GB2312'
        run2._element.rPr.rFonts.set(qn('w:eastAsia'), '楷体_GB2312')
        run2.font.size = Pt(12)

        # 第二行：接收单位 + 日期
        p2 = doc.add_paragraph()
        p2.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        p2.paragraph_format.left_indent = Cm(0)
        p2.paragraph_format.right_indent = Cm(0)
        p2.paragraph_format.tab_stops.add_tab_stop(Cm(15.5), WD_TAB_ALIGNMENT.RIGHT)

        _responsible_dept = rec.responsible_dept or ''
        run3 = p2.add_run(f"接收单位/部门：{_responsible_dept}")
        run3.bold = True
        run3.font.name = '楷体_GB2312'
        run3._element.rPr.rFonts.set(qn('w:eastAsia'), '楷体_GB2312')
        run3.font.size = Pt(12)

        p2.add_run().add_tab()

        issue_date = rec.issue_date.strftime("%Y年%m月%d日") if rec.issue_date else ""
        run4 = p2.add_run(f"{issue_date}")
        run4.bold = True
        run4.font.name = '楷体_GB2312'
        run4._element.rPr.rFonts.set(qn('w:eastAsia'), '楷体_GB2312')
        run4.font.size = Pt(12)


        tbl = doc.add_table(rows=8, cols=3)
        tbl.autofit = False
        tbl.allow_autofit = False
        # 设置列宽，总和为 16.0 cm
        tbl.columns[0].width = Cm(5.5)   
        tbl.columns[1].width = Cm(5)   
        tbl.columns[2].width = Cm(5.5)   


        # 合并单元格
        def merge_cells(table, start_row, start_col, end_row, end_col):
            cell_1 = table.cell(start_row, start_col)
            cell_2 = table.cell(end_row, end_col)
            return cell_1.merge(cell_2)

        merge_cells(tbl, 0, 0, 2, 0)  # 督查基本情况
        merge_cells(tbl, 3, 1, 3, 2)  # 发现隐患情况
        merge_cells(tbl, 4, 1, 4, 2)  # 消缺整改建议
        merge_cells(tbl, 5, 1, 5, 2)  # 整改时间要求
        merge_cells(tbl, 6, 0, 6, 2)  # 督查执行负责人
        merge_cells(tbl, 7, 0, 7, 1)  # 整改情况

        # 填充内容
        # 第0~2行
        tbl.cell(0, 0).text = '督查基本情况'
        tbl.cell(0, 1).text = '督查时间'

        _issue_date = rec.issue_date or ''
        tbl.cell(0, 2).text = f"{_issue_date}"

        tbl.cell(1, 1).text = '督查内容'
        tbl.cell(1, 2).text = rec.inspection_content or '信息内网风险督查'

        tbl.cell(2, 1).text = '督查实施概况'
        tbl.cell(2, 2).text = rec.inspection_overview or '内网检查'

        # 第3行：发现隐患情况
        tbl.cell(3, 0).text = '发现隐患情况'
        cell_hazard = tbl.cell(3, 1)
        cell_hazard.text = ''
        p = cell_hazard.paragraphs[0] if cell_hazard.paragraphs else cell_hazard.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT

        existing_hazard = getattr(rec, 'existing_hazard', '') or getattr(rec, 'vuln_type', '')

        # 先处理前面的纯文本字段
        for label, value in [
            ("系统名称：", getattr(rec, 'system_name', '')),
            ("隐患ip：", getattr(rec, 'ip', '')),
            ("存在隐患：", existing_hazard),
            ("隐患级别：", getattr(rec, 'vuln_level', ''))
        ]:
            run = p.add_run(label)
            set_run_font(run, bold=True)
            run = p.add_run(f"{value}\n")
            set_run_font(run, bold=False)

        # 单独处理“隐患详情”（图文）
        run = p.add_run("隐患详情：")
        set_run_font(run, bold=True)

        desc = getattr(rec, 'description', '') or ''
        add_html_with_base64_images(cell_hazard, desc)  # 调用新函数

        cell_hazard.vertical_alignment = WD_ALIGN_VERTICAL.TOP

        # 第4行：消缺整改建议
        tbl.cell(4, 0).text = '消缺整改建议'
        cell_suggestion = tbl.cell(4, 1)
        cell_suggestion.text = rec.rectification_suggestion or ''
        p = cell_suggestion.paragraphs[0]
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        for run in p.runs:
            set_run_font(run)
        cell_suggestion.vertical_alignment = WD_ALIGN_VERTICAL.TOP

        # 第5行：整改时间要求
        tbl.cell(5, 0).text = '整改时间要求'
        cell_requirement = tbl.cell(5, 1)
        cell_requirement.text = rec.rectification_time_requirement or ''
        p = cell_requirement.paragraphs[0]
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        for run in p.runs:
            set_run_font(run)
        cell_requirement.vertical_alignment = WD_ALIGN_VERTICAL.TOP

        # 第6行：督查执行负责人
        executor = rec.inspection_executor or '黄梦琦-82526013/18995531955'
        tbl.cell(6, 0).text = f'督查执行负责人：{executor}\n督查管理部门盖章：\n（公章）'
        p = tbl.cell(6, 0).paragraphs[0]
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        for run in p.runs:
            set_run_font(run)
        tbl.cell(6, 0).vertical_alignment = WD_ALIGN_VERTICAL.TOP

        # 第7行左侧：整改情况
        cell_7_0 = tbl.cell(7, 0)
        cell_7_0.text = ''  # 清空

        # 段落1：整改情况：
        p1 = cell_7_0.add_paragraph()
        p1.add_run("整改情况：")
        p1.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # 段落2：提示文字（五号 = 10.5 磅）
        p2 = cell_7_0.add_paragraph()
        run_hint1 = p2.add_run("（接收单位在此处填写，并盖章）\n")
        run_hint1.font.size = Pt(10.5)  # 五号字
        # 可选：斜体或灰色，更像提示
        # run_hint1.italic = True

        # 段落3：盖章信息
        p3 = cell_7_0.add_paragraph()
        p3.add_run("信息安全责任单位（部门）盖章：")

        # 段落4：公章
        p4 = cell_7_0.add_paragraph()
        p4.add_run("（公章）")

        # 所有段落使用默认字体（楷体），无需额外设置
        cell_7_0.vertical_alignment = WD_ALIGN_VERTICAL.TOP

        # 第7行右侧：复查情况
        cell_7_2 = tbl.cell(7, 2)
        cell_7_2.text = ''  # 清空

        # 段落1：复查情况：
        p1 = cell_7_2.add_paragraph()
        p1.add_run("复查情况：")
        p1.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # 段落2：提示文字或实际内容（提示用五号）
        p2 = cell_7_2.add_paragraph()
        rescan_info = rec.rescan_situation or '（督查单位在此处填写复查时间、整改完成情况）'

        run_info = p2.add_run(rescan_info)
        if not rec.rescan_situation:  # 如果是默认提示文字，则设为五号
            run_info.font.size = Pt(10.5)
        # 否则使用默认字号（12 磅）

        # 段落3：督查复查人员：
        p3 = cell_7_2.add_paragraph()
        p3.add_run("\n督查复查人员：")

        # 段落4：人员姓名
        p4 = cell_7_2.add_paragraph()
        rescan_person = rec.inspection_rescan_person or ''
        p4.add_run(rescan_person)

        cell_7_2.vertical_alignment = WD_ALIGN_VERTICAL.TOP

        set_table_border(tbl, border_size=4)  # 细实线

        # 生成文件名
        filename = f'信息安全技术督查整改通知单{rec.serial_number}号（{rec.responsible_dept}）.docx'
        temp_file_path = f'/tmp/{filename}'
        doc.save(temp_file_path)

        try:
            # 记录操作日志
            add_operation_log("系统用户", "导出Word文档", rec.rect_id, f"导出整改单Word文档: {rec.serial_number}")
            
            # 读取文件内容并返回文件下载
            with open(temp_file_path, 'rb') as f:
                file_content = f.read()
            
            # 删除临时文件
            import os
            os.remove(temp_file_path)
            
            # 返回文件下载响应
            # from flask import send_file
            from io import BytesIO
            
            file_stream = BytesIO(file_content)
            file_stream.seek(0)

            response = make_response(send_file(
                file_stream,
                mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                as_attachment=True,
                attachment_filename=filename
            ))

            # 禁用缓存
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            
            return response
            
        except Exception as e:
            # 清理临时文件
            try:
                import os
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            except:
                pass
            raise e
    except Exception as e:
        logger.error(f"导出整改单失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))


@r.route("/post/operation_log/list", methods=['POST'])
def post_log_list():
    """
    查询操作日志列表
    支持分页和多条件筛选查询。
    ---
    tags:
      - 日志管理
    parameters:
      - name: body
        in: body
        required: false
        schema:
          type: object
          properties:
            page: { type: integer, default: 1, description: "页码" }
            page_size: { type: integer, default: 10, description: "每页数量" }
            operator: { type: string, description: "按操作人模糊查询" }
            operation_type: { type: string, description: "按操作类型筛选" }
            rect_id: { type: string, description: "按整改单编号筛选" }
            start_time: { type: string, format: "datetime", description: "操作时间开始 YYYY-MM-DD HH:MM:SS" }
            end_time: { type: string, format: "datetime", description: "操作时间结束 YYYY-MM-DD HH:MM:SS" }
    responses:
      200:
        description: "成功返回日志列表"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json or {}
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))

        query = OperationLog.select()

        # 字符串模糊匹配筛选
        for field in ['operator', 'operation_type', 'rect_id']:
            value = data.get(field)
            if value:
                query = query.where(field, 'like', f'%{value}%')

        # 时间范围筛选
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        if start_time:
            try:
                start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                query = query.where('operation_time', '>=', start_datetime)
            except ValueError:
                pass
        if end_time:
            try:
                end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                query = query.where('operation_time', '<=', end_datetime)
            except ValueError:
                pass

        total_count = query.count()
        total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0

        records = query.order_by('operation_time', 'desc').offset((page - 1) * page_size).limit(page_size).get()

        result_list = []
        for rec in records:
            rec_data = {
                'log_id': rec.log_id,
                'operator': rec.operator,
                'operation_type': rec.operation_type,
                'rect_id': rec.rect_id,
                'operation_time': rec.operation_time.strftime('%Y-%m-%d %H:%M:%S') if rec.operation_time else None,
                'operation_details': rec.operation_details,
                'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
                'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None
            }
            result_list.append(rec_data)

        response_data = {
            'list': result_list,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        }
        return Response.re(data=response_data)

    except Exception as e:
        logger.error(f"查询日志列表失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))

def add_operation_log(operator, operation_type, rect_id=None, operation_details=None):
    """
    添加操作日志记录
    :param operator: 操作人
    :param operation_type: 操作类型
    :param rect_id: 整改单编号（可选）
    :param operation_details: 操作详情（可选）
    """
    try:
        log = OperationLog()
        log.log_id = str(uuid.uuid4())
        log.operator = operator
        log.operation_type = operation_type
        log.rect_id = rect_id
        log.operation_time = datetime.now()
        log.operation_details = operation_details
        log.create_time = datetime.now()
        log.update_time = datetime.now()
        log.save()
        logger.info(f"已记录操作日志: {operator} - {operation_type} - {rect_id}")
    except Exception as e:
        logger.error(f"记录操作日志失败: {e}", exc_info=True)

@r.route("/post/operation_log/delete", methods=['POST'])
def post_operation_log_delete():
  """
  删除操作日志
  ---
  consumes:
    - application/json
  parameters:
    - in: body
      required: false
      schema:
        type: object
        properties:
          log_id: { type: string, description: "操作日志ID", required: true }
    responses:
      200:
        description: "成功删除操作日志"
      500:
        description: "服务器内部错误"
  """
  try:
    data = request.json or {}
    log_id = data.get('log_id')
    OperationLog.where('log_id', log_id).delete()
    return Response.re(data={"message": "操作日志删除成功"})
  except Exception as e:
    logger.error(f"操作日志删除失败: {e}", exc_info=True)
    return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))

@r.route("/post/operation_log/batch_delete", methods=['POST'])
def post_operation_log_batch_delete():
  """
  批量删除操作日志
  ---
  consumes:
    - application/json
  parameters:
    - in: body
      required: false
      schema:
        type: object
        properties:
          log_ids: { type: array, items: { type: string }, description: "操作日志ID列表", required: true }
    responses:
      200:
        description: "成功批量删除操作日志"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
  """
  try:
    data = request.json or {}
    log_ids = data.get('log_ids')

    if not isinstance(log_ids, list) or not log_ids:
        return Response.re(ErrMsg(400, "参数 log_ids 必须是一个非空的ID列表"))

    deleted_count = OperationLog.where_in('log_id', log_ids).delete()

    logger.info(f"成功批量删除 {deleted_count} 条操作日志")
    return Response.re(data={"deleted_count": deleted_count, "message": "批量删除操作日志成功"})
  except Exception as e:
    logger.error(f"批量删除操作日志失败: {e}", exc_info=True)
    return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))
