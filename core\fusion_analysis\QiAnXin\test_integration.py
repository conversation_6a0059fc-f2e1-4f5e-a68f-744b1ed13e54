#!/usr/bin/env python
# encoding:utf-8
"""
测试step2_flow_direction.py与step1_parse_security_logs.py的集成
"""

import pandas as pd
from step1_parse_security_logs import get_security_logs_from_db, convert_qianxin_to_enhanced
from step2_flow_direction import determine_flow_direction, normalize_time_format

def test_integration():
    """
    测试两个步骤的集成是否正常工作
    """
    print("=== 开始集成测试 ===")
    
    # 步骤1：从数据库获取原始数据
    print("1. 从数据库获取原始数据...")
    raw_data = get_security_logs_from_db()
    if raw_data.empty:
        print("❌ 无法从数据库获取数据")
        return False
    print(f"✅ 成功获取 {len(raw_data)} 条原始记录")
    
    # 步骤2：转换数据格式
    print("2. 转换数据格式...")
    df = convert_qianxin_to_enhanced(raw_data)
    if df is None or df.empty:
        print("❌ 数据转换失败")
        return False
    print(f"✅ 成功转换 {len(df)} 条记录")
    
    # 步骤3：检查必需列是否存在
    print("3. 检查数据结构...")
    required_columns = ['srcAddress', 'destAddress', 'time', 'id']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 缺少必需列: {missing_columns}")
        return False
    print("✅ 数据结构检查通过")
    
    # 步骤4：处理时间格式
    print("4. 处理时间格式...")
    df['time'] = df['time'].apply(normalize_time_format)
    df['hour_slot'] = df['time'].dt.strftime('%Y-%m-%d %H:00:00')
    print("✅ 时间格式处理完成")
    
    # 步骤5：判断流向
    print("5. 判断流向...")
    df = determine_flow_direction(df)
    print("✅ 流向判断完成")
    
    # 步骤6：验证结果
    print("6. 验证结果...")
    flow_counts = df['flow_direction'].value_counts()
    total_records = len(df)
    
    print(f"\n=== 测试结果 ===")
    print(f"总记录数: {total_records}")
    print(f"流向分布:")
    for direction, count in flow_counts.items():
        percentage = (count / total_records) * 100
        print(f"  {direction}: {count}条 ({percentage:.1f}%)")
    
    # 检查是否有未知流向
    if '未知' in flow_counts:
        print(f"⚠️  发现 {flow_counts['未知']} 条未知流向记录")
    
    print("\n✅ 集成测试完成！")
    return True

if __name__ == '__main__':
    test_integration()