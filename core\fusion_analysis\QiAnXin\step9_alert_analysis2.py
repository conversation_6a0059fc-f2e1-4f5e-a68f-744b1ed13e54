import pandas as pd
import numpy as np
import os
import ipaddress

# 导入step1和step8模块
import step1_parse_security_logs
import step8_risk_scoring

def analyze_alert_types(df):
    """
    分析告警类型，根据调整后的威胁分数和结论分配告警级别
    """
    print("正在分析告警类型...")
    
    # 初始化告警级别列
    df['alert_level'] = ''
    
    # 根据调整后的威胁分数和结论分配告警级别
    # 高级别告警
    high_risk_keywords = ['高危', '立即处理', '立即处置']
    df.loc[df['_adjusted_threat_score'] >= 75, 'alert_level'] = '高'
    df.loc[df['conclusion'].str.contains('|'.join(high_risk_keywords)), 'alert_level'] = '高'
    
    # 中级别告警
    medium_risk_keywords = ['中危', '需审核', '需关注', '优先']
    df.loc[(df['_adjusted_threat_score'] >= 50) & (df['_adjusted_threat_score'] < 75), 'alert_level'] = '中'
    df.loc[(df['alert_level'] == '') & (df['conclusion'].str.contains('|'.join(medium_risk_keywords))), 'alert_level'] = '中'
    
    # 低级别告警
    df.loc[df['alert_level'] == '', 'alert_level'] = '低'
    
    # 误报覆盖
    df.loc[df['conclusion'].str.contains('误报'), 'alert_level'] = '低'
    
    return df

def analyze_attack_results(df):
    """
    分析攻击结果，根据调整后的威胁分数、结论和流向判断攻击阶段
    """
    print("正在分析攻击结果...")
    
    # 初始化攻击结果列
    df['attackResult'] = ''
    
    # 失陷判断
    compromise_keywords = ['失陷', '数据外泄', '持续性外部入侵']
    df.loc[df['conclusion'].str.contains('|'.join(compromise_keywords)), 'attackResult'] = '失陷'
    df.loc[(df['_adjusted_threat_score'] >= 85), 'attackResult'] = '失陷'
    
    # 攻击成功判断
    success_keywords = ['攻击成功', '入侵', '高危']
    df.loc[(df['attackResult'] == '') & 
           (df['conclusion'].str.contains('|'.join(success_keywords))), 'attackResult'] = '成功'
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] >= 70), 'attackResult'] = '成功'
    
    # 企图判断
    attempt_keywords = ['探测', '扫描', '中危']
    df.loc[(df['attackResult'] == '') & 
           (df['conclusion'].str.contains('|'.join(attempt_keywords))), 'attackResult'] = '尝试'
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] >= 50), 'attackResult'] = '尝试'
    
    # 失败判断
    df.loc[(df['attackResult'] == '') & 
           (df['_adjusted_threat_score'] < 50), 'attackResult'] = '失败'
    
    # 误报覆盖
    df.loc[df['conclusion'].str.contains('误报'), 'attackResult'] = '误报'
    
    return df

def analyze_confidence(df):
    """
    分析置信度，根据多种因素评估告警的可信度
    """
    print("正在分析置信度...")
    
    # 初始化置信度列
    df['confidence'] = ''
    
    # 根据攻击结果判断置信度
    # 成功的攻击置信度为高
    df.loc[df['attackResult'] == '成功', 'confidence'] = '高'
    df.loc[df['attackResult'] == '失陷', 'confidence'] = '高'
    
    # 误报
    df.loc[df['attackResult'] == '误报', 'confidence'] = '低'
    
    # 尝试的攻击置信度为低或中（根据威胁分数决定）
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '尝试') & 
           (df['_adjusted_threat_score'] >= 60), 'confidence'] = '中'
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '尝试') & 
           (df['_adjusted_threat_score'] < 60), 'confidence'] = '低'
    
    # 失败的攻击置信度为低
    df.loc[(df['confidence'] == '') & (df['attackResult'] == '失败'), 'confidence'] = '低'
    
    # 根据结论中的关键词判断
    # 高危的置信度为中或高
    df.loc[(df['confidence'] == '') & (df['conclusion'].str.contains('高危')), 'confidence'] = '高'
    
    # 其他因素判断
    # 高置信度判断（其他因素）
    df.loc[(df['confidence'] == '') & 
           ((df['_adjusted_threat_score'] >= 80) & (df['false_positive_flag'] == False)), 'confidence'] = '高'
    
    # 中置信度判断（其他因素）
    df.loc[(df['confidence'] == '') & 
           (df['_adjusted_threat_score'] >= 60), 'confidence'] = '中'
    
    # 低置信度判断（默认）
    df.loc[df['confidence'] == '', 'confidence'] = '低'
    
    # 格式化置信度输出
    df['confidence'] = '置信度：' + df['confidence']
    
    return df

def generate_comprehensive_analysis(df):
    """
    生成综合分析结果
    """
    print("正在生成综合分析结果...")
    
    # 初始化综合分析列
    df['comprehensive_analysis'] = ''
    
    # 根据告警级别、攻击结果和置信度生成综合分析
    for idx, row in df.iterrows():
        alert_level = row['alert_level']
        attack_result = row['attackResult']
        confidence = row['confidence']
        conclusion = row['conclusion']
        
        # 构建综合分析
        analysis = f"{alert_level}级告警 - {attack_result}（{confidence}）\n"
        
        # 添加详细说明
        if alert_level == '高':
            if attack_result == '失陷':
                analysis += "系统已被攻击者控制，需立即隔离并进行应急响应"
            elif attack_result == '成功':
                analysis += "攻击者已成功利用漏洞，需立即处置并评估影响范围"
            else:
                analysis += "高风险安全事件，需立即处理"
        elif alert_level == '中':
            if attack_result == '尝试':
                analysis += "检测到明显的攻击企图，需进一步调查"
            else:
                analysis += "中等风险安全事件，建议及时处理"
        else:  # 低级别
            if attack_result == '误报':
                analysis += "疑似误报，可能是正常业务行为"
            else:
                analysis += "低风险安全事件，建议定期复核"
        
        df.loc[idx, 'comprehensive_analysis'] = analysis
    
    return df

def generate_statistics(df):
    """
    生成统计表
    """
    print("正在生成统计表...")
    
    # 1. 攻击结果分布
    attack_result_counts = df['attackResult'].value_counts()
    print("\n1. 攻击结果分布:")
    for result, count in attack_result_counts.items():
        print(f"{result}: {count}条记录")
    
    # 2. 置信度分布
    # 提取置信度中的高/中/低部分进行统计
    df['confidence_level'] = df['confidence'].str.replace('置信度：', '')
    confidence_counts = df['confidence_level'].value_counts()
    print("\n2. 置信度分布:")
    for conf, count in confidence_counts.items():
        print(f"{conf}置信度: {count}条记录")
    
    # 3. 攻击结果与置信度交叉表
    attack_confidence_cross = pd.crosstab(df['attackResult'], df['confidence_level'])
    print("\n3. 攻击结果与置信度交叉表:")

    # 构建统计结果字典
    statistics = {
        'attack_result_counts': attack_result_counts.to_dict(),
        'confidence_counts': confidence_counts.to_dict(),
        'attack_confidence_cross': attack_confidence_cross.to_dict()
    }
    
    # 删除临时列
    if 'confidence_level' in df.columns:
        df = df.drop('confidence_level', axis=1)
    
    return df, statistics

def is_chinese_ip(ip):
    """
    判断IP是否为中国IP（简化版，仅作示例）
    实际应用中可能需要更复杂的IP地理位置库
    """
    try:
        # 检查是否为内网IP
        ip_obj = ipaddress.ip_address(ip)
        if ip_obj.is_private:
            return True
        
        # 简单判断，以10、20开头的IP视为中国IP
        # 实际应用中应使用IP地理位置库
        if ip.startswith(('10.', '20.')):
            return True
    except:
        pass
    return False

def get_solution_from_original_data(df, original_df):
    """
    从原始数据中获取solution字段
    """
    print("正在从原始数据获取solution字段和目标地址...")
    
    if original_df is None or original_df.empty:
        print("警告: 原始数据为空，无法获取solution字段和目标地址")
        df['solution'] = ''
        if 'destAddress' not in df.columns:
            df['destAddress'] = ''
        return df
    
    try:
        # 检查原始数据是否包含solution字段
        if 'solution' not in original_df.columns:
            print(f"警告: 原始数据不包含solution字段")
        
        # 创建IP地址到solution的映射
        ip_solution_map = {}
        ip_dest_map = {}
        
        for _, row in original_df.iterrows():
            ip = row['srcAddress']
            
            # 获取solution字段
            if 'solution' in original_df.columns and pd.notna(row['solution']) and row['solution'].strip() != '':
                if ip not in ip_solution_map:
                    ip_solution_map[ip] = row['solution']
            
            # 获取destAddress字段
            if 'destAddress' in original_df.columns and pd.notna(row['destAddress']):
                if ip not in ip_dest_map:
                    ip_dest_map[ip] = row['destAddress']
        
        # 为结果数据添加solution字段
        df['solution'] = df['srcAddress'].map(ip_solution_map).fillna('')
        
        # 为结果数据添加destAddress字段（如果不存在）
        if 'destAddress' not in df.columns:
            df['destAddress'] = df['srcAddress'].map(ip_dest_map).fillna('')
        
        return df
    except Exception as e:
        print(f"警告: 获取solution字段和目标地址时出错: {e}")
        df['solution'] = ''
        if 'destAddress' not in df.columns:
            df['destAddress'] = ''
        return df

def main():
    # 调用step1模块处理原始数据
    print("调用step1模块处理原始数据...")
    original_df = step1_parse_security_logs.main()
    
    if original_df is None or original_df.empty:
        print("错误: step1模块未返回有效数据")
        return
    
    # 调用step8模块进行风险评分
    print("调用step8模块进行风险评分...")
    df, statistics = step8_risk_scoring.main()
    
    if df is None or df.empty:
        print("错误: step8模块未返回有效数据")
        return
    
    # 检查必需列是否存在
    required_columns = ['hour_slot', 'srcAddress', 'flow_directions', '_adjusted_threat_score', 
                       'conclusion', 'false_positive_flag']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 数据缺少必需列: {missing_columns}")
        print(f"当前列: {list(df.columns)}")
        return
    
    # 处理flow_directions列（如果是字符串形式）
    if df['flow_directions'].dtype == 'object' and not isinstance(df['flow_directions'].iloc[0], list):
        df['flow_directions'] = df['flow_directions'].apply(eval)  # 将字符串转换为列表
    
    # 从原始数据获取solution字段
    df = get_solution_from_original_data(df, original_df)
    
    # 分析攻击结果
    df = analyze_attack_results(df)
    
    # 分析置信度
    df = analyze_confidence(df)
    
    # 生成统计表
    df, step9_statistics = generate_statistics(df)
    
    # 只保留需要的列
    keep_columns = ['hour_slot', 'srcAddress', 'destAddress', 'flow_directions', 'threat_score', 
                   'conclusion', 'business_related_level', 'work_time_level', 'frequency_level',
                   'attackResult', 'confidence', 'solution']
    df = df[keep_columns]
    
    # 将英文字段名映射为中文字段名
    column_mapping = {
        'hour_slot': '时间段',
        'srcAddress': '源地址',
        'destAddress': '目标地址',
        'flow_directions': '流量方向',
        'threat_score': '威胁分数',
        'conclusion': '结论',
        'business_related_level': '业务相关性',
        'work_time_level': '工作时间级别',
        'frequency_level': '频率级别',
        'attackResult': '攻击结果',
        'confidence': '置信度',
        'solution': '解决方案'
    }
    
    df = df.rename(columns=column_mapping)
    # 返回处理后的数据和统计结果
    test =step9_statistics.get('attack_confidence_cross')
    print(test)
    return df, step9_statistics
if __name__ == "__main__":
    main()