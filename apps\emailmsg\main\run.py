#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
import sys
import poplib
import email
import json
import os
import hashlib
from email.message import Message
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from email.header import decode_header
from email.utils import parsedate_to_datetime
import base64
import ssl
import time

# 添加项目根目录到Python路径，以便导入core模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, project_root)

try:
    # 导入核心模块
    from core.model import Users
    from core import redis
    from core.utils.randoms import Random
    from core.utils.times import Time
except ImportError as e:
    logger.error("[消息通知] 导入核心模块失败: {}", str(e))
    # 如果导入失败，使用备用方案
    Users = None
    redis = None
    Random = None
    Time = None


def get_system_token():
    """
    获取系统TOKEN
    1. 从数据库中获取ID为1的用户的token
    2. 验证token在Redis中是否有效
    3. 如果有效则使用，如果失效则重新生成token
    """
    try:
        # 检查模块是否导入成功
        if Users is None or redis is None:
            raise Exception("核心模块未导入，无法获取系统TOKEN")

        # 1. 从数据库获取ID为1的用户的token
        user = Users.where('id', 1).first()
        if not user:
            raise Exception("未找到ID为1的用户，请确保数据库中存在系统用户")

        token = user.token
        if not token:
            logger.info("[消息通知] 用户token为空，生成新token")
            return generate_new_token(user)

        logger.info("[消息通知] 从数据库获取到TOKEN: {}...", token[:20] if len(token) > 20 else token)

        # 2. 验证token在Redis中是否有效
        if verify_token_in_redis(token):
            logger.info("[消息通知] TOKEN验证有效，直接使用")
            return token
        else:
            logger.info("[消息通知] TOKEN已失效，重新生成")
            return generate_new_token(user)

    except Exception as e:
        logger.error("[消息通知] 获取系统TOKEN失败: {}", str(e))
        raise Exception(f"获取系统TOKEN失败: {str(e)}")


def verify_token_in_redis(token):
    """验证TOKEN在Redis中是否有效"""
    try:
        if redis is None:
            logger.error("[消息通知] Redis模块未导入")
            return False

        # 检查token是否存在
        user_id = redis.get(token)
        if user_id:
            logger.info("[消息通知] TOKEN在Redis中有效，用户ID: {}", user_id)
            return True
        else:
            logger.info("[消息通知] TOKEN在Redis中不存在或已过期")
            return False
    except Exception as e:
        logger.error("[消息通知] Redis TOKEN验证失败: {}", str(e))
        return False


def generate_new_token(user):
    """生成新的TOKEN并更新到数据库和Redis"""
    try:
        if Random is None:
            raise Exception("Random模块未导入，无法生成TOKEN")

        # 生成新TOKEN
        token = "W5_TOKEN_" + Random.make_token(string=user.account)

        # 存储到Redis（7天过期）
        if redis:
            redis.set(token, str(user.id), ex=60 * 60 * 24 * 7)
            logger.info("[消息通知] 新TOKEN已存储到Redis")

        # 更新数据库中的TOKEN
        if Time is not None:
            update_time = Time.get_date_time()
        else:
            from datetime import datetime
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        Users.where('id', user.id).update({
            "token": token,
            "update_time": update_time
        })
        logger.info("[消息通知] 数据库中的TOKEN已更新")

        return token

    except Exception as e:
        logger.error("[消息通知] 生成新TOKEN失败: {}", str(e))
        raise Exception(f"生成新TOKEN失败: {str(e)}")




# 禁用SSL证书验证警告
ssl._create_default_https_context = ssl._create_unverified_context

# 常量配置
MAX_ATTACHMENT_SIZE = 10 * 1024 * 1024  # 10MB
READ_EMAILS_FILE = os.path.dirname(__file__)+"/read_emails.json"
MAX_RECENT_EMAILS = 5  # 只检查最新的5封邮件
ATTACHMENTS_DIR = os.path.dirname(__file__)+"/email_attachments"  # 附件保存目录
SESSION_TIMEOUT = 300  # 会话超时时间(秒)


class EmailConfig:
    """邮件服务器配置类"""
    
    def __init__(self):
        self.servers = [
            {
                "host": "newmail.sgcc.com.cn",
                "port": 110,
                "username": "<EMAIL>",
                "password": "whgd!@95598"
            },
            {
                "host": "newmail.sgcc.com.cn",
                "port": 110,
                "username": "<EMAIL>",
                "password": "2017xt@wh"
            }
        ]
        self.timeout = 30
        self.max_retries = 3


class EmailStatusTracker:
    """邮件状态跟踪器（支持多账户）"""
    
    def __init__(self, storage_path: str = READ_EMAILS_FILE):
        self.storage_path = storage_path
        self.read_data = {}
        self._load()
    
    def _load(self):
        """加载已读状态"""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    self.read_data = json.load(f)
        except Exception as e:
            logger.error(f"加载已读状态失败: {str(e)}")
            self.read_data = {}
    
    def _save(self):
        """保存已读状态"""
        try:
            with open(self.storage_path, 'w') as f:
                json.dump(self.read_data, f, indent=2)
        except Exception as e:
            logger.error(f"保存已读状态失败: {str(e)}")
    
    def is_read(self, account: str, email_id: str) -> bool:
        """检查邮件是否已读"""
        return email_id in self.read_data.get(account, [])
    
    def mark_as_read(self, account: str, email_ids: List[str]):
        """标记邮件为已读"""
        if account not in self.read_data:
            self.read_data[account] = []
        
        for email_id in email_ids:
            if email_id not in self.read_data[account]:
                self.read_data[account].append(email_id)
        self._save()


class EmailClient:
    """邮件客户端核心类"""
    
    def __init__(self, config: EmailConfig, tracker: EmailStatusTracker):
        self.config = config
        self.tracker = tracker
        self.current_connection = None
        self.current_account = None
        os.makedirs(ATTACHMENTS_DIR, exist_ok=True)
    
    def _connect(self, server_config: Dict[str, Any]) -> bool:
        """连接到邮件服务器"""
        try:
            self.current_connection = poplib.POP3(
                server_config["host"],
                server_config["port"],
                timeout=self.config.timeout
            )
            self.current_connection.user(server_config["username"])
            self.current_connection.pass_(server_config["password"])
            self.current_account = server_config["username"]
            logger.info(f"成功连接到邮箱: {server_config['username']}")
            return True
        except Exception as e:
            logger.error(f"连接邮箱失败({server_config['username']}): {str(e)}")
            return False
    
    def _disconnect(self):
        """断开当前连接"""
        if self.current_connection:
            try:
                self.current_connection.quit()
            except:
                pass
            finally:
                self.current_connection = None
            logger.info(f"已断开邮箱连接: {self.current_account}")
            self.current_account = None
    
    def _generate_email_id(self, msg: Message) -> str:
        """生成邮件唯一标识"""
        return hashlib.md5(msg.as_bytes()).hexdigest()
    
    def _decode_header(self, header) -> str:
        """解码邮件头"""
        try:
            decoded = decode_header(header)
            return ''.join(
                t[0].decode(t[1] or 'utf-8', errors='ignore') 
                if isinstance(t[0], bytes) else t[0] 
                for t in decoded
            )
        except Exception as e:
            logger.warning(f"邮件头解码失败: {str(e)}")
            return str(header)
    
    def _extract_text_content(self, msg: Message) -> str:
        """提取纯文本内容"""
        text_content = ""
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    payload = part.get_payload(decode=True)
                    charset = part.get_content_charset() or 'utf-8'
                    text_content += payload.decode(charset, errors='ignore')
        else:
            payload = msg.get_payload(decode=True)
            charset = msg.get_content_charset() or 'utf-8'
            text_content = payload.decode(charset, errors='ignore')
        
        return text_content.strip()
    
    def _save_attachment(self, part: Message, email_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """保存邮件附件"""
        filename = self._decode_header(part.get_filename())
        if not filename:
            return None
        
        try:
            payload = part.get_payload(decode=True)
            if len(payload) > MAX_ATTACHMENT_SIZE:
                return {
                    "name": filename,
                    "error": f"附件超过大小限制({len(payload)} > {MAX_ATTACHMENT_SIZE})"
                }
            
            # 创建账户专属目录
            account_dir = os.path.join(ATTACHMENTS_DIR, self.current_account)
            os.makedirs(account_dir, exist_ok=True)
            
            # 保存附件
            file_path = os.path.join(account_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(payload)
            
            return {
                "name": filename,
                "path": os.path.join(self.current_account, filename),
                "size": len(payload),
                "saved_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            return {
                "name": filename,
                "error": str(e)
            }
    
    def fetch_unread_emails(self) -> List[Dict[str, Any]]:
        """获取所有未读邮件"""
        all_unread = []
        
        for server_config in self.config.servers:
            if not self._connect(server_config):
                continue
                
            try:
                email_count = len(self.current_connection.list()[1])
                start_index = max(1, email_count - MAX_RECENT_EMAILS + 1)
                
                for i in range(start_index, email_count + 1):
                    try:
                        _, lines, _ = self.current_connection.retr(i)
                        msg = email.message_from_bytes(b'\r\n'.join(lines))
                        
                        email_id = self._generate_email_id(msg)
                        if self.tracker.is_read(server_config["username"], email_id):
                            continue
                        
                        # 解析邮件基本信息
                        email_data = {
                            "account": server_config["username"],
                            "id": email_id,
                            "subject": self._decode_header(msg.get('Subject', '无主题')),
                            "from": self._decode_header(msg.get('From', '未知发件人')),
                            "to": self._decode_header(msg.get('To', '')),
                            "date": parsedate_to_datetime(msg.get('Date')).strftime("%Y-%m-%d %H:%M:%S"),
                            "received_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "body": self._extract_text_content(msg),
                            "attachments": []
                        }
                        
                        # 处理附件
                        for part in msg.walk():
                            if part.get_filename() and part.get_content_disposition() == 'attachment':
                                attachment = self._save_attachment(part, email_data)
                                if attachment:
                                    email_data["attachments"].append(attachment)
                        
                        all_unread.append(email_data)
                    
                    except Exception as e:
                        logger.error(f"处理邮件失败({server_config['username']} #{i}): {str(e)}")
                        continue
            
            except Exception as e:
                logger.error(f"获取邮件列表失败({server_config['username']}): {str(e)}")
            finally:
                self._disconnect()
        
        return all_unread


# 全局服务实例
_email_client = None
_tracker = None
_last_init_time = 0


def get_email_service() -> Tuple[EmailClient, bool]:
    """获取邮件服务实例"""
    global _email_client, _tracker, _last_init_time
    
    current_time = time.time()
    
    if (_email_client is None or 
        _tracker is None or 
        current_time - _last_init_time > SESSION_TIMEOUT):
        
        logger.info("初始化邮件服务...")
        
        config = EmailConfig()
        tracker = EmailStatusTracker()
        client = EmailClient(config, tracker)
        
        _email_client = client
        _tracker = tracker
        _last_init_time = current_time
        
        logger.info("邮件服务初始化完成")
    
    return _email_client, True


async def check_unread_emails() -> Dict[str, Any]:
    """检查所有未读邮件
    
    Returns:
        Dict[str, Any]: 包含查询结果的字典
            - status: "0" 或 "1"
            - data: 邮件数据列表
            - message: 状态信息
            - total_unread: 未读邮件总数
    """
    logger.info("开始检查未读邮件...")
    
    try:
        client, success = get_email_service()
        if not success:
            return {
                "status": "1",
                "result": "邮件服务初始化失败"
            }
        
        unread_emails = client.fetch_unread_emails()
        total_unread = len(unread_emails)
        
        # 标记为已读
        if unread_emails:
            account_emails = {}
            for email in unread_emails:
                account_emails.setdefault(email["account"], []).append(email["id"])
            
            for account, email_ids in account_emails.items():
                client.tracker.mark_as_read(account, email_ids)
            logger.info(f"已标记 {total_unread} 封邮件为已读")
        
        # 格式化返回结果
        formatted_emails = []
        for email in unread_emails:
            formatted_emails.append({
                "account": email["account"],
                "subject": email["subject"],
                "from": email["from"],
                "to": email["to"],
                "received_date": email["received_time"],
                "body": email["body"],
                "attachments": [
                    {"name": att["name"], "path": att["path"]}
                    for att in email["attachments"] if "path" in att
                ]
            })
        
        return {
            "status": "0",
            "result": {
                "mails_count": len(formatted_emails),
                "mails": formatted_emails,
                "total_unread": total_unread
            },
            "message": f"发现 {total_unread} 封未读邮件"
        }
    
    except Exception as e:
        logger.error(f"检查未读邮件失败: {str(e)}")
        return {
            "status": "1",
            "result": f"检查未读邮件失败: {str(e)}"
        }


# if __name__ == "__main__":
    # print(check_unread_emails())