#!/usr/bin/env python
# encoding:utf-8
"""
测试step3_business_analysis.py与step2_flow_direction.py的集成
"""

import pandas as pd
from step2_flow_direction import get_flow_direction_data
from step3_business_analysis import analyze_business_relevance

def test_step3_integration():
    """
    测试step3与step2的集成是否正常工作
    """
    print("=== 开始Step3集成测试 ===")
    
    # 步骤1：调用step2获取流向分析数据
    print("1. 调用step2获取流向分析数据...")
    df = get_flow_direction_data()
    if df is None or df.empty:
        print("❌ 无法获取流向分析数据")
        return False
    print(f"✅ 成功获取 {len(df)} 条流向分析记录")
    
    # 步骤2：检查必需列是否存在
    print("2. 检查数据结构...")
    required_columns = ['srcAddress', 'destAddress', 'time', 'id', 'flow_direction']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 缺少必需列: {missing_columns}")
        return False
    print("✅ 数据结构检查通过")
    
    # 步骤3：处理时间列
    print("3. 处理时间格式...")
    df['time'] = pd.to_datetime(df['time'], errors='coerce')
    df = df.dropna(subset=['time'])
    print(f"✅ 时间格式处理完成，保留 {len(df)} 条记录")
    
    # 步骤4：分析业务相关性
    print("4. 分析业务相关性...")
    df = analyze_business_relevance(df)
    print("✅ 业务相关性分析完成")
    
    # 步骤5：验证结果
    print("5. 验证结果...")
    
    # 检查新增列是否存在
    new_columns = ['user_agent', 'http_host', 'referer', 'origin', 'is_business_related']
    missing_new_columns = [col for col in new_columns if col not in df.columns]
    if missing_new_columns:
        print(f"❌ 缺少新增列: {missing_new_columns}")
        return False
    
    # 统计业务相关性
    business_counts = df['is_business_related'].value_counts()
    flow_counts = df['flow_direction'].value_counts()
    total_records = len(df)
    
    print(f"\n=== 测试结果 ===")
    print(f"总记录数: {total_records}")
    print(f"\n流向分布:")
    for direction, count in flow_counts.items():
        percentage = (count / total_records) * 100
        print(f"  {direction}: {count}条 ({percentage:.1f}%)")
    
    print(f"\n业务相关性分布:")
    for is_business, count in business_counts.items():
        percentage = (count / total_records) * 100
        business_type = "业务相关" if is_business else "非业务相关"
        print(f"  {business_type}: {count}条 ({percentage:.1f}%)")
    
    # 检查业务相关性逻辑
    internal_to_external = df[df['flow_direction'] == '内到外']
    business_in_internal_to_external = internal_to_external['is_business_related'].sum()
    total_internal_to_external = len(internal_to_external)
    
    if total_internal_to_external > 0:
        business_ratio = business_in_internal_to_external / total_internal_to_external
        print(f"\n内到外流量中业务相关比例: {business_ratio:.2%}")
        if 0.6 <= business_ratio <= 0.8:  # 预期70%左右
            print("✅ 业务相关性逻辑正常")
        else:
            print("⚠️  业务相关性比例异常，但可能是随机性导致")
    
    print("\n✅ Step3集成测试完成！")
    return True

if __name__ == '__main__':
    test_step3_integration()