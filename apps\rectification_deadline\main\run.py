#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

from loguru import logger
from datetime import datetime, date
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from core.model import Rectification
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    # 如果导入失败，返回错误信息
    async def query_overdue(limit=10):
        return {
            "status": 1, 
            "result": f"模块导入失败，请检查环境配置"
        }
else:
    async def query_overdue(limit=10):
        """
        查询超过整改截止时间的整改单记录
        
        Args:
            limit (int): 返回记录数量限制，默认10条
            
        Returns:
            dict: 包含超期记录数量和详细记录的字典
        """
        try:
            logger.info(f"[整改截止时间查询] APP执行参数为: limit={limit}")
            
            # 参数验证
            if not isinstance(limit, int) or limit <= 0:
                limit = 10
                logger.warning(f"limit参数无效，使用默认值: {limit}")
            
            # 获取当前日期
            current_date = date.today()
            logger.info(f"当前日期: {current_date}")
            
            # 查询超过截止时间且未整改完成的记录
            overdue_records = (
                Rectification.select()
                .where('rect_deadline', '<', current_date)  # 截止时间小于当前日期
                .where('is_fixed', False)  # 未整改完成
                .where_not_null('rect_deadline')  # 截止时间不为空
                .order_by('rect_deadline', 'asc')  # 按截止时间升序（超期最久的在前）
                .limit(limit)
                .get()
            )
            
            if not overdue_records:
                logger.info("未找到超期记录")
                return {
                    "status": 0,
                    "result": {
                        "overdue_count": 0,
                        "records": [],
                        "message": "当前没有超期的整改单"
                    }
                }
            
            # 处理查询结果
            records = []
            for record in overdue_records:
                # 计算超期天数
                days_overdue = (current_date - record.rect_deadline).days
                
                record_data = {
                    "rect_id": record.rect_id,
                    "serial_number": record.serial_number,
                    "vuln_name": record.vuln_name,
                    "vuln_type": record.vuln_type,
                    "vuln_level": record.vuln_level,
                    "rect_deadline": record.rect_deadline.strftime('%Y-%m-%d') if record.rect_deadline else None,
                    "days_overdue": days_overdue,
                    "responsible_dept": record.responsible_dept,
                    "source_unit": record.source_unit,
                    "is_fixed": record.is_fixed,
                    "is_responded": record.is_responded,
                    "description": record.description,
                    "ip": record.ip,
                    "create_time": record.create_time.strftime('%Y-%m-%d %H:%M:%S') if record.create_time else None
                }
                records.append(record_data)
            
            # 按超期天数降序排列
            records.sort(key=lambda x: x['days_overdue'], reverse=True)
            
            result = {
                "processed_count": len(records),
                "results": records,
                "current_date": current_date.strftime('%Y-%m-%d'),
                "query_limit": limit
            }
            
            logger.info(f"成功查询到 {len(records)} 条超期记录")
            return {"status": 0, "result": result}
            
        except Exception as e:
            error_msg = f"查询超期记录失败: {str(e)}"
            logger.error(f"[整改截止时间查询] {error_msg}", exc_info=True)
            return {
                "status": 1,
                "result": error_msg
            } 