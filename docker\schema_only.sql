-- MySQL dump 10.13  Distrib 8.0.43, for Linux (x86_64)
--
-- Host: localhost    Database: w5_db
-- ------------------------------------------------------
-- Server version	8.0.43-0ubuntu0.22.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `w5_db`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `w5_db` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `w5_db`;

--
-- Table structure for table `apilog_source_tbl`
--

DROP TABLE IF EXISTS `apilog_source_tbl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apilog_source_tbl` (
  `id` bigint NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source_id` bigint NOT NULL,
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `params` varchar(510) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dataset_info`
--

DROP TABLE IF EXISTS `dataset_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dataset_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `tags` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `num_samples` int DEFAULT NULL,
  `format` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_schema` text COLLATE utf8mb4_general_ci,
  `storage_path` text COLLATE utf8mb4_general_ci NOT NULL,
  `download_url` text COLLATE utf8mb4_general_ci,
  `owner` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dataset_logs`
--

DROP TABLE IF EXISTS `dataset_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dataset_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int DEFAULT NULL,
  `log_type` enum('create','update','delete','download') COLLATE utf8mb4_general_ci NOT NULL,
  `message` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=769 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dataset_uploads`
--

DROP TABLE IF EXISTS `dataset_uploads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dataset_uploads` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int DEFAULT NULL,
  `uploader` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `upload_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `file_path` text COLLATE utf8mb4_general_ci NOT NULL,
  `file_size` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('pending','success','failed') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `decision_info`
--

DROP TABLE IF EXISTS `decision_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `decision_info` (
  `event_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `event_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `maintenance_method` text COLLATE utf8mb4_general_ci,
  `check_item` text COLLATE utf8mb4_general_ci,
  `device_software_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vendor_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `harm_name` text COLLATE utf8mb4_general_ci,
  `description` text COLLATE utf8mb4_general_ci,
  `prevention_measures` text COLLATE utf8mb4_general_ci,
  `attack_cause` text COLLATE utf8mb4_general_ci,
  `defective_device_software` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `configuration_solution` text COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`event_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `file_source_tbl`
--

DROP TABLE IF EXISTS `file_source_tbl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `file_source_tbl` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `type` varchar(255) NOT NULL COMMENT '数据源类型',
  `name` varchar(255) NOT NULL COMMENT '数据源名称',
  `headers` text COMMENT 'CSV文件的表头',
  `host_index` int DEFAULT NULL COMMENT 'host索引',
  `time_index` int DEFAULT NULL COMMENT '时间索引',
  `level_index` int DEFAULT NULL COMMENT '日志级别索引',
  `type_index` int DEFAULT NULL COMMENT '类型索引',
  `source_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_source_id` (`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件源表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hardware_resources`
--

DROP TABLE IF EXISTS `hardware_resources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hardware_resources` (
  `id` int NOT NULL COMMENT '自增主键',
  `resource_category` varchar(255) NOT NULL COMMENT '资源分类',
  `resource_type` varchar(255) NOT NULL COMMENT '资源类型',
  `standard_full_name` varchar(255) NOT NULL COMMENT '标准全称',
  `manufacturer` varchar(255) NOT NULL COMMENT '制造商',
  `brand` varchar(255) NOT NULL COMMENT '品牌',
  `model` varchar(255) NOT NULL COMMENT '型号',
  `factory_serial_number` varchar(255) NOT NULL COMMENT '出厂序列号',
  `device_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备状态',
  `is_synced_to_erp` enum('是','否') NOT NULL COMMENT '是否同步给ERP',
  `operation_unit` varchar(255) NOT NULL COMMENT '运维单位',
  `purchase_date` date NOT NULL COMMENT '采购日期',
  `affiliated_network` varchar(20) NOT NULL COMMENT '所属网络',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物理机信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `log_row_tbl`
--

DROP TABLE IF EXISTS `log_row_tbl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `log_row_tbl` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'host',
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志级别',
  `data_source_id` bigint DEFAULT NULL COMMENT '数据源ID',
  `data_source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源类型',
  `source_id` bigint DEFAULT NULL COMMENT '源ID',
  `row_number` int DEFAULT NULL COMMENT '行号',
  `raw_string` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '原始字符串',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1958733866759700482 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='日志行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `source_tbl`
--

DROP TABLE IF EXISTS `source_tbl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `source_tbl` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `service` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `syslog_source_tbl`
--

DROP TABLE IF EXISTS `syslog_source_tbl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `syslog_source_tbl` (
  `id` bigint NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `source_id` bigint NOT NULL,
  `host` varchar(255) NOT NULL,
  `port` int DEFAULT NULL,
  `started` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_apikeys`
--

DROP TABLE IF EXISTS `user_apikeys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_apikeys` (
  `user_id` int NOT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`api_key`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_bug`
--

DROP TABLE IF EXISTS `user_bug`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_bug` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `bug_description` text COLLATE utf8mb4_general_ci NOT NULL,
  `bug_image` longblob,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_chat`
--

DROP TABLE IF EXISTS `user_chat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_chat` (
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` int NOT NULL,
  `histext` longtext COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_history`
--

DROP TABLE IF EXISTS `user_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_history` (
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `session_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `hismain` text COLLATE utf8mb4_general_ci,
  `histext` longtext COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_alert`
--

DROP TABLE IF EXISTS `w5_alert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_alert` (
  `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `source_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `destination_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `source_port` int DEFAULT NULL,
  `destination_port` int DEFAULT NULL,
  `protocol` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `attack_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `severity` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `signature` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `detection_system` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `correlation_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `detail` text COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息',
  `ip_location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息',
  PRIMARY KEY (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_alert_analysis`
--

DROP TABLE IF EXISTS `w5_alert_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_alert_analysis` (
  `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `alert_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`,`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_alert_copy1`
--

DROP TABLE IF EXISTS `w5_alert_copy1`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_alert_copy1` (
  `alert_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `source_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `destination_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `source_port` int DEFAULT NULL,
  `destination_port` int DEFAULT NULL,
  `protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `attack_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `detection_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `correlation_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SELKS原始详细信息',
  `ip_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP归属地信息',
  PRIMARY KEY (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_analysis`
--

DROP TABLE IF EXISTS `w5_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_analysis` (
  `analysis_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `attack_summary` text COLLATE utf8mb4_general_ci,
  `affected_systems` text COLLATE utf8mb4_general_ci,
  `potential_risk` text COLLATE utf8mb4_general_ci,
  `recommended_actions` text COLLATE utf8mb4_general_ci,
  `notes` text COLLATE utf8mb4_general_ci,
  `status` int DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_audit`
--

DROP TABLE IF EXISTS `w5_audit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_audit` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `workflow_uuid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `only_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `user_id` int NOT NULL DEFAULT '0',
  `audit_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `start_app` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_login_history`
--

DROP TABLE IF EXISTS `w5_login_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_login_history` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL DEFAULT '0',
  `login_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_logs`
--

DROP TABLE IF EXISTS `w5_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `only_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `app_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `app_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `result` mediumtext COLLATE utf8mb4_general_ci NOT NULL,
  `status` int NOT NULL DEFAULT '0',
  `html` mediumtext COLLATE utf8mb4_general_ci,
  `args` mediumtext COLLATE utf8mb4_general_ci,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=496 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_nav`
--

DROP TABLE IF EXISTS `w5_nav`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_nav` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `icon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `is_menu` int NOT NULL DEFAULT '0',
  `up` int NOT NULL DEFAULT '0',
  `order` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_notification`
--

DROP TABLE IF EXISTS `w5_notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_notification` (
  `notification_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `notification_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('read','unread') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unread',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `result_data` json DEFAULT NULL,
  `execution_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_decided` tinyint(1) DEFAULT '0' COMMENT '是否决策：0-未决策，1-已决策',
  `is_processed` tinyint(1) DEFAULT '0' COMMENT '是否处理：0-未处理，1-已处理',
  `is_dispose` tinyint(1) DEFAULT '0' COMMENT '是否已处置：0-未处置，1-已处置',
  `dispose_msg` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`notification_id`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_source` (`source`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_is_decided` (`is_decided`),
  KEY `idx_is_processed` (`is_processed`),
  KEY `idx_is_dispose` (`is_dispose`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_operation_log`
--

DROP TABLE IF EXISTS `w5_operation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_operation_log` (
  `log_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志ID',
  `operator` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `operation_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型',
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operation_details` text COLLATE utf8mb4_general_ci COMMENT '操作详情',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `idx_rect_id` (`rect_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_rectification`
--

DROP TABLE IF EXISTS `w5_rectification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_rectification` (
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `message_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息ID',
  `serial_number` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单编号',
  `source_unit` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源单位（如市公司）',
  `source_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源编号或来源说明',
  `issue_date` date DEFAULT NULL COMMENT '下发时间',
  `rect_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '整改单类型（二级单位整改单/内部整改单/自建系统整改单）',
  `description` text COLLATE utf8mb4_general_ci COMMENT '隐患描述',
  `vuln_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患名称',
  `vuln_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患类型',
  `vuln_level` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患级别（低/中/高危/严重）',
  `vuln_count` int DEFAULT NULL COMMENT '隐患个数',
  `ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐患IP',
  `responsible_dept` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '责任单位/部门',
  `is_responded` tinyint(1) DEFAULT '0' COMMENT '是否反馈',
  `response_date` date DEFAULT NULL COMMENT '反馈时间',
  `is_fixed` tinyint(1) DEFAULT '0' COMMENT '是否整改',
  `fix_details` text COLLATE utf8mb4_general_ci COMMENT '整改详情',
  `retest_status` tinyint(1) DEFAULT '0' COMMENT '复测状态',
  `note` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `rect_deadline` date DEFAULT NULL,
  `remind_time` date DEFAULT NULL COMMENT '提醒时间',
  `last_rescan_time` date DEFAULT NULL COMMENT '上次复测时间',
  `is_excel_imported` tinyint(1) DEFAULT '0' COMMENT '是否通过Excel导入',
  `excel_filename` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入的Excel文件名',
  `excel_row_index` int DEFAULT NULL COMMENT '导入时的原始Excel行号',
  `import_batch_id` varchar(36) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '导入批次ID',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `inspection_content` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察内容',
  `inspection_overview` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察实施概况',
  `system_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统名称',
  `existing_hazard` text COLLATE utf8mb4_general_ci COMMENT '存在隐患',
  `rectification_suggestion` text COLLATE utf8mb4_general_ci COMMENT '整改建议',
  `rectification_time_requirement` text COLLATE utf8mb4_general_ci COMMENT '整改时间要求',
  `inspection_executor` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察执行负责人',
  `rescan_situation` text COLLATE utf8mb4_general_ci COMMENT '复查情况',
  `inspection_rescan_person` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '督察复查人员',
  PRIMARY KEY (`rect_id`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_report`
--

DROP TABLE IF EXISTS `w5_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_report` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `report_no` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `workflow_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_rescan_record`
--

DROP TABLE IF EXISTS `w5_rescan_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_rescan_record` (
  `rescan_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '复测记录ID',
  `rect_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联整改单ID',
  `rescan_time` datetime DEFAULT NULL COMMENT '复测时间',
  `result` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '复测结果，如“通过/未通过/需复检”',
  `rescan_note` text COLLATE utf8mb4_general_ci COMMENT '复测备注',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`rescan_id`),
  KEY `idx_rect_id` (`rect_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_role`
--

DROP TABLE IF EXISTS `w5_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_role` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_role_nav`
--

DROP TABLE IF EXISTS `w5_role_nav`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_role_nav` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL DEFAULT '0',
  `nav_id` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_setting`
--

DROP TABLE IF EXISTS `w5_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_setting` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_key` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_timer`
--

DROP TABLE IF EXISTS `w5_timer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_timer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `timer_uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `interval_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `time` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `start_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `end_date` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `jitter` int NOT NULL DEFAULT '0',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`timer_uuid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_type`
--

DROP TABLE IF EXISTS `w5_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL DEFAULT '1',
  `name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_user_role`
--

DROP TABLE IF EXISTS `w5_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_user_role` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL DEFAULT '0',
  `role_id` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_users`
--

DROP TABLE IF EXISTS `w5_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `passwd` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `nick_name` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `avatar` text COLLATE utf8mb4_general_ci,
  `email` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `token` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_account` (`account`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_variablen`
--

DROP TABLE IF EXISTS `w5_variablen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_variablen` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type_id` int NOT NULL DEFAULT '0',
  `key` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `value` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index.key` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `w5_workflow`
--

DROP TABLE IF EXISTS `w5_workflow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `w5_workflow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `user_id` int NOT NULL DEFAULT '0',
  `type_id` int NOT NULL DEFAULT '0',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remarks` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `status` int NOT NULL DEFAULT '0',
  `start_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `end_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `input_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `webhook_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `timer_app` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `for_list` mediumtext COLLATE utf8mb4_general_ci,
  `if_list` mediumtext COLLATE utf8mb4_general_ci,
  `audit_list` mediumtext COLLATE utf8mb4_general_ci,
  `flow_json` mediumtext COLLATE utf8mb4_general_ci,
  `flow_data` mediumtext COLLATE utf8mb4_general_ci,
  `controller_data` mediumtext COLLATE utf8mb4_general_ci,
  `local_var_data` mediumtext COLLATE utf8mb4_general_ci,
  `grid_type` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_marker` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_color` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_connector` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `edge_router` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `thumbnail` longtext COLLATE utf8mb4_general_ci,
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'w5_db'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-09-16 16:28:22
