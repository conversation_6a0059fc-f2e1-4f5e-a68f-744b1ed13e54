#!/usr/bin/env python
# encoding:utf-8

import pandas as pd
from step9_alert_analysis_fw import main as step9_main

def test_step9_integration():
    """
    测试step9_alert_analysis.py与step8_risk_scoring.py的集成
    """
    print("开始测试step9告警分析集成...")
    
    # 调用step9主函数
    result = step9_main()
    
    if result is None or len(result) != 2:
        print("错误: step9未返回有效结果")
        return
    
    df, statistics = result
    
    if df is None or df.empty:
        print("错误: 返回的DataFrame为空")
        return
    
    print(f"\n=== 数据完整性检查 ===")
    print(f"处理记录数: {len(df)}")
    print(f"数据列数: {len(df.columns)}")
    print(f"数据列名: {list(df.columns)}")
    
    # 检查关键列是否存在
    key_columns = ['hour_slot', 'srcAddress', 'flow_directions', '_adjusted_threat_score', 
                   'conclusion', 'attackResult', 'confidence']
    
    missing_columns = [col for col in key_columns if col not in df.columns]
    if missing_columns:
        print(f"警告: 缺少关键列: {missing_columns}")
    else:
        print("✓ 所有关键列都存在")
    
    # 检查攻击结果分布
    if 'attackResult' in df.columns:
        print(f"\n=== 攻击结果分布 ===")
        attack_result_dist = df['attackResult'].value_counts()
        for result, count in attack_result_dist.items():
            print(f"{result}: {count}条")
    
    # 检查置信度分布
    if 'confidence' in df.columns:
        print(f"\n=== 置信度分布 ===")
        confidence_dist = df['confidence'].value_counts()
        for conf, count in confidence_dist.items():
            print(f"{conf}: {count}条")
    
    # 检查威胁分数统计
    if '_adjusted_threat_score' in df.columns:
        print(f"\n=== 调整后威胁分数统计 ===")
        threat_scores = df['_adjusted_threat_score']
        print(f"最小值: {threat_scores.min():.1f}")
        print(f"最大值: {threat_scores.max():.1f}")
        print(f"平均值: {threat_scores.mean():.1f}")
        print(f"中位数: {threat_scores.median():.1f}")
    
    # 检查统计结果
    print(f"\n=== 统计结果检查 ===")
    if statistics:
        print(f"统计结果类型: {type(statistics)}")
        if isinstance(statistics, dict):
            print(f"统计结果键: {list(statistics.keys())}")
            
            # 检查攻击结果统计
            if 'attack_result_counts' in statistics:
                print(f"攻击结果统计类型: {type(statistics['attack_result_counts'])}")
            
            # 检查置信度统计
            if 'confidence_counts' in statistics:
                print(f"置信度统计类型: {type(statistics['confidence_counts'])}")
            
            # 检查交叉表
            if 'attack_confidence_cross' in statistics:
                print(f"交叉表类型: {type(statistics['attack_confidence_cross'])}")
        else:
            print(f"警告: 统计结果不是字典类型")
    else:
        print("警告: 统计结果为空")
    
    # 保存测试结果到CSV文件
    output_file = "test_step9_integration_result.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n测试结果已保存到: {output_file}")
    
    print("\n=== step9集成测试完成 ===")
    return df, statistics

if __name__ == "__main__":
    test_step9_integration()