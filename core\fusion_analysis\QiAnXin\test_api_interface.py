#!/usr/bin/env python3

import sys
import os
import pandas as pd

# 添加QiAnXin目录到Python路径
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 添加qiAnXinInterface目录到Python路径
qianxin_interface_path = os.path.join(os.path.dirname(current_dir), 'qiAnXinInterface')
if qianxin_interface_path not in sys.path:
    sys.path.append(qianxin_interface_path)

try:
    # 直接导入step5_temp模块测试
    from step5_temp import main as get_threat_scores
    
    print("正在测试威胁评分数据获取...")
    
    # 调用威胁评分函数
    result_df = get_threat_scores()
    
    if result_df is None:
        print("❌ 威胁评分函数返回None")
    elif result_df.empty:
        print("❌ 威胁评分函数返回空DataFrame")
    else:
        print("✅ 威胁评分数据获取成功!")
        print(f"数据行数: {len(result_df)}")
        print(f"数据列: {list(result_df.columns)}")
        
        # 模拟接口数据处理逻辑
        data_list = result_df.to_dict('records')
        
        # 处理数据格式，确保所有值都是JSON可序列化的
        for item in data_list:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
                elif hasattr(value, 'item'):  # numpy类型转换
                    item[key] = value.item()
                elif str(type(value)).startswith('<class \'pandas'):
                    item[key] = str(value)
        
        # 模拟接口返回格式
        api_response = {
            "code": 0,
            "msg": "Success",
            "data": {
                "total": len(data_list),
                "list": data_list[:3]  # 只显示前3条记录作为示例
            }
        }
        
        print("\n模拟接口返回格式:")
        print(f"总记录数: {api_response['data']['total']}")
        print(f"示例数据: {api_response['data']['list']}")
        print("\n✅ 接口数据格式测试通过!")
        
except Exception as e:
    print(f"❌ 测试过程中发生错误: {str(e)}")
    import traceback
    traceback.print_exc()