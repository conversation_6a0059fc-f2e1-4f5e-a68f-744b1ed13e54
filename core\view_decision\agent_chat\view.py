from . import *
from flask import request, jsonify, Response
import requests
import json

# 目标服务的基础URL
TARGET_BASE_URL = "http://127.0.0.1:28123"
TARGET_CHAT_URL = f"{TARGET_BASE_URL}/chat"

@r.route("/chat", methods=['POST'])
def agent_chat():
    """
    对话接口，作为代理转发请求到目标服务，并正确处理流式响应。
    """
    try:
        # 1. 获取客户端请求数据
        client_data = request.get_json()
        if not client_data or 'message' not in client_data:
            return jsonify({
                "status": "error",
                "code": 400,
                "message": "缺少必要参数: message",
                "result": None
            }), 400
            
        # 2. 将请求转发到目标服务
        #    使用 stream=True 来确保连接保持开放
        response = requests.post(
            TARGET_CHAT_URL, 
            json=client_data, 
            stream=True, 
            timeout=300
        )
        
        # 检查目标服务的响应状态码
        response.raise_for_status()

        # 3. 创建一个生成器函数，用于“透传”数据
        #    这个生成器不做任何解析，直接yield数据块
        def generate_chunks():
            try:
                # 使用 iter_content 迭代原始数据块
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        yield chunk
            except Exception as e:
                # 在代理流式传输过程中捕获异常
                print(f"代理流式传输时发生错误: {e}")
                # 也可以向客户端发送一个错误事件
                error_event = f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"
                yield error_event.encode('utf-8')

        # 4. 返回一个Flask的流式响应
        #    关键：直接使用目标服务的响应头（尤其是Content-Type）
        #    Flask会自动处理分块传输编码
        return Response(
            generate_chunks(),
            status=response.status_code,
            headers=dict(response.headers)
        )

    except requests.exceptions.HTTPError as e:
        # 捕获来自目标服务的HTTP错误（如4xx, 5xx）
        return jsonify({
            "status": "error",
            "code": e.response.status_code,
            "message": f"目标服务返回错误: {e.response.text}",
            "result": None
        }), e.response.status_code

    except requests.exceptions.RequestException as e:
        # 捕获连接到目标服务的网络错误
        return jsonify({
            "status": "error",
            "code": 503, # Service Unavailable
            "message": f"连接目标服务失败: {str(e)}",
            "result": None
        }), 503
        
    except Exception as e:
        # 捕获其他未知错误
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500