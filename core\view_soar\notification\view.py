#!/usr/bin/env python
# encoding:utf-8
from . import *
import uuid
import json
import re
from datetime import datetime
import hashlib
from core.model import Notification, Users
from core import db
import threading
import os
import requests
import mimetypes
from werkzeug.utils import secure_filename
from pathlib import Path
from urllib.parse import quote


# WebSocket连接管理
active_connections = {}  # {user_id: [ws1, ws2, ...]}
active_connections_lock = threading.RLock()

# 附件下载配置
LOCAL_FILE_DIRECTORY = "/home/<USER>/soar/apps/emailmsg/main/email_attachments/"  # 本地文件目录
S6000_DOWNLOAD_BASE_URL = "http://*************:6000/download/file"  # S6000下载服务地址

# 剧本条件映射配置
SCRIPT_CONDITIONS = {
    "s60000_pending": {
        "field": "processed_count",
        "datasfield": "results", # ["工作通知", "工作任务", "工作联系单", "预警单管理", "地市预警通告"],
        "type": "待处理数",
        "source": "S60000系统"
    },
    "email": {
        "field": "mails_count", 
        "datasfield": "mails",
        "type": "未读邮件",
        "source": "邮件系统"
    },
    "qianxin_ids": {
        "field": "total",
        "datasfield": "alerts",
        "type": "危急告警",
        "source": "奇安信天眼"
    },
    "nsfocus_scan": {
        "field": "total_tasks",
        "datasfield": "new_completed_tasks",
        "type": "扫描任务",
        "source": "绿盟漏扫"
    },
    "s60000_feedback": {
        "field": "processed_count",
        "datasfield": "results", # ["工作任务", "工作联系单", "预警单管理"],
        "type": "反馈截止",
        "source": "S60000系统"
    },
    "diy": {
        "field": "processed_count",
        "datasfield": "results",
        "type": "",
        "source": ""
    }
}


def process_file_url_for_notification(source, result_data):
    """
    为通知数据处理文件下载URL
    
    Args:
        source (str): 通知来源
        result_data (dict): 通知结果数据
    
    Returns:
        dict: 处理后的结果数据（如果需要添加file_url则会添加）
    """
    if not isinstance(result_data, dict):
        return result_data
    
    # 处理S60000系统的文件下载URL
    if source == "S60000系统" and "file_path" in result_data:
        file_path = result_data.get("file_path", "")
        if file_path:
            # 获取文件名（basename）
            file_path = file_path.split('\\')[-1]
            file_basename = os.path.basename(file_path)
            if file_basename:
                # 对文件名进行URL编码，处理中文和特殊字符
                encoded_filename = quote(file_basename, safe='')
                # 生成下载URL
                result_data["file_url"] = f"/api/v1/soar/notification/attachment/download?source={source}&filename={encoded_filename}"
    elif source == "邮件系统" and "attachments" in result_data:
        for i in range(len(result_data["attachments"])):
            file_path = result_data["attachments"][i].get("path", "")
            if file_path:
                # 获取文件名（basename）
                # file_basename = os.path.basename(file_path)
                if file_path:
                    # 对文件名进行URL编码，处理中文和特殊字符
                    encoded_filename = quote(file_path, safe='')
                    # 生成下载URL
                    result_data["attachments"][i]["file_url"] = f"http://10.228.18.115:28123/download/file/{encoded_filename}"
    
    return result_data


def generate_notifications_title(source, notification_type, count, timestamp=None):
    """生成通知标题"""
    if timestamp is None:
        timestamp = datetime.now()
    
    format_time = timestamp.strftime("%Y/%m/%d %H:%M")
    return f"{format_time}发现【{source}】存在{notification_type}({count})"


def generate_notification_title(source, notification_type, data, timestamp=None):
    """生成单个通知标题"""
    # if timestamp is None:
    #     timestamp = datetime.now()
    
    # format_time = timestamp.strftime("%Y/%m/%d %H:%M")
    if source == '邮件系统':
        return f"【{source}】{notification_type}-{data.get('subject', '')}"
    elif source == 'S60000系统':
        return f"【{source}】{notification_type}-{data.get('title', '')}"
    elif source == '奇安信天眼':
        return f"【{source}】存在{notification_type}-{data.get('rule_name', '')}"
    elif source == '绿盟漏扫':
        return f"【{source}】存在{notification_type}-{data.get('任务名称', '')}"
    elif source == '整改单':
        return f"【{source}】{notification_type}-{data.get('serial_number', '')+data.get('ip', '')}"
    return f"【{source}】存在{notification_type}"

def process_script_result(script_type, result_data):
    """处理剧本结果，判断是否需要通知"""
    """
      1.根据业务类型，解析相应的JSON
      2.把每次解析的通知相关的数据，存入mysql，需要先建表
      3.判断是否需要通知
    """
    if script_type not in SCRIPT_CONDITIONS:
        return False, None, None, None, None
    
    config = SCRIPT_CONDITIONS[script_type]
    field = config["field"]
    _type = config.get("type", "")

    count = result_data.get(field, 0)
    if count == 0:
        return False, None, None, None, None
    
    datas = result_data.get(config["datasfield"], [])
    if len(datas) == 0:
        return False, None, None, None, None
    
    # 处理不同类型的通知(特例)
    if script_type == "s60000_pending":
        # 处理S60000待办事项
        _datas = []
        for item_type in ["工作通知", "工作任务", "工作联系单", "预警单管理", "地市预警通告"]:
            if item_type in datas:
                for item in datas[item_type]:
                    item["_type"] = item_type
                    _datas.append(item)
        datas = _datas
    elif script_type == "s60000_feedback":
        # 处理S60000反馈截止事项
        _datas = []
        for item_type in ["工作任务", "工作联系单", "预警单管理"]:
            if item_type in datas:
                for item in datas[item_type]:
                    item["_type"] = item_type
                    _datas.append(item)
        datas = _datas
    return True, count, config["source"], config["type"], datas


def add_connection(user_id, ws):
    """添加WebSocket连接"""
    with active_connections_lock:
      if user_id not in active_connections:
          active_connections[user_id] = []
      active_connections[user_id].append(ws)
      active_connections_count = len(active_connections[user_id])
    logger.info(f"用户 {user_id} 建立WebSocket连接，当前连接数: {active_connections_count}")

def remove_connection(user_id, ws):
    """移除WebSocket连接"""
    with active_connections_lock:
      if user_id in active_connections:
          try:
              active_connections[user_id].remove(ws)
              if not active_connections[user_id]:  # 如果列表为空，删除用户
                  del active_connections[user_id]
              logger.info(f"用户 {user_id} 断开WebSocket连接")
          except ValueError:
              pass

def broadcast_to_user(user_id, message):
    """向指定用户的所有连接广播消息"""
    active_connections_copy = []
    with active_connections_lock:
      if user_id in active_connections:
        active_connections_copy = active_connections[user_id].copy()

    if not active_connections_copy:
      return
    
    logger.error(f"{user_id} - {message} - {active_connections_copy}")
    disconnected_connections = []
    for ws in active_connections_copy:
        try:
            if not ws.closed:
                logger.info(message)
                ws.send(json.dumps(message, ensure_ascii=False))
            else:
                disconnected_connections.append(ws)
        except Exception as e:
            logger.error(f"向用户 {user_id} 发送消息失败: {str(e)}")
            disconnected_connections.append(ws)
    
    # 清理断开的连接
    for ws in disconnected_connections:
        remove_connection(user_id, ws)

def broadcast_notification_to_user(user_id, notification_data):
    """向用户广播新通知"""
    message = {
        "type": "new_notification",
        "data": notification_data
    }
    broadcast_to_user(user_id, message)

def save_notification_to_mysql(user_id, notification_datas):
    """保存通知到数据库"""
    try:
        notification_ids = []
        new_notifications = []  # 存储新创建的通知，用于实时推送
        
        for data in notification_datas["datas"]:
            
            # 生成基于内容的唯一ID，避免重复通知
            content_hash = hashlib.md5(
                f"{notification_datas['source']}_{notification_datas['notification_type']}_{notification_datas['count']}_{str(data)}".encode()
            ).hexdigest()
            notification_id = content_hash
            
            # 检查是否已存在相同通知（使用 id 字段查询）
            existing_notification = Notification.where('notification_id', notification_id).first()
            
            if existing_notification:
                # 更新时间戳
                existing_notification.updated_at = datetime.now()
                existing_notification.save()
                notification_ids.append(notification_id)
                continue
            
            # 处理创建时间
            created_at = notification_datas.get("created_at")
            if isinstance(created_at, str):
                try:
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except:
                    created_at = datetime.now()
            elif not isinstance(created_at, datetime):
                created_at = datetime.now()
            
            # 创建新通知
            notification = Notification()
            notification.notification_id = notification_id  # 使用 id 作为主键
            notification.title = generate_notification_title(notification_datas["source"], notification_datas["notification_type"], data)
            notification.source = notification_datas["source"]
            notification.notification_type = notification_datas["notification_type"]
            notification.status = "unread"
            notification.created_at = created_at
            notification.updated_at = datetime.now()
            notification.result_data = json.dumps(data, ensure_ascii=False)
            notification.user_id = str(user_id)
            notification.execution_id = notification_datas.get("execution_id", "")
            
            notification.save()
            notification_ids.append(notification_id)
            
            # 处理文件URL（如果需要）
            # processed_data = process_file_url_for_notification(notification.source, data)
            
            # 准备实时推送的通知数据
            notification_data = {
                "notification_id": notification_id,
                "title": notification.title,
                "source": notification.source,
                "notification_type": notification.notification_type,
                "status": notification.status,
                "created_at": created_at.isoformat() if hasattr(created_at, 'isoformat') else str(created_at),
                # "result_data": processed_data
            }
            new_notifications.append(notification_data)
        
        # 实时推送新通知给用户
        if new_notifications:
            try:
                # 推送每个新通知
                for notification_data in new_notifications:
                    # notification_data["created_at"] = notification_data["created_at"]
                    broadcast_notification_to_user(user_id, notification_data)
                
                logger.info(f"已向用户 {user_id} 实时推送 {len(new_notifications)} 条新通知")
            except Exception as e:
                logger.error(f"实时推送通知失败: {str(e)}")
          
        return notification_ids
    except Exception as e:
        logger.error(f"保存通知到数据库异常: {str(e)}")
        return []


def get_user_notifications(user_id, page=1, limit=20, status="all", notification_id = None, notification_type = None, source = None, title = None, execution_id = None, created_at = None, detail = False):
    """获取用户通知列表"""
    try:
        # 参数验证
        page = max(1, int(page)) if str(page).isdigit() else 1
        limit = max(1, min(100, int(limit))) if str(limit).isdigit() else 20  # 限制最大100条
        
        if status not in ["all", "read", "unread"]:
            status = "all"
        
        # 构建查询
        query = Notification.where('user_id', str(user_id))
        
        # 状态过滤
        if status != "all":
            query = query.where('status', status)

        if notification_id:
            notification_ids = [t.strip() for t in notification_id.split(',') if t.strip()]
            if len(notification_ids) == 1:
                query = query.where('notification_id', notification_ids[0])
            elif len(notification_ids) > 1:
                query = query.where_in('notification_id', notification_ids)
        
        if notification_type:
            # 支持多选，以逗号分割
            notification_types = [t.strip() for t in notification_type.split(',') if t.strip()]
            if len(notification_types) == 1:
                query = query.where('notification_type', notification_types[0])
            elif len(notification_types) > 1:
                query = query.where_in('notification_type', notification_types)
        
        if source:
            # 支持多选，以逗号分割
            sources = [s.strip() for s in source.split(',') if s.strip()]
            if len(sources) == 1:
                query = query.where('source', sources[0])
            elif len(sources) > 1:
                query = query.where_in('source', sources)
        
        if title:
            query = query.where('title', 'like', f'%{title}%')

        if execution_id:
            query = query.where('execution_id', execution_id)

        if created_at:
            if len(created_at) == 2:
                try:
                    # 处理开始时间
                    start_time_str = created_at[0].strip()
                    if start_time_str:
                        # 支持多种时间格式
                        if 'T' in start_time_str:
                            # ISO格式: 2024-01-01T00:00:00 或 2024-01-01T00:00:00Z
                            start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                        else:
                            # 日期格式: 2024-01-01，自动补充时间为00:00:00
                            start_time = datetime.strptime(start_time_str, '%Y-%m-%d')
                        
                        # 处理结束时间
                        end_time_str = created_at[1].strip()
                        if end_time_str:
                            if 'T' in end_time_str:
                                # ISO格式: 2024-01-01T23:59:59 或 2024-01-01T23:59:59Z
                                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                            else:
                                # 日期格式: 2024-01-01，自动补充时间为23:59:59
                                end_time = datetime.strptime(end_time_str, '%Y-%m-%d')
                                end_time = end_time.replace(hour=23, minute=59, second=59, microsecond=999999)
                            
                            # 确保开始时间小于等于结束时间
                            if start_time <= end_time:
                                query = query.where('created_at', '>=', start_time).where('created_at', '<=', end_time)
                            else:
                                logger.warning(f"时间范围无效: 开始时间 {start_time} 大于结束时间 {end_time}")
                        else:
                            # 只有开始时间，查询从开始时间到现在
                            query = query.where('created_at', '>=', start_time)
                    else:
                        # 只有结束时间，查询从最早到结束时间
                        end_time_str = created_at[1].strip()
                        if end_time_str:
                            if 'T' in end_time_str:
                                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                            else:
                                end_time = datetime.strptime(end_time_str, '%Y-%m-%d')
                                end_time = end_time.replace(hour=23, minute=59, second=59, microsecond=999999)
                            query = query.where('created_at', '<=', end_time)
                except Exception as e:
                    logger.error(f"时间范围解析失败: {str(e)}, created_at: {created_at}")
                    # 时间解析失败时，忽略时间过滤条件，继续其他查询

        
        # 按创建时间倒序排列
        if not detail:
            query = query.select(
                'notification_type', 'notification_id', 'status', 'created_at', 'title', 'source'  # 只选择需要的字段
            ).order_by('created_at', 'desc')
        else:
            query = query.select(
                'notification_type', 'notification_id', 'status', 'created_at', 'title', 'source', 'result_data'  # 只选择需要的字段
            ).order_by('created_at', 'desc')
        
        # 计算总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * limit
        notifications_data = query.offset(offset).limit(limit).get()
        
        # 转换为字典格式
        notifications = []
        for notification in notifications_data:
            try:
                data = {
                    "notification_id": notification.notification_id,  # 使用 id 字段
                    "title": notification.title or "",
                    "source": notification.source or "",
                    "notification_type": notification.notification_type or "",
                    "status": notification.status or "unread",
                    "created_at": notification.created_at.strftime("%Y-%m-%d %H:%M:%S") if hasattr(notification.created_at, 'strftime') else str(notification.created_at)
                }

                if hasattr(notification, 'result_data') and notification.result_data:
                    # 安全地解析 result_data
                    result_data = notification.result_data
                    if isinstance(result_data, str):
                        try:
                            result_data = json.loads(result_data)
                        except:
                            result_data = {}
                    else:
                        result_data = {}
                    # 处理文件URL（如果需要）
                    result_data = process_file_url_for_notification(notification.source, result_data)
                    data["result_data"] = result_data
                
                notifications.append(data)
            except Exception as e:
                logger.error(f"处理通知数据异常: {str(e)}")
                continue
        
        return {
            "total": total,
            "page": page,
            "limit": limit,
            "notifications": notifications
        }
    except Exception as e:
        logger.error(f"获取用户通知列表异常: {str(e)}")
        return {
            "total": 0,
            "page": page,
            "limit": limit,
            "notifications": []
        }


@r.route("/notification/process", methods=['POST'])
def notification_process():
    """
    消息处理接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 剧本执行结果处理
        schema:
          type: object
          required:
            - script_type
            - result
          properties:
            script_type:
              type: string
              description: 剧本类型
            result:
              type: object
              description: 业务APP的JSON返回结果
            execution_id:
              type: string
              description: 剧本执行ID
            timestamp:
              type: string
              description: 执行时间戳
            notification_type:
              type: string
              description: 通知类型
            title:
              type: string
              description: 通知标题
            source:
              type: string
              description: 通知来源
    responses:
      200:
        description: 处理成功
      400:
        description: 参数错误
      500:
        description: 处理失败
    """
    try:
        # 参数验证
        if not request.json:
            return Response.re(err=Err)
            
        script_type = request.json.get("script_type", "").strip()
        result_data = request.json.get("result", {})
        execution_id = request.json.get("execution_id", "").strip()
        timestamp_str = request.json.get("timestamp", "").strip()
        _notification_type = request.json.get("notification_type", "").strip()
        _title = request.json.get("title", "").strip()
        _source = request.json.get("source", "").strip()
        
        if not script_type or not result_data:
            return Response.re(err=Err)
        
        # 解析时间戳
        timestamp = datetime.now()
        if timestamp_str:
            try:
                # 支持多种时间戳格式
                if timestamp_str.endswith('Z'):
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                else:
                    timestamp = datetime.fromisoformat(timestamp_str)
            except Exception as e:
                logger.warning(f"时间戳解析失败，使用当前时间: {str(e)}")
                timestamp = datetime.now()
        
        # 处理剧本结果
        need_notification, count_or_list, source, notification_type, datas = process_script_result(script_type, result_data)
        if script_type == "diy":
            notification_type = _notification_type or "diy"
            source = _source or "diy"

        if not need_notification:
            return Response.re(data={
                "need_notification": False,
                "message": "无需通知"
            })
        
        # 获取当前用户ID（从token中解析）
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 处理单通知情况
        title = generate_notifications_title(source, notification_type, count_or_list, timestamp)
        if script_type == "diy":
            title = _title or title

        notification_datas = {
            "title": title,
            "source": source,
            "notification_type": notification_type,
            "count": count_or_list,
            "created_at": timestamp,  # 直接传递 datetime 对象
            "datas": datas,
            "execution_id": execution_id
        }
        
        # 接收返回的通知ID列表
        notification_ids = save_notification_to_mysql(user_id, notification_datas)
        
        if not notification_ids:
            logger.error("保存通知失败")
            return Response.re(err=Err)
        
        return Response.re(data={
            "need_notification": True,
            "notification_ids": notification_ids,
            "notification_type": notification_type,
            "count": count_or_list,
            "source": source,
            "title": title,
            "created_at": timestamp.isoformat()
        })
        
    except Exception as e:
        import traceback
        # 获取请求参数用于错误日志
        request_params = {}
        try:
            if request.json:
                request_params = {
                    "script_type": request.json.get("script_type", ""),
                    "execution_id": request.json.get("execution_id", ""),
                    "timestamp": request.json.get("timestamp", ""),
                    "notification_type": request.json.get("notification_type", ""),
                    "title": request.json.get("title", ""),
                    "source": request.json.get("source", ""),
                    "result_data_keys": list(request.json.get("result", {}).keys()) if isinstance(request.json.get("result"), dict) else "non-dict"
                }
        except:
            request_params = {"error": "无法解析请求参数"}
        
        # 记录详细的错误信息
        logger.error(f"通知处理异常: {str(e)}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"请求参数: {request_params}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        
        return Response.re(err=Err)


@r.route("/notification/list", methods=['POST'])
def notification_list():
    """
    通知列表查询接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 查询参数
        schema:
          type: object
          properties:
            page:
              type: integer
              description: 页码
              default: 1
            limit:
              type: integer
              description: 每页数量
              default: 20
            status:
              type: string
              description: 状态过滤
              enum: ["all", "read", "unread"]
              default: "all"
            notification_id:
              type: string
              description: 通知ID过滤，支持多选，多个值用逗号分割
            notification_type:
              type: string
              description: 通知类型过滤，支持多选，多个值用逗号分割
            source:
              type: string
              description: 通知来源过滤，支持多选，多个值用逗号分割
            title:
              type: string
              description: 标题关键词过滤
            detail:
              type: boolean
              description: 详情是否展示
            execution_id:
              type: string
              description: 执行ID过滤
            created_at:
              type: string
              description: 创建时间范围过滤，格式为"开始时间~结束时间"。支持格式：1) 日期格式："2024-01-01~2024-01-31" 2) ISO格式："2024-01-01T00:00:00~2024-01-31T23:59:59" 3) 带时区："2024-01-01T00:00:00Z~2024-01-31T23:59:59Z" 4) 单边范围："2024-01-01~"(从指定时间到现在) 或 "~2024-01-31"(从最早到指定时间)
              example: "2024-01-01~2024-01-31"
    responses:
      200:
        description: 查询成功
      400:
        description: 参数错误
      500:
        description: 查询失败
    """
    try:
        # 参数验证和默认值处理
        request_data = request.json or {}
        page = request_data.get("page", 1)
        limit = request_data.get("limit", 20)
        status = request_data.get("status", "all")
        notification_id = request_data.get("notification_id", "").strip()
        notification_type = request_data.get("notification_type", "").strip()
        source = request_data.get("source", "").strip()
        title = request_data.get("title", "").strip()
        execution_id = request_data.get("execution_id", "").strip()
        created_at = request_data.get("created_at", "").strip()
        detail = request_data.get("detail", False)
        if created_at:
            created_at = created_at.split("~")
            if len(created_at) == 2:
                created_at = [created_at[0], created_at[1]]
            else:
                created_at = None
        
        # 获取当前用户ID
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 获取通知列表
        result = get_user_notifications(user_id, page, limit, status, notification_id, notification_type, source, title, execution_id, created_at, detail)
        
        return Response.re(data=result)
        
    except Exception as e:
        logger.error(f"通知列表查询异常: {str(e)}")
        return Response.re(err=Err)

@r.route("/notification/show", methods=['POST'])
def notification_show():
    """
    通知列表查询接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 查询参数
        schema:
          type: object
          properties:
            notification_id:
              type: string
              description: 通知ID过滤
    responses:
      200:
        description: 查询成功
      400:
        description: 参数错误
      500:
        description: 查询失败
    """
    try:
        # 参数验证和默认值处理
        request_data = request.json or {}
        notification_id = request_data.get("notification_id", "").strip()
        if not notification_id:
            return Response.re(err=ErrParam)
        
        # 获取当前用户ID
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 获取通知列表
        # 构建查询
        query = Notification.where('user_id', str(user_id)).where('notification_id', notification_id)
        # 执行查询
        notification = query.first()
        if not notification:
            return Response.re(err=ErrParam)

        # 安全地解析 result_data
        try:
            result_data = notification.result_data
            if isinstance(result_data, str):
                result_data = json.loads(result_data)
            elif result_data is None:
                result_data = {}
        except Exception as e:
            logger.error(f"解析通知详细数据异常: {str(e)}")
            result_data = {}

        notification.result_data = process_file_url_for_notification(notification.source, result_data)
        return Response.re(data=notification.serialize())
        
    except Exception as e:
        logger.error(f"通知详情查询异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/notification/count", methods=['POST'])
def notification_count():
    """
    通知列表查询接口
    ---
    consumes:
      - application/json
      parameters:
      - in: body
        name: body
        required: true
        description: 查询参数
        schema:
          type: object
          properties:
            status:
              type: string
              description: 通知状态过滤
    responses:
      200:
        description: 查询成功
      400:
        description: 参数错误
      500:
        description: 查询失败
    """
    try:    
        # 获取当前用户ID
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)

        request_data = request.json or {}
        status = request_data.get("status", "unread")
        
        # 获取通知未读数量
        # 构建查询
        query = Notification.where('user_id', str(user_id)) \
                   .where('status', status) \
                   .group_by('notification_type') \
                   .select(
                       'notification_type',
                       db.raw('COUNT(*) as count')  # 使用 db.raw() 计算 COUNT
                   )

        # 执行查询
        result = query.get()  # 返回一个 Collection 对象
        if result is None:
            return Response.re(err=ErrParam)
        data = {}
        for item in result:
            data[item.notification_type] = item.count
        data["total"] = sum(data.values())
        return Response.re(data=data)
        
    except Exception as e:
        logger.error(f"通知未读数量查询异常: {str(e)}")
        return Response.re(err=Err)


@ws.route('/notification/ws')
def notification_ws(ws):
    added = False
    while not ws.closed:
        # 接收发送过来的消息
        message = ws.receive()
        if message:
            req_data = json.loads(message)
            method = req_data.get("method", "").strip()
            token = req_data.get("token", "").strip()
            page = req_data.get("page", 1)
            limit = req_data.get("limit", 50)
            status = req_data.get("status", "").strip()
            notification_id = req_data.get("notification_id", "").strip()
            notification_type = req_data.get("notification_type", "").strip()
            source = req_data.get("source", "").strip()
            title = req_data.get("title", "").strip()
            execution_id = req_data.get("execution_id", "").strip()
            created_at = req_data.get("created_at", "").strip()
            if created_at:
                created_at = created_at.split("~")
                if len(created_at) == 2:
                    created_at = [created_at[0], created_at[1]]
                else:
                    created_at = None

            if status not in ["all", "read", "unread"]:
                status = "all"
            if method == "ping":
                pass
            elif method == "get_notification_list":
                if not token:
                    ws.send(json.dumps({"error": "Token is required"}))
                    continue
                user_id = redis.get(token)
                if not user_id:
                    ws.send(json.dumps({"error": "Invalid token"}))
                    continue
                result = get_user_notifications(user_id, page, limit, status, notification_id, notification_type, source, title, execution_id, created_at)
                if result["total"] > 0:
                    ws.send(json.dumps({
                      "type": "new_notification",
                      "data": result["notifications"]
                    }))
                if not added:
                  add_connection(user_id, ws)
                  added = True


                # broadcast_to_user(b'1', "{aaaa}")
    else:
        remove_connection(user_id, ws)
                
                
                


@r.route("/notification/status", methods=['PUT'])
def notification_status():
    """
    通知状态更新接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 状态更新参数
        schema:
          type: object
          required:
            - notification_id
            - status
          properties:
            notification_id:
              type: string
              description: 通知ID
            status:
              type: string
              description: 新状态
              enum: ["read", "unread"]
    responses:
      200:
        description: 更新成功
      400:
        description: 参数错误
      404:
        description: 通知不存在
      500:
        description: 更新失败
    """
    try:
        # 参数验证
        if not request.json:
            return Response.re(err=Err)
            
        notification_id = request.json.get("notification_id", "").strip()
        status = request.json.get("status", "").strip()

        if not notification_id or status not in ["read", "unread"]:
            return Response.re(err=Err)

        # 获取当前用户ID
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)

        # 检查通知是否存在且属于当前用户
        notification = Notification.where('notification_id', notification_id).where('user_id', str(user_id)).first()
        
        if not notification:
            return Response.re(err=Err)
        
        old_status = notification.status

        # 如果状态没有变化，直接返回
        if old_status == status:
            return Response.re(data={
                "notification_id": notification_id,
                "status": status,
                "updated_at": notification.updated_at.isoformat() if hasattr(notification.updated_at, 'isoformat') else str(notification.updated_at)
            })

        # 更新状态
        notification.status = status
        notification.updated_at = datetime.now()
        notification.save()

        # 更新未读计数
        try:
            if old_status == "unread" and status == "read":
                # 减少未读计数
                user = Users.where('id', str(user_id)).first()
                if user and hasattr(user, 'unread_count') and user.unread_count > 0:
                    user.unread_count = user.unread_count - 1
                    user.save()
            elif old_status == "read" and status == "unread":
                # 增加未读计数
                user = Users.where('id', str(user_id)).first()
                if user:
                    if not hasattr(user, 'unread_count') or user.unread_count is None:
                        user.unread_count = 1
                    else:
                        user.unread_count = user.unread_count + 1
                    user.save()
        except Exception as e:
            logger.error(f"更新用户未读计数异常: {str(e)}")

        return Response.re(data={
            "notification_id": notification_id,
            "status": status,
            "updated_at": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"通知状态更新异常: {str(e)}")
        return Response.re(err=Err)


@r.route("/notification/dialog", methods=['POST'])
def notification_dialog():
    """TODO: 根据模型实际的对话生成接口，进行调整"""
    """
    通知对话生成接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 对话生成参数
        schema:
          type: object
          required:
            - notification_id
          properties:
            notification_id:
              type: string
              description: 通知ID
            user_question:
              type: string
              description: 用户问题
    responses:
      200:
        description: 对话生成成功
      400:
        description: 参数错误
      404:
        description: 通知不存在
      500:
        description: 对话生成失败
    """
    try:
        # 参数验证
        if not request.json:
            return Response.re(err=Err)
            
        notification_id = request.json.get("notification_id", "").strip()
        user_question = request.json.get("user_question", "").strip()

        if not notification_id:
            return Response.re(err=Err)

        # 获取当前用户ID
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)

        # 获取通知详情
        notification = Notification.where('notification_id', notification_id).where('user_id', str(user_id)).first()
        
        if not notification:
            return Response.re(err=Err)

        # 安全地解析 result_data
        try:
            result_data = notification.result_data
            if isinstance(result_data, str):
                result_data = json.loads(result_data)
            elif result_data is None:
                result_data = {}
        except Exception as e:
            logger.error(f"解析通知详细数据异常: {str(e)}")
            result_data = {}

        # 尝试从标题中提取数量信息
        count = 0
        if notification.title:
            count_match = re.search(r'\((\d+)\)', notification.title)
            if count_match:
                count = int(count_match.group(1))

        # 构造对话上下文
        context_info = {
            "通知标题": notification.title or "",
            "来源系统": notification.source or "",
            "通知类型": notification.notification_type or "",
            "数量": count,
            "创建时间": notification.created_at,
            "详细数据": result_data
        }

        # 生成系统提示词
        system_prompt = f"""你是一个安全运营助手，正在帮助用户分析以下安全通知：

通知信息：
- 标题：{context_info['通知标题']}
- 来源：{context_info['来源系统']}
- 类型：{context_info['通知类型']}
- 数量：{context_info['数量']}
- 时间：{context_info['创建时间']}

详细数据：{json.dumps(context_info['详细数据'], ensure_ascii=False, indent=2)}

请基于以上信息，为用户提供专业的安全分析和建议。如果用户没有具体问题，请主动分析这个安全事件的重要性、可能的影响和建议的处理步骤。"""

        # 构造对话内容
        conversation = [
            {
                "role": "system",
                "content": system_prompt,
                "timestamp": datetime.now().isoformat()
            }
        ]

        # 如果用户有问题，添加用户消息
        if user_question:
            conversation.append({
                "role": "user",
                "content": user_question,
                "timestamp": datetime.now().isoformat()
            })

            # 这里可以调用现有的大模型接口生成回复
            # 暂时返回一个示例回复
            ai_response = f"基于您的通知「{notification.title}」，我建议您立即关注此{notification.notification_type}。具体分析和处理建议请查看详细数据。"

            conversation.append({
                "role": "assistant",
                "content": ai_response,
                "timestamp": datetime.now().isoformat()
            })

        # 生成对话ID
        dialog_id = str(uuid.uuid4())

        return Response.re(data={
            "dialog_id": dialog_id,
            "conversation": conversation,
            "context": {
                "notification_info": {
                    "notification_id": notification_id,
                    "title": notification.title or "",
                    "source": notification.source or "",
                    "notification_type": notification.notification_type or "",
                    "count": count,
                    "created_at": notification.created_at.isoformat() if hasattr(notification.created_at, 'isoformat') else str(notification.created_at)
                },
                "result_data": result_data
            }
        })

    except Exception as e:
        logger.error(f"通知对话生成异常: {str(e)}")
        return Response.re(err=Err)


def is_safe_path(base_path, file_path):
    """检查文件路径是否安全，防止目录遍历攻击"""
    try:
        base_path = Path(base_path).resolve()
        file_path = Path(base_path / file_path).resolve()
        return str(file_path).startswith(str(base_path))
    except Exception:
        return False


@r.route("/notification/attachment/download", methods=['GET'])
def download_attachment():
    """
    附件下载接口
    ---
    parameters:
      - in: query
        name: source
        required: true
        type: string
        description: 文件来源 (邮件系统 或 S60000系统)
      - in: query
        name: filename
        required: true
        type: string
        description: 文件名
    responses:
      200:
        description: 文件下载成功
      400:
        description: 参数错误
      404:
        description: 文件不存在
      500:
        description: 下载失败
    """
    try:
        # 参数验证
        source = request.args.get('source', '').strip()
        filename = request.args.get('filename', '').strip()
        
        if not source or not filename:
            return Response.re(ErrMsg(400, "参数source和filename不能为空"))
        
        if source not in ['邮件系统', 'S60000系统']:
            return Response.re(ErrMsg(400, "source参数必须是邮件系统或S60000系统"))
        
        # 获取当前用户ID（验证权限）
        token = request.headers.get('token', '').strip()
        if not token:
            return Response.re(err=ErrToken)
            
        user_id = redis.get(token)
        if not user_id:
            return Response.re(err=ErrToken)
        
        # 安全文件名处理
        safe_filename = secure_filename(filename)
        if not safe_filename:
            return Response.re(ErrMsg(400, "非法的文件名"))
        
        logger.info(f"用户 {user_id} 请求下载附件: source={source}, filename={filename}")
        
        if source == 'S60000系统':
            # S6000来源：代理到后端服务
            return download_from_s6000(safe_filename)
        elif source == '邮件系统':
            # 邮件系统来源：读取本地文件
            return download_from_local(safe_filename)
            
    except Exception as e:
        logger.error(f"附件下载异常: {str(e)}")
        return Response.re(ErrMsg(500, "下载失败"))


def download_from_s6000(filename):
    """从S6000服务下载文件"""
    try:
        # 构造S6000下载URL
        download_url = f"{S6000_DOWNLOAD_BASE_URL}/{filename}"
        
        # 处理Range请求（断点续传）
        headers = {}
        range_header = request.headers.get('Range')
        if range_header:
            headers['Range'] = range_header
        
        logger.info(f"代理下载S6000文件: {download_url}")
        
        # 发起流式请求到S6000服务
        response = requests.get(download_url, headers=headers, stream=True, timeout=30)
        
        if response.status_code == 404:
            return Response.re(ErrMsg(404, "文件不存在"))
        elif response.status_code == 416:
            # Range Not Satisfiable
            from flask import Response as FlaskResponse
            return FlaskResponse(status=416)
        elif response.status_code not in [200, 206]:
            return Response.re(ErrMsg(500, f"S6000服务返回错误: {response.status_code}"))
        
        # 构造流式响应
        def generate():
            try:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        yield chunk
            except Exception as e:
                logger.error(f"流式传输异常: {str(e)}")
            finally:
                response.close()
        
        # 获取响应头
        content_type = response.headers.get('Content-Type', 'application/octet-stream')
        content_length = response.headers.get('Content-Length')
        content_range = response.headers.get('Content-Range')
        
        # 构造Flask响应
        from flask import Response as FlaskResponse
        flask_response = FlaskResponse(
            generate(),
            status=response.status_code,
            headers={
                'Content-Type': content_type,
                'Accept-Ranges': 'bytes',
                'Content-Disposition': f'attachment; filename="{secure_filename(filename)}"'
            }
        )
        
        if content_length:
            flask_response.headers['Content-Length'] = content_length
        if content_range:
            flask_response.headers['Content-Range'] = content_range
            
        logger.info(f"S6000文件下载开始: {filename}, 状态码: {response.status_code}")
        return flask_response
        
    except requests.exceptions.RequestException as e:
        logger.error(f"S6000服务请求异常: {str(e)}")
        return Response.re(ErrMsg(500, "无法连接到S6000服务"))
    except Exception as e:
        logger.error(f"S6000文件下载异常: {str(e)}")
        return Response.re(ErrMsg(500, "下载失败"))


def download_from_local(filename):
    """从本地文件目录下载文件"""
    try:
        # 安全检查
        if not is_safe_path(LOCAL_FILE_DIRECTORY, filename):
            return Response.re(ErrMsg(400, "非法的文件路径"))
        
        file_path = os.path.join(LOCAL_FILE_DIRECTORY, filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return Response.re(ErrMsg(404, "文件不存在"))
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        
        # 处理Range请求（断点续传）
        range_header = request.headers.get('Range')
        if range_header:
            # 解析Range头
            byte_start = 0
            byte_end = file_size - 1
            
            if range_header.startswith('bytes='):
                range_match = range_header[6:].split('-')
                if len(range_match) == 2:
                    if range_match[0]:
                        byte_start = int(range_match[0])
                    if range_match[1]:
                        byte_end = int(range_match[1])
            
            # 验证范围
            if byte_start >= file_size or byte_end >= file_size or byte_start > byte_end:
                from flask import Response as FlaskResponse
                return FlaskResponse(status=416)  # Range Not Satisfiable
            
            content_length = byte_end - byte_start + 1
            
            def generate_partial_content():
                with open(file_path, 'rb') as f:
                    f.seek(byte_start)
                    remaining = content_length
                    while remaining:
                        chunk_size = min(8192, remaining)  # 8KB chunks
                        chunk = f.read(chunk_size)
                        if not chunk:
                            break
                        remaining -= len(chunk)
                        yield chunk
            
            # 获取MIME类型
            mimetype = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
            from flask import Response as FlaskResponse
            response = FlaskResponse(
                generate_partial_content(),
                206,  # Partial Content
                headers={
                    'Content-Type': mimetype,
                    'Content-Length': str(content_length),
                    'Content-Range': f'bytes {byte_start}-{byte_end}/{file_size}',
                    'Accept-Ranges': 'bytes',
                    'Content-Disposition': f'attachment; filename="{secure_filename(filename)}"'
                }
            )
            
            logger.info(f"本地文件断点下载: {filename}, 范围: {byte_start}-{byte_end}/{file_size}")
            return response
        
        else:
            # 完整文件下载（流式传输）
            def generate_full_content():
                with open(file_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)  # 8KB chunks
                        if not chunk:
                            break
                        yield chunk
            
            # 获取MIME类型
            mimetype = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
            from flask import Response as FlaskResponse
            response = FlaskResponse(
                generate_full_content(),
                200,
                headers={
                    'Content-Type': mimetype,
                    'Content-Length': str(file_size),
                    'Accept-Ranges': 'bytes',
                    'Content-Disposition': f'attachment; filename="{secure_filename(filename)}"'
                }
            )
            
            logger.info(f"本地文件完整下载: {filename}, 大小: {file_size} bytes")
            return response
            
    except Exception as e:
        logger.error(f"本地文件下载异常: {str(e)}")
        return Response.re(ErrMsg(500, "下载失败"))



# notifications = Notification.all()
# for notification in notifications:
    
#     # if '-' not in notification.title:
#     try:
#         result_data = notification.result_data
#         if isinstance(result_data, str):
#             result_data = json.loads(result_data)
#         elif result_data is None:
#             result_data = {}
#     except Exception as e:
#         logger.error(f"解析通知详细数据异常: {str(e)}")
#         result_data = {}
#     notification.title = generate_notification_title(notification.source, notification.notification_type, result_data, notification.created_at)
#     notification.save()
