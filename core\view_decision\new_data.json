[{"event_id": "ea014", "event_name": "SQL注入攻击", "maintenance_method": "数据库防火墙配置, 输入验证", "check_item": "应用日志检查, SQL语句审计", "device_software_id": "DBFW-1.0, WAF-3.1", "vendor_name": "<PERSON>endor L, Vendor M", "harm_name": "数据库泄露, 数据篡改", "description": "检测到应用程序存在SQL注入攻击行为。", "prevention_measures": "启用参数化查询，开启数据库防火墙。", "attack_cause": "输入未过滤直接拼接SQL语句。", "defective_device_software": "WAF-3.1", "configuration_solution": "1. 开启SQL防注入规则。 2. 所有数据库访问使用预编译语句。"}, {"event_id": "ea015", "event_name": "勒索软件感染", "maintenance_method": "数据备份, 行为监控", "check_item": "文件加密检测, 异常进程分析", "device_software_id": "EDR-2.0, AV-5.0", "vendor_name": "Vendor N", "harm_name": "文件加密, 数据丢失", "description": "发现终端设备遭到勒索软件加密文件。", "prevention_measures": "定期进行离线备份，部署EDR系统。", "attack_cause": "用户点击钓鱼邮件中的恶意附件。", "defective_device_software": "EDR-2.0", "configuration_solution": "1. 隔离感染终端。 2. 恢复备份数据并更新补丁。"}, {"event_id": "ea016", "event_name": "弱口令暴力破解", "maintenance_method": "账户安全策略, 登录异常监控", "check_item": "登录失败次数统计, 异常IP来源分析", "device_software_id": "SIEM-2.0, FW-3.5", "vendor_name": "<PERSON><PERSON><PERSON> O, Vendor P", "harm_name": "账号被盗, 非法访问", "description": "发现短时间内大量登录失败，怀疑暴力破解攻击。", "prevention_measures": "启用强密码策略，增加多因素认证。", "attack_cause": "系统未强制密码复杂度。", "defective_device_software": "SIEM-2.0", "configuration_solution": "1. 启用登录失败自动锁定。 2. 对异常IP进行阻断。"}, {"event_id": "ea017", "event_name": "供应链攻击", "maintenance_method": "软件供应链安全验证, 签名校验", "check_item": "软件更新源校验, 依赖包完整性检查", "device_software_id": "SCM-1.0, AV-5.2", "vendor_name": "<PERSON><PERSON><PERSON>", "harm_name": "后门植入, 系统被控", "description": "检测到第三方软件更新包被篡改，存在恶意代码。", "prevention_measures": "所有更新必须通过签名校验，建立可信源。", "attack_cause": "下载了被篡改的第三方库文件。", "defective_device_software": "SCM-1.0", "configuration_solution": "1. 使用代码签名验证更新包。 2. 部署软件供应链安全审计系统。"}, {"event_id": "ea018", "event_name": "云存储数据泄露", "maintenance_method": "访问权限控制, 加密传输", "check_item": "云端访问日志分析, 加密状态检查", "device_software_id": "CSP-2.0, DLP-2.1", "vendor_name": "Vendor R, Vendor S", "harm_name": "敏感数据外泄, 合规风险", "description": "云存储中存在未授权访问和外部共享链接。", "prevention_measures": "开启云数据加密，限制外部共享。", "attack_cause": "错误的权限配置导致外部可访问。", "defective_device_software": "CSP-2.0", "configuration_solution": "1. 所有云存储桶需关闭公开访问。 2. 开启敏感数据访问告警。"}, {"event_id": "ea019", "event_name": "APT攻击", "maintenance_method": "威胁情报分析, 持续监控", "check_item": "入侵检测日志, C2通信分析", "device_software_id": "IDS-2.0, TI-1.0", "vendor_name": "Vend<PERSON> T", "harm_name": "长期潜伏, 敏感信息外泄", "description": "检测到APT组织利用零日漏洞进行渗透。", "prevention_measures": "部署威胁情报平台，及时修复漏洞。", "attack_cause": "系统存在未修复的零日漏洞。", "defective_device_software": "IDS-2.0", "configuration_solution": "1. 启用C2流量检测规则。 2. 定期修复已知漏洞。"}, {"event_id": "ea020", "event_name": "内部人员越权访问", "maintenance_method": "零信任架构, 权限分级管理", "check_item": "用户行为分析, 审计日志检查", "device_software_id": "UAM-1.0, SIEM-2.0", "vendor_name": "Vendor U, Vendor V", "harm_name": "数据窃取, 内部信息泄漏", "description": "员工在未授权情况下访问敏感信息。", "prevention_measures": "实施最小权限原则，启用用户行为监控。", "attack_cause": "权限配置不当，缺乏访问审计。", "defective_device_software": "UAM-1.0", "configuration_solution": "1. 启用零信任访问控制。 2. 建立详细的访问审计机制。"}, {"event_id": "ea021", "event_name": "IoT设备被劫持", "maintenance_method": "固件更新, 默认口令清理", "check_item": "设备通信日志检查, 异常流量分析", "device_software_id": "IoT-1.0, FW-3.2", "vendor_name": "<PERSON><PERSON><PERSON> W", "harm_name": "设备被控, 僵尸网络组成", "description": "检测到大量IoT摄像头被远程劫持用于DDoS攻击。", "prevention_measures": "更新固件，关闭默认口令，启用防火墙。", "attack_cause": "设备出厂未修改默认密码。", "defective_device_software": "IoT-1.0", "configuration_solution": "1. 强制修改出厂默认密码。 2. 屏蔽不必要的远程管理端口。"}, {"event_id": "ea022", "event_name": "DNS劫持攻击", "maintenance_method": "DNS安全配置, 加密传输", "check_item": "DNS请求日志, 域名解析完整性检查", "device_software_id": "DNSSEC-1.0, FW-3.0", "vendor_name": "Vendor X", "harm_name": "用户流量重定向, 钓鱼网站", "description": "检测到内部用户DNS请求被劫持至恶意IP。", "prevention_measures": "启用DNSSEC，部署可信DNS服务器。", "attack_cause": "DNS解析未启用安全验证。", "defective_device_software": "DNSSEC-1.0", "configuration_solution": "1. 启用DNSSEC签名验证。 2. 配置内网强制使用可信DNS。"}, {"event_id": "ea023", "event_name": "边界路由器漏洞利用", "maintenance_method": "补丁管理, 漏洞扫描", "check_item": "设备固件版本检查, 路由器日志审计", "device_software_id": "RTR-1.5, IDS-2.1", "vendor_name": "<PERSON><PERSON><PERSON>", "harm_name": "网络中断, 非法访问", "description": "攻击者利用边界路由器固件漏洞获取控制权。", "prevention_measures": "定期更新固件，启用入侵检测。", "attack_cause": "使用了存在已知漏洞的旧固件。", "defective_device_software": "RTR-1.5", "configuration_solution": "1. 升级固件到最新版本。 2. 开启入侵防御功能。"}, {"event_id": "ea024", "event_name": "无线网络入侵", "maintenance_method": "Wi-<PERSON>安全配置, 加密协议升级", "check_item": "接入点日志检查, 异常SSID检测", "device_software_id": "WLAN-2.0, IDS-1.5", "vendor_name": "<PERSON><PERSON><PERSON>", "harm_name": "非法接入, 数据嗅探", "description": "发现未经授权的设备连接到企业Wi-Fi。", "prevention_measures": "启用WPA3，关闭WPS功能。", "attack_cause": "无线接入点使用了弱加密协议。", "defective_device_software": "WLAN-2.0", "configuration_solution": "1. 切换到WPA3加密。 2. 开启MAC地址白名单。"}, {"event_id": "ea025", "event_name": "智能合约漏洞利用", "maintenance_method": "代码审计, 合约测试", "check_item": "区块链交易日志, 合约调用检查", "device_software_id": "BC-1.0, SCAN-2.0", "vendor_name": "Vendor AA", "harm_name": "资产被盗, 财务损失", "description": "攻击者利用智能合约重入漏洞窃取资金。", "prevention_measures": "进行安全代码审计，增加合约测试用例。", "attack_cause": "合约未做重入保护。", "defective_device_software": "BC-1.0", "configuration_solution": "1. 在合约中使用互斥锁。 2. 引入第三方合约安全工具。"}, {"event_id": "ea026", "event_name": "工控系统异常登录", "maintenance_method": "远程访问限制, 多因子认证", "check_item": "SCADA日志, 用户权限检查", "device_software_id": "ICS-3.0, SIEM-2.5", "vendor_name": "Vendor BB", "harm_name": "生产线停工, 安全事故", "description": "工控系统检测到异常远程登录活动。", "prevention_measures": "限制外网访问工控系统，启用MFA。", "attack_cause": "工控系统开放了远程管理端口。", "defective_device_software": "ICS-3.0", "configuration_solution": "1. 关闭不必要的远程端口。 2. 启用多因子认证。"}, {"event_id": "ea027", "event_name": "云API密钥泄露", "maintenance_method": "密钥轮换, 最小权限", "check_item": "云审计日志, API调用检查", "device_software_id": "CSP-3.0, DLP-2.2", "vendor_name": "Vendor CC", "harm_name": "未授权访问, 云资源滥用", "description": "云环境API密钥泄露，攻击者利用进行资源挖矿。", "prevention_measures": "定期轮换API密钥，限制访问权限。", "attack_cause": "密钥存储在公共代码仓库。", "defective_device_software": "CSP-3.0", "configuration_solution": "1. 使用环境变量存储密钥。 2. 开启云平台的异常使用告警。"}, {"event_id": "ea028", "event_name": "跨租户数据访问", "maintenance_method": "云多租户隔离, 权限分离", "check_item": "访问控制检查, 虚拟化日志审查", "device_software_id": "CSP-4.0, VM-2.0", "vendor_name": "<PERSON><PERSON><PERSON>", "harm_name": "数据越权, 合规风险", "description": "检测到不同租户之间的数据访问异常。", "prevention_measures": "启用租户隔离策略，定期进行安全审计。", "attack_cause": "虚拟化层隔离不完善。", "defective_device_software": "VM-2.0", "configuration_solution": "1. 强化虚拟机隔离机制。 2. 实施租户级别的访问控制。"}, {"event_id": "ea029", "event_name": "浏览器零日漏洞利用", "maintenance_method": "安全补丁, 沙箱机制", "check_item": "浏览器版本检查, 漏洞利用检测", "device_software_id": "BRW-1.0, EDR-3.1", "vendor_name": "Vendor EE", "harm_name": "恶意代码执行, 用户信息泄露", "description": "攻击者通过浏览器零日漏洞执行恶意代码。", "prevention_measures": "及时更新浏览器，启用沙箱模式。", "attack_cause": "浏览器未及时打补丁。", "defective_device_software": "BRW-1.0", "configuration_solution": "1. 开启自动更新机制。 2. 使用安全沙箱隔离浏览器进程。"}, {"event_id": "ea030", "event_name": "AI模型中毒攻击", "maintenance_method": "训练数据验证, 模型完整性校验", "check_item": "训练数据源审查, 模型文件哈希比对", "device_software_id": "AI-1.0, DLP-3.0", "vendor_name": "Vendor FF", "harm_name": "模型预测失真, 决策误导", "description": "检测到AI模型训练数据被投毒。", "prevention_measures": "建立可信数据源，启用模型校验机制。", "attack_cause": "训练数据未经过完整性验证。", "defective_device_software": "AI-1.0", "configuration_solution": "1. 对数据集进行哈希校验。 2. 部署模型完整性监控。"}, {"event_id": "ea031", "event_name": "移动终端越狱检测", "maintenance_method": "移动设备管理, 安全策略下发", "check_item": "设备合规检查, 安全补丁检测", "device_software_id": "MDM-2.0, AV-6.0", "vendor_name": "Vendor GG", "harm_name": "设备失控, 数据泄露", "description": "企业移动终端检测到越狱或Root行为。", "prevention_measures": "禁止越狱设备接入企业网络，强制安全策略。", "attack_cause": "用户私自越狱绕过安全限制。", "defective_device_software": "MDM-2.0", "configuration_solution": "1. 在MDM中启用越狱检测。 2. 拒绝不合规设备访问。"}, {"event_id": "ea032", "event_name": "钓鱼网站伪造证书", "maintenance_method": "证书透明度检查, HSTS策略", "check_item": "SSL证书有效性检查, 浏览器安全告警", "device_software_id": "CA-2.0, BRW-1.2", "vendor_name": "Vendor HH", "harm_name": "凭证窃取, 金融欺诈", "description": "检测到钓鱼网站使用伪造SSL证书。", "prevention_measures": "启用证书透明度日志，强制HSTS。", "attack_cause": "用户访问未验证的证书链。", "defective_device_software": "CA-2.0", "configuration_solution": "1. 启用浏览器证书吊销检查。 2. 配置HSTS强制HTTPS。"}, {"event_id": "ea033", "event_name": "容器逃逸攻击", "maintenance_method": "容器安全加固, 内核补丁", "check_item": "容器运行日志, 内核漏洞检测", "device_software_id": "CTR-1.0, IDS-3.0", "vendor_name": "Vendor II", "harm_name": "主机被控, 数据泄露", "description": "检测到容器进程尝试突破隔离访问宿主机。", "prevention_measures": "启用容器安全策略，更新内核补丁。", "attack_cause": "容器运行环境未隔离关键权限。", "defective_device_software": "CTR-1.0", "configuration_solution": "1. 限制容器root权限。 2. 启用内核安全模块（SELinux/AppArmor）。"}, {"event_id": "ea034", "event_name": "勒索短信攻击(SMS Phishing)", "maintenance_method": "短信网关过滤, 用户教育", "check_item": "短信来源分析, 可疑链接检测", "device_software_id": "SMSF-1.0, UF-3.0", "vendor_name": "Vendor JJ", "harm_name": "凭证泄露, 财产损失", "description": "用户收到伪造银行短信，诱导输入凭证。", "prevention_measures": "启用短信网关过滤，教育用户识别诈骗。", "attack_cause": "短信网关未过滤可疑来源。", "defective_device_software": "SMSF-1.0", "configuration_solution": "1. 启用短信黑名单过滤。 2. 向用户发送防诈骗警示。"}, {"event_id": "ea035", "event_name": "电力SCADA数据篡改", "maintenance_method": "工控安全加固, 数据完整性监控", "check_item": "SCADA日志审计, 数据校验", "device_software_id": "ICS-4.0, DLP-3.5", "vendor_name": "Vendor KK", "harm_name": "电网异常, 安全事故", "description": "检测到电力调度系统的SCADA数据被篡改。", "prevention_measures": "实施完整性校验，启用只读监控。", "attack_cause": "工控设备防护不足。", "defective_device_software": "ICS-4.0", "configuration_solution": "1. 启用数据签名校验。 2. 增强工控隔离措施。"}, {"event_id": "ea036", "event_name": "深度伪造视频传播", "maintenance_method": "AI检测工具, 内容审查", "check_item": "视频指纹比对, 异常媒体传播分析", "device_software_id": "AI-2.0, DLP-4.0", "vendor_name": "<PERSON>end<PERSON>", "harm_name": "虚假信息扩散, 声誉损害", "description": "检测到深度伪造视频在企业社交平台传播。", "prevention_measures": "部署AI伪造检测工具，建立内容审查机制。", "attack_cause": "缺乏媒体真实性验证。", "defective_device_software": "AI-2.0", "configuration_solution": "1. 引入视频溯源技术。 2. 增设企业社交平台内容过滤。"}, {"event_id": "ea037", "event_name": "远程桌面暴力破解", "maintenance_method": "RDP安全加固, 登录限制", "check_item": "RDP登录日志, 登录失败次数统计", "device_software_id": "RDP-1.0, FW-4.0", "vendor_name": "Vendor MM", "harm_name": "远程控制, 数据泄露", "description": "发现大量来自异常IP的RDP登录尝试。", "prevention_measures": "限制RDP外网访问，启用MFA。", "attack_cause": "RDP端口暴露且无防护。", "defective_device_software": "RDP-1.0", "configuration_solution": "1. 修改RDP默认端口。 2. 启用登录失败锁定。"}, {"event_id": "ea038", "event_name": "无人机信号干扰", "maintenance_method": "通信加密, 信号干扰检测", "check_item": "无人机通信日志, GPS信号校验", "device_software_id": "UAV-1.0, IDS-4.0", "vendor_name": "Vendor NN", "harm_name": "无人机失控, 数据丢失", "description": "检测到无人机通信信号受到恶意干扰。", "prevention_measures": "启用频段跳变，加密通信。", "attack_cause": "信号加密不足，容易被干扰。", "defective_device_software": "UAV-1.0", "configuration_solution": "1. 使用抗干扰通信协议。 2. 启用无人机自动返航机制。"}, {"event_id": "ea039", "event_name": "数据库权限提升", "maintenance_method": "数据库权限分级, 审计机制", "check_item": "数据库日志, SQL执行权限检查", "device_software_id": "DB-3.0, SIEM-3.0", "vendor_name": "Vendor <PERSON>", "harm_name": "数据篡改, 敏感数据泄露", "description": "攻击者利用数据库漏洞获取管理员权限。", "prevention_measures": "严格控制数据库权限，启用审计日志。", "attack_cause": "数据库未启用最小权限。", "defective_device_software": "DB-3.0", "configuration_solution": "1. 关闭高权限账户远程访问。 2. 使用数据库活动监控工具。"}, {"event_id": "ea040", "event_name": "量子计算破解风险", "maintenance_method": "后量子加密算法, 密钥管理升级", "check_item": "加密算法使用情况, 密钥长度审查", "device_software_id": "CRYPTO-1.0, PKI-3.0", "vendor_name": "Vendor PP", "harm_name": "加密失效, 数据泄露", "description": "评估量子计算可能对现有加密体系造成威胁。", "prevention_measures": "逐步引入后量子加密算法，延长密钥长度。", "attack_cause": "传统RSA/ECC算法可能被量子计算破解。", "defective_device_software": "CRYPTO-1.0", "configuration_solution": "1. 评估迁移到后量子密码学。 2. 启用混合加密过渡方案。"}, {"event_id": "ea041", "event_name": "边缘计算节点入侵", "maintenance_method": "节点加固, 数据隔离", "check_item": "边缘节点日志分析, 流量检测", "device_software_id": "EDGE-1.0, IDS-4.1", "vendor_name": "Vendor <PERSON>", "harm_name": "边缘数据泄露, 运算被控", "description": "检测到黑客入侵边缘计算节点，非法访问业务数据。", "prevention_measures": "边缘节点最小化部署，隔离关键数据。", "attack_cause": "节点缺乏安全加固。", "defective_device_software": "EDGE-1.0", "configuration_solution": "1. 禁止默认SSH访问。 2. 启用边缘节点完整性监控。"}, {"event_id": "ea042", "event_name": "自动驾驶传感器欺骗", "maintenance_method": "传感器数据校验, 多模态融合", "check_item": "传感器输入分析, 异常数据比对", "device_software_id": "AUTO-1.0, AI-3.1", "vendor_name": "Vendor RR", "harm_name": "车辆误判, 交通事故", "description": "自动驾驶车辆遭遇激光干扰导致传感器失真。", "prevention_measures": "引入多模态传感器融合，数据交叉验证。", "attack_cause": "传感器单一信号依赖过高。", "defective_device_software": "AUTO-1.0", "configuration_solution": "1. 在AI模型中引入冗余检测。 2. 启用传感器异常报警机制。"}, {"event_id": "ea043", "event_name": "卫星通信信号欺骗", "maintenance_method": "频率加密, 信号认证", "check_item": "卫星链路日志, 信号完整性检查", "device_software_id": "SAT-1.0, CRYPTO-2.0", "vendor_name": "Vendor SS", "harm_name": "定位偏差, 通信中断", "description": "检测到卫星通信遭遇伪造信号攻击。", "prevention_measures": "加密链路传输，使用多源信号交叉验证。", "attack_cause": "卫星信号未做强认证。", "defective_device_software": "SAT-1.0", "configuration_solution": "1. 启用信号签名验证。 2. 部署抗欺骗导航系统。"}, {"event_id": "ea044", "event_name": "医疗设备未授权访问", "maintenance_method": "设备加密认证, 访问审计", "check_item": "医疗日志检查, 网络访问监控", "device_software_id": "MED-1.0, SIEM-4.0", "vendor_name": "Vendor TT", "harm_name": "患者隐私泄露, 医疗风险", "description": "黑客未授权访问联网医疗设备。", "prevention_measures": "为医疗设备启用访问认证，限制远程访问。", "attack_cause": "默认口令未修改，访问无审计。", "defective_device_software": "MED-1.0", "configuration_solution": "1. 医疗设备接入零信任网关。 2. 启用强身份认证。"}, {"event_id": "ea045", "event_name": "电网调度系统被入侵", "maintenance_method": "工控加固, 多级隔离", "check_item": "调度日志审查, 工控指令完整性检测", "device_software_id": "GRID-1.0, ICS-5.0", "vendor_name": "Vendor UU", "harm_name": "电网瘫痪, 大规模停电", "description": "攻击者控制电网调度系统下发非法指令。", "prevention_measures": "加强隔离区划分，部署工控防火墙。", "attack_cause": "调度系统与办公网未完全隔离。", "defective_device_software": "GRID-1.0", "configuration_solution": "1. 实施工控与IT网络物理隔离。 2. 对调度指令进行签名验证。"}, {"event_id": "ea046", "event_name": "电动车充电桩攻击", "maintenance_method": "充电桩固件更新, 接口认证", "check_item": "充电交易日志, 异常流量分析", "device_software_id": "EV-1.0, FW-5.0", "vendor_name": "Vendor VV", "harm_name": "电费盗刷, 充电中断", "description": "黑客利用充电桩接口漏洞进行盗刷。", "prevention_measures": "更新固件，增加接口认证机制。", "attack_cause": "接口API未做鉴权。", "defective_device_software": "EV-1.0", "configuration_solution": "1. 在API层增加OAuth认证。 2. 定期更新充电桩固件。"}, {"event_id": "ea047", "event_name": "AI对抗样本攻击", "maintenance_method": "模型鲁棒性增强, 输入检测", "check_item": "模型预测偏差检查, 输入特征分析", "device_software_id": "AI-4.0, IDS-5.0", "vendor_name": "Vendor WW", "harm_name": "模型误判, 决策错误", "description": "AI模型受到对抗样本输入干扰，输出异常结果。", "prevention_measures": "增强模型鲁棒性，输入做安全检测。", "attack_cause": "AI缺乏对抗样本防护。", "defective_device_software": "AI-4.0", "configuration_solution": "1. 使用对抗训练。 2. 部署输入异常检测器。"}, {"event_id": "ea048", "event_name": "供应链CI/CD平台入侵", "maintenance_method": "代码仓库保护, 构建环境隔离", "check_item": "CI/CD流水线日志, 构建镜像校验", "device_software_id": "CICD-1.0, SCM-2.0", "vendor_name": "Vendor XX", "harm_name": "恶意代码注入, 后门软件发布", "description": "CI/CD流水线被入侵，攻击者在构建环节注入后门。", "prevention_measures": "启用双因子认证，隔离构建环境。", "attack_cause": "CI/CD平台凭证泄露。", "defective_device_software": "CICD-1.0", "configuration_solution": "1. 对构建产物做哈希校验。 2. 仅可信节点可参与构建。"}, {"event_id": "ea049", "event_name": "云原生K8s集群被控", "maintenance_method": "RBAC权限收紧, API安全", "check_item": "K8s API日志, Pod异常检查", "device_software_id": "K8S-1.0, CTR-2.0", "vendor_name": "Vendor YY", "harm_name": "容器逃逸, 敏感数据访问", "description": "攻击者利用弱配置控制Kubernetes集群。", "prevention_measures": "启用RBAC严格控制权限。", "attack_cause": "K8s API未做鉴权。", "defective_device_software": "K8S-1.0", "configuration_solution": "1. 启用基于角色的访问控制。 2. API服务强制认证。"}, {"event_id": "ea050", "event_name": "区块链51%攻击", "maintenance_method": "算力分散, 区块链监控", "check_item": "区块链交易延迟, 分叉检测", "device_software_id": "BC-2.0, TI-2.0", "vendor_name": "Vendor Z<PERSON>", "harm_name": "双花攻击, 交易欺诈", "description": "检测到区块链遭遇51%算力控制攻击。", "prevention_measures": "增加算力分布，监控异常分叉。", "attack_cause": "算力集中度过高。", "defective_device_software": "BC-2.0", "configuration_solution": "1. 部署分布式算力联盟。 2. 启用分叉告警。"}, {"event_id": "ea051", "event_name": "无人驾驶车联网攻击", "maintenance_method": "V2X加密, 网络分段", "check_item": "车联网通信日志, 未授权接入检测", "device_software_id": "V2X-1.0, IDS-6.0", "vendor_name": "Vendor AAA", "harm_name": "交通瘫痪, 车辆失控", "description": "黑客通过V2X接口伪造交通信号。", "prevention_measures": "启用加密通信，限制外部接入。", "attack_cause": "通信未加密，缺乏认证。", "defective_device_software": "V2X-1.0", "configuration_solution": "1. 启用端到端加密。 2. 部署车辆安全网关。"}, {"event_id": "ea052", "event_name": "零信任认证绕过", "maintenance_method": "身份验证加强, 行为检测", "check_item": "零信任网关日志, 认证流量检查", "device_software_id": "ZT-1.0, SIEM-5.0", "vendor_name": "Vendor BBB", "harm_name": "未授权访问, 数据泄露", "description": "检测到攻击者绕过零信任认证机制进入系统。", "prevention_measures": "多因素动态验证，持续行为监控。", "attack_cause": "零信任配置不完善。", "defective_device_software": "ZT-1.0", "configuration_solution": "1. 引入基于风险的动态验证。 2. 定期审查零信任策略。"}, {"event_id": "ea053", "event_name": "量子通信窃听尝试", "maintenance_method": "量子密钥分发, 光信道监控", "check_item": "光子误码率检测, 信道完整性分析", "device_software_id": "QKD-1.0, MON-1.0", "vendor_name": "Vendor CCC", "harm_name": "加密密钥泄露, 通信失效", "description": "检测到光信道中存在窃听尝试。", "prevention_measures": "启用量子密钥分发，监控光信号异常。", "attack_cause": "传统信道未检测到中间人攻击。", "defective_device_software": "QKD-1.0", "configuration_solution": "1. 启用光信道异常检测。 2. 使用量子安全协议。"}, {"event_id": "ea054", "event_name": "语音助手窃听风险", "maintenance_method": "语音数据加密, 本地处理", "check_item": "语音日志审查, 网络传输检查", "device_software_id": "VA-1.0, DLP-5.0", "vendor_name": "Vendor DDD", "harm_name": "隐私泄露, 数据滥用", "description": "语音助手被发现长时间监听用户对话。", "prevention_measures": "限制远程传输，语音在本地处理。", "attack_cause": "设备默认上传语音数据。", "defective_device_software": "VA-1.0", "configuration_solution": "1. 禁用默认云端上传。 2. 开启本地语音加密存储。"}, {"event_id": "ea055", "event_name": "边界防火墙规则配置错误", "maintenance_method": "防火墙策略优化, 配置审计", "check_item": "防火墙日志, 规则有效性检测", "device_software_id": "FW-6.0, SIEM-5.1", "vendor_name": "Vendor EEE", "harm_name": "非法流量放行, 潜在入侵", "description": "发现边界防火墙放行了不应开放的端口。", "prevention_measures": "定期审查防火墙规则，最小化放行策略。", "attack_cause": "防火墙规则设置过宽。", "defective_device_software": "FW-6.0", "configuration_solution": "1. 禁用所有不必要端口。 2. 定期生成规则审计报告。"}, {"event_id": "ea056", "event_name": "远程办公VPN泄露", "maintenance_method": "VPN日志监控, MFA强制", "check_item": "VPN会话日志, 登录地理位置检测", "device_software_id": "VPN-3.0, SIEM-6.0", "vendor_name": "Vendor FFF", "harm_name": "未授权远程访问, 数据泄露", "description": "远程办公VPN账号被盗用。", "prevention_measures": "启用多因子认证，限制IP来源。", "attack_cause": "凭证泄露导致VPN被盗用。", "defective_device_software": "VPN-3.0", "configuration_solution": "1. 开启VPN地理位置限制。 2. 强制MFA认证。"}, {"event_id": "ea057", "event_name": "Web3钱包钓鱼攻击", "maintenance_method": "反钓鱼插件, 交易签名验证", "check_item": "交易请求分析, 域名来源检查", "device_software_id": "WALLET-1.0, BC-3.0", "vendor_name": "Vendor GGG", "harm_name": "数字资产被盗, 金融损失", "description": "用户通过伪造钱包网站签署恶意交易。", "prevention_measures": "部署反钓鱼插件，交易签名前验证。", "attack_cause": "用户访问了伪造钱包页面。", "defective_device_software": "WALLET-1.0", "configuration_solution": "1. 启用域名白名单。 2. 在钱包中加入风险提示。"}, {"event_id": "ea058", "event_name": "电子邮件供应链攻击", "maintenance_method": "邮件签名验证, 邮件网关过滤", "check_item": "邮件头部检查, DKIM/SPF验证", "device_software_id": "MAIL-1.0, EF-3.0", "vendor_name": "Vendor HHH", "harm_name": "钓鱼邮件, 凭证盗取", "description": "攻击者伪造供应商邮件发送恶意附件。", "prevention_measures": "启用邮件签名验证，教育员工识别风险。", "attack_cause": "邮件未进行SPF/DKIM验证。", "defective_device_software": "MAIL-1.0", "configuration_solution": "1. 强制所有外部邮件使用SPF/DKIM。 2. 开启附件沙箱检测。"}, {"event_id": "ea059", "event_name": "量子随机数生成器缺陷", "maintenance_method": "随机数源检测, 算法更新", "check_item": "随机数熵检测, 加密模块检查", "device_software_id": "QRNG-1.0, CRYPTO-3.0", "vendor_name": "Vendor III", "harm_name": "密钥可预测, 加密失效", "description": "量子随机数发生器输出熵不足，密钥可预测。", "prevention_measures": "增加熵源检测，替换缺陷设备。", "attack_cause": "硬件随机数发生器设计缺陷。", "defective_device_software": "QRNG-1.0", "configuration_solution": "1. 引入多源熵混合机制。 2. 升级硬件随机数发生器。"}, {"event_id": "ea060", "event_name": "跨境数据合规违规", "maintenance_method": "数据分级, 合规审计", "check_item": "跨境传输日志, 数据分类检查", "device_software_id": "DLP-6.0, AUDIT-1.0", "vendor_name": "Vendor <PERSON>", "harm_name": "违规罚款, 法律责任", "description": "检测到敏感数据未经许可跨境传输。", "prevention_measures": "数据传输需审批，启用跨境传输网关。", "attack_cause": "合规审计缺失，策略未落实。", "defective_device_software": "DLP-6.0", "configuration_solution": "1. 对敏感数据启用跨境审计机制。 2. 加强数据分级标识。"}, {"event_id": "ea061", "event_name": "5G核心网攻击", "maintenance_method": "切片隔离, 网络流量监控", "check_item": "5G核心网日志, 用户面流量检查", "device_software_id": "5GC-1.0, IDS-6.5", "vendor_name": "Vendor KKK", "harm_name": "网络中断, 通信劫持", "description": "检测到针对5G核心网的异常信令攻击。", "prevention_measures": "启用切片隔离，部署5G安全防护。", "attack_cause": "信令接口暴露缺乏限制。", "defective_device_software": "5GC-1.0", "configuration_solution": "1. 启用接口访问控制。 2. 增设5G核心安全监控。"}, {"event_id": "ea062", "event_name": "工业机器人被远程控制", "maintenance_method": "机器人控制通道加密, 访问认证", "check_item": "机器人控制日志, 网络访问日志", "device_software_id": "ROBOT-1.0, ICS-6.0", "vendor_name": "Vendor LLL", "harm_name": "生产中断, 人员安全风险", "description": "检测到工业机器人被异常远程控制。", "prevention_measures": "控制通道加密，禁止外部未授权访问。", "attack_cause": "控制接口未加密且缺乏认证。", "defective_device_software": "ROBOT-1.0", "configuration_solution": "1. 启用TLS加密控制信道。 2. 接入零信任访问网关。"}, {"event_id": "ea063", "event_name": "跨链桥漏洞攻击", "maintenance_method": "智能合约审计, 多签验证", "check_item": "跨链交易日志, 合约调用分析", "device_software_id": "BRIDGE-1.0, BC-4.0", "vendor_name": "Vendor M<PERSON>", "harm_name": "数字资产盗窃, 区块链信任崩溃", "description": "黑客利用跨链桥合约漏洞窃取资金。", "prevention_measures": "跨链桥需多签验证，合约定期审计。", "attack_cause": "跨链验证逻辑存在缺陷。", "defective_device_software": "BRIDGE-1.0", "configuration_solution": "1. 引入多签机制。 2. 进行跨链安全审计。"}, {"event_id": "ea064", "event_name": "AI越权调用检测", "maintenance_method": "API网关限制, 调用频率控制", "check_item": "AI API调用日志, 用户身份检查", "device_software_id": "AI-5.0, API-2.0", "vendor_name": "Vendor NNN", "harm_name": "敏感信息泄露, 资源滥用", "description": "AI服务被发现存在越权调用。", "prevention_measures": "增加调用频率控制和鉴权机制。", "attack_cause": "API网关未进行身份校验。", "defective_device_software": "AI-5.0", "configuration_solution": "1. 为每个用户分配唯一Token。 2. 限制调用频率。"}, {"event_id": "ea065", "event_name": "车载信息娱乐系统入侵", "maintenance_method": "系统加固, OTA安全更新", "check_item": "车载日志分析, CAN总线异常检测", "device_software_id": "CAR-1.0, IDS-7.0", "vendor_name": "Vendor OOO", "harm_name": "车辆失控, 数据泄露", "description": "检测到车载娱乐系统被远程入侵。", "prevention_measures": "启用安全OTA更新，限制外部接入。", "attack_cause": "车载系统接口暴露。", "defective_device_software": "CAR-1.0", "configuration_solution": "1. OTA升级需签名校验。 2. 车载网络分区隔离。"}, {"event_id": "ea066", "event_name": "供应链钓鱼网站", "maintenance_method": "域名监控, 邮件安全", "check_item": "可疑域名检测, 邮件钓鱼识别", "device_software_id": "DNSMON-1.0, EF-4.0", "vendor_name": "Vendor PPP", "harm_name": "凭证盗取, 财务欺诈", "description": "攻击者建立伪造供应商网站实施钓鱼。", "prevention_measures": "域名注册监控，启用品牌保护。", "attack_cause": "缺乏钓鱼域名检测机制。", "defective_device_software": "DNSMON-1.0", "configuration_solution": "1. 部署域名监控系统。 2. 在邮件系统中屏蔽可疑域名。"}, {"event_id": "ea067", "event_name": "Rogue Wi-Fi接入点", "maintenance_method": "无线入侵防御, 网络接入控制", "check_item": "Wi-Fi SSID检测, 信号强度分析", "device_software_id": "WIDS-1.0, NAC-2.0", "vendor_name": "Vendor QQQ", "harm_name": "流量窃取, 中间人攻击", "description": "检测到恶意Wi-Fi接入点诱导用户连接。", "prevention_measures": "部署WIDS，强制企业网络认证。", "attack_cause": "用户设备自动连接开放Wi-Fi。", "defective_device_software": "WIDS-1.0", "configuration_solution": "1. 禁止自动连接开放Wi-Fi。 2. NAC强制设备认证。"}, {"event_id": "ea068", "event_name": "智能楼宇控制系统入侵", "maintenance_method": "物理隔离, 默认口令清理", "check_item": "楼宇控制日志, 异常指令检测", "device_software_id": "BMS-1.0, IDS-7.5", "vendor_name": "Vendor RRR", "harm_name": "楼宇瘫痪, 人员困境", "description": "黑客入侵楼宇自动化控制系统关闭电梯与空调。", "prevention_measures": "设备口令必须更改，启用物理隔离。", "attack_cause": "楼宇系统出厂口令未修改。", "defective_device_software": "BMS-1.0", "configuration_solution": "1. 更改出厂默认口令。 2. 部署独立的管理网络。"}, {"event_id": "ea069", "event_name": "航空飞行管理系统异常", "maintenance_method": "飞行数据监控, 航电加固", "check_item": "航电系统日志, 飞行路径检测", "device_software_id": "FMS-1.0, IDS-8.0", "vendor_name": "Vendor SSS", "harm_name": "航班偏离, 乘客安全风险", "description": "飞行管理系统受到恶意指令影响。", "prevention_measures": "航电系统隔离，加强飞行数据监控。", "attack_cause": "飞行系统未完全隔离外部通信。", "defective_device_software": "FMS-1.0", "configuration_solution": "1. 实现航电系统隔离。 2. 启用飞行数据校验机制。"}, {"event_id": "ea070", "event_name": "智能电表数据篡改", "maintenance_method": "数据签名, 远程监控", "check_item": "电表通信日志, 读数校验", "device_software_id": "METER-1.0, DLP-7.0", "vendor_name": "Vendor TTT", "harm_name": "电费欺诈, 能源损失", "description": "发现电表读数被篡改以逃避缴费。", "prevention_measures": "电表数据签名，部署远程监控。", "attack_cause": "电表通信未加密。", "defective_device_software": "METER-1.0", "configuration_solution": "1. 电表通信必须加密。 2. 启用数据完整性校验。"}, {"event_id": "ea071", "event_name": "智能家居设备越权控制", "maintenance_method": "用户认证, 访问分级", "check_item": "设备控制日志, 用户身份验证", "device_software_id": "HOME-1.0, NAC-3.0", "vendor_name": "Vendor UUU", "harm_name": "隐私泄露, 设备失控", "description": "智能家居摄像头被远程非法控制。", "prevention_measures": "启用强认证，限制外部接入。", "attack_cause": "默认口令未更换。", "defective_device_software": "HOME-1.0", "configuration_solution": "1. 开启双因素认证。 2. 对设备控制进行访问分级。"}, {"event_id": "ea072", "event_name": "供应商远程维护账户泄露", "maintenance_method": "第三方接入控制, 审计机制", "check_item": "外部账户使用日志, 异常登录检测", "device_software_id": "AC-3.0, SIEM-7.0", "vendor_name": "Vendor VVV", "harm_name": "系统后门, 数据泄露", "description": "供应商维护账户被盗用进行入侵。", "prevention_measures": "外部账户启用MFA，定期审计。", "attack_cause": "外部账户缺乏安全措施。", "defective_device_software": "AC-3.0", "configuration_solution": "1. 禁止共享维护账户。 2. 强制MFA登录。"}, {"event_id": "ea073", "event_name": "无人仓储系统入侵", "maintenance_method": "机器人控制隔离, 网络加固", "check_item": "仓储日志, 机器人指令校验", "device_software_id": "WARE-1.0, IDS-8.2", "vendor_name": "Vendor WWW", "harm_name": "物流中断, 货物丢失", "description": "无人仓储系统被入侵，货物搬运异常。", "prevention_measures": "控制通道隔离，启用数据校验。", "attack_cause": "仓储系统缺乏安全防护。", "defective_device_software": "WARE-1.0", "configuration_solution": "1. 独立部署仓储控制网络。 2. 启用指令完整性校验。"}, {"event_id": "ea074", "event_name": "API密钥暴力破解", "maintenance_method": "速率限制, 强认证", "check_item": "API调用日志, 登录失败统计", "device_software_id": "API-3.0, SIEM-8.0", "vendor_name": "Vendor XXX", "harm_name": "API滥用, 数据泄露", "description": "攻击者对API接口进行密钥暴力破解。", "prevention_measures": "速率限制，启用强认证机制。", "attack_cause": "接口缺乏防暴力破解机制。", "defective_device_software": "API-3.0", "configuration_solution": "1. 启用速率限制。 2. 增设防暴力破解机制。"}, {"event_id": "ea075", "event_name": "智能交通信号篡改", "maintenance_method": "交通控制加密, 系统隔离", "check_item": "交通日志, 信号状态校验", "device_software_id": "TRAFFIC-1.0, IDS-9.0", "vendor_name": "Vendor YYY", "harm_name": "交通事故, 城市瘫痪", "description": "城市智能交通信号被黑客篡改。", "prevention_measures": "信号数据加密，部署冗余控制。", "attack_cause": "交通控制系统未加密。", "defective_device_software": "TRAFFIC-1.0", "configuration_solution": "1. 启用TLS通信。 2. 建立冗余控制通道。"}, {"event_id": "ea076", "event_name": "深网数据交易泄露", "maintenance_method": "暗网监控, 数据溯源", "check_item": "暗网市场扫描, 数据比对", "device_software_id": "DARK-1.0, TI-3.0", "vendor_name": "Vendor ZZZ", "harm_name": "敏感信息泄露, 企业声誉受损", "description": "在暗网发现企业敏感数据被出售。", "prevention_measures": "启用暗网监控，部署数据泄露响应。", "attack_cause": "数据外泄未及时发现。", "defective_device_software": "DARK-1.0", "configuration_solution": "1. 部署暗网情报收集平台。 2. 启用敏感数据标记。"}, {"event_id": "ea077", "event_name": "软件许可证伪造", "maintenance_method": "许可证验证, 数字签名", "check_item": "许可证文件校验, 使用日志检查", "device_software_id": "LIC-1.0, PKI-4.0", "vendor_name": "Vendor AAAA", "harm_name": "软件盗版, 收益损失", "description": "检测到伪造的软件许可证文件。", "prevention_measures": "启用数字签名，许可证远程验证。", "attack_cause": "许可证文件未加签。", "defective_device_software": "LIC-1.0", "configuration_solution": "1. 启用许可证数字签名验证。 2. 定期审计许可证使用情况。"}, {"event_id": "ea078", "event_name": "智能门禁系统破解", "maintenance_method": "多因素认证, 日志监控", "check_item": "门禁刷卡记录, 异常访问检查", "device_software_id": "ACCESS-1.0, NAC-4.0", "vendor_name": "Vendor BBBB", "harm_name": "非法入侵, 人员安全风险", "description": "黑客破解智能门禁系统进入机房。", "prevention_measures": "增加多因素认证，启用日志监控。", "attack_cause": "门禁系统单一认证方式。", "defective_device_software": "ACCESS-1.0", "configuration_solution": "1. 在门禁中启用人脸识别+卡片认证。 2. 增设实时告警机制。"}, {"event_id": "ea079", "event_name": "加密货币矿机感染木马", "maintenance_method": "恶意进程检测, 节点隔离", "check_item": "矿机性能监控, 恶意进程分析", "device_software_id": "MINER-1.0, EDR-7.0", "vendor_name": "Vendor CCCC", "harm_name": "算力被盗用, 电力浪费", "description": "矿机被木马感染进行隐蔽挖矿。", "prevention_measures": "矿机接入EDR，启用进程监控。", "attack_cause": "矿机无安全防护。", "defective_device_software": "MINER-1.0", "configuration_solution": "1. 启用EDR持续监控。 2. 对异常进程自动隔离。"}, {"event_id": "ea080", "event_name": "基因数据泄露", "maintenance_method": "数据加密存储, 访问控制", "check_item": "基因数据库日志, 访问审计", "device_software_id": "GENE-1.0, DLP-8.0", "vendor_name": "Vendor DDDD", "harm_name": "隐私泄露, 医疗风险", "description": "基因测序数据库被黑客入侵导致敏感数据泄露。", "prevention_measures": "基因数据必须加密存储，严格访问控制。", "attack_cause": "数据库缺乏加密和审计。", "defective_device_software": "GENE-1.0", "configuration_solution": "1. 基因数据库启用加密存储。 2. 访问行为启用全审计。"}, {"event_id": "ea081", "event_name": "数字身份伪造", "maintenance_method": "身份验证增强, 区块链身份认证", "check_item": "登录日志, 身份校验证书", "device_software_id": "ID-1.0, PKI-5.0", "vendor_name": "Vendor EEEE", "harm_name": "身份盗用, 非法交易", "description": "检测到伪造的数字身份用于非法访问。", "prevention_measures": "启用区块链身份验证，增加多因素认证。", "attack_cause": "身份认证系统缺乏强校验。", "defective_device_software": "ID-1.0", "configuration_solution": "1. 使用基于区块链的身份存证。 2. 强制双因子认证。"}, {"event_id": "ea082", "event_name": "无人矿区控制系统入侵", "maintenance_method": "网络隔离, 访问监控", "check_item": "矿区设备日志, 异常指令检测", "device_software_id": "MINE-1.0, ICS-7.0", "vendor_name": "Vendor FFFF", "harm_name": "矿区停工, 人员安全风险", "description": "检测到无人矿区控制系统被远程入侵。", "prevention_measures": "实施网络隔离，启用访问监控。", "attack_cause": "矿区系统对外暴露接口。", "defective_device_software": "MINE-1.0", "configuration_solution": "1. 禁止公网访问控制端口。 2. 部署工控隔离网关。"}, {"event_id": "ea083", "event_name": "智能电网负荷异常攻击", "maintenance_method": "电网流量监控, 数据完整性校验", "check_item": "电网调度日志, 负荷曲线分析", "device_software_id": "GRID-2.0, DLP-9.0", "vendor_name": "Vendor GGGG", "harm_name": "电网瘫痪, 能源损耗", "description": "检测到黑客操控智能电网负荷数据。", "prevention_measures": "启用数据完整性校验，电网安全监控。", "attack_cause": "电网调度缺乏数据验证。", "defective_device_software": "GRID-2.0", "configuration_solution": "1. 电网数据签名校验。 2. 启用异常负荷告警。"}, {"event_id": "ea084", "event_name": "量子通信设备劫持", "maintenance_method": "设备物理防护, 信道监控", "check_item": "量子通信日志, 光信号监测", "device_software_id": "QCOMM-1.0, QKD-2.0", "vendor_name": "Vendor HHHH", "harm_name": "通信中断, 密钥失效", "description": "量子通信设备被非法接入并干扰传输。", "prevention_measures": "加强物理防护，实时监控信道。", "attack_cause": "量子设备缺乏接入控制。", "defective_device_software": "QCOMM-1.0", "configuration_solution": "1. 增设量子通信设备防篡改保护。 2. 启用接入校验机制。"}, {"event_id": "ea085", "event_name": "元宇宙平台诈骗", "maintenance_method": "虚拟身份认证, 行为监控", "check_item": "虚拟交易日志, 身份验证审计", "device_software_id": "META-1.0, DLP-10.0", "vendor_name": "Vendor IIII", "harm_name": "虚拟财产盗窃, 用户受骗", "description": "元宇宙虚拟平台发现诈骗交易。", "prevention_measures": "加强虚拟身份认证，建立行为监控。", "attack_cause": "虚拟身份注册缺乏验证。", "defective_device_software": "META-1.0", "configuration_solution": "1. 所有虚拟身份需实名认证。 2. 对异常交易启用告警。"}, {"event_id": "ea086", "event_name": "智能工厂生产线停摆", "maintenance_method": "工控隔离, 安全补丁更新", "check_item": "生产日志, 控制系统状态监控", "device_software_id": "FACTORY-1.0, ICS-8.0", "vendor_name": "<PERSON>endor J<PERSON>", "harm_name": "生产停工, 经济损失", "description": "智能工厂生产线遭到黑客入侵停摆。", "prevention_measures": "工控系统隔离，定期打补丁。", "attack_cause": "控制系统使用了旧版本补丁。", "defective_device_software": "FACTORY-1.0", "configuration_solution": "1. 定期修复漏洞。 2. 部署工控隔离策略。"}, {"event_id": "ea087", "event_name": "卫星互联网链路劫持", "maintenance_method": "链路加密, 异常检测", "check_item": "卫星链路日志, IP劫持检测", "device_software_id": "SATNET-1.0, IDS-9.5", "vendor_name": "Vendor KKKK", "harm_name": "通信中断, 流量窃取", "description": "卫星互联网链路遭受劫持。", "prevention_measures": "启用加密链路，部署异常检测。", "attack_cause": "链路传输未加密。", "defective_device_software": "SATNET-1.0", "configuration_solution": "1. 强制链路加密。 2. 部署链路完整性监测。"}, {"event_id": "ea088", "event_name": "车路协同伪造消息", "maintenance_method": "消息签名, 通信加密", "check_item": "V2X消息日志, 信号验证", "device_software_id": "V2X-2.0, CRYPTO-4.0", "vendor_name": "Vendor LLLL", "harm_name": "交通事故, 车辆混乱", "description": "检测到伪造车路协同消息欺骗车辆。", "prevention_measures": "消息签名校验，启用通信加密。", "attack_cause": "消息缺乏签名验证。", "defective_device_software": "V2X-2.0", "configuration_solution": "1. 强制消息签名校验。 2. 开启端到端加密。"}, {"event_id": "ea089", "event_name": "AI欺骗攻击", "maintenance_method": "模型检测, 异常输入识别", "check_item": "输入输出对比, 模型预测偏差分析", "device_software_id": "AI-6.0, IDS-10.0", "vendor_name": "Vendor MMMM", "harm_name": "模型错误决策, 安全风险", "description": "AI系统遭遇欺骗性输入导致误判。", "prevention_measures": "加强输入检测，增加对抗训练。", "attack_cause": "缺乏输入验证机制。", "defective_device_software": "AI-6.0", "configuration_solution": "1. 部署输入异常检测器。 2. 增强模型鲁棒性。"}, {"event_id": "ea090", "event_name": "医疗影像数据篡改", "maintenance_method": "数据完整性保护, 数字签名", "check_item": "影像日志审查, 文件校验", "device_software_id": "MEDIMG-1.0, DLP-11.0", "vendor_name": "Vendor NNNN", "harm_name": "误诊风险, 患者生命威胁", "description": "医疗影像文件被黑客篡改。", "prevention_measures": "启用数字签名，部署完整性保护。", "attack_cause": "医疗影像未启用完整性校验。", "defective_device_software": "MEDIMG-1.0", "configuration_solution": "1. 所有影像启用哈希签名。 2. 开启存储完整性保护。"}, {"event_id": "ea091", "event_name": "能源交易平台被入侵", "maintenance_method": "交易签名验证, 访问控制", "check_item": "交易日志, 用户权限检查", "device_software_id": "ENERGY-1.0, PKI-6.0", "vendor_name": "Vendor OOOO", "harm_name": "能源欺诈, 经济损失", "description": "能源交易平台被攻击者入侵篡改数据。", "prevention_measures": "强制交易签名验证，权限控制。", "attack_cause": "平台未启用签名验证。", "defective_device_software": "ENERGY-1.0", "configuration_solution": "1. 启用交易签名校验。 2. 开启权限分级管理。"}, {"event_id": "ea092", "event_name": "金融风控模型绕过", "maintenance_method": "模型检测, 风险规则优化", "check_item": "交易日志分析, 模型偏差监控", "device_software_id": "FIN-1.0, AI-7.0", "vendor_name": "Vendor PPPP", "harm_name": "欺诈交易, 财务损失", "description": "金融风控AI模型被绕过。", "prevention_measures": "增加多层风控模型，实时监控偏差。", "attack_cause": "单一模型缺乏多层验证。", "defective_device_software": "FIN-1.0", "configuration_solution": "1. 部署多层风控机制。 2. 引入实时检测。"}, {"event_id": "ea093", "event_name": "工业传感器数据伪造", "maintenance_method": "数据校验, 多源对比", "check_item": "传感器日志, 数据异常检测", "device_software_id": "SENSOR-1.0, ICS-9.0", "vendor_name": "Vendor QQQQ", "harm_name": "生产异常, 设备损坏", "description": "工业传感器数据被伪造。", "prevention_measures": "引入多源数据交叉验证。", "attack_cause": "传感器数据缺乏校验。", "defective_device_software": "SENSOR-1.0", "configuration_solution": "1. 启用多传感器冗余机制。 2. 部署数据完整性校验。"}, {"event_id": "ea094", "event_name": "加密货币交易所被黑", "maintenance_method": "冷热钱包分离, 多签保护", "check_item": "交易所资金流监控, 钱包调用日志", "device_software_id": "EXCH-1.0, BC-5.0", "vendor_name": "Vendor RRRR", "harm_name": "资金盗窃, 用户损失", "description": "加密货币交易所遭黑客盗币。", "prevention_measures": "冷钱包存储，强制多签。", "attack_cause": "热钱包未加保护。", "defective_device_software": "EXCH-1.0", "configuration_solution": "1. 冷热钱包分离。 2. 所有提现启用多签。"}, {"event_id": "ea095", "event_name": "工业PLC逻辑篡改", "maintenance_method": "PLC程序保护, 审计机制", "check_item": "PLC日志, 程序完整性检测", "device_software_id": "PLC-1.0, ICS-10.0", "vendor_name": "Vendor SSSS", "harm_name": "设备损坏, 生产异常", "description": "黑客篡改PLC逻辑导致设备异常运行。", "prevention_measures": "PLC程序启用加密，部署审计机制。", "attack_cause": "PLC未启用完整性校验。", "defective_device_software": "PLC-1.0", "configuration_solution": "1. 对PLC逻辑启用签名保护。 2. 定期校验逻辑完整性。"}, {"event_id": "ea096", "event_name": "智能语音合成滥用", "maintenance_method": "声音指纹识别, 使用监控", "check_item": "语音调用日志, 声纹比对", "device_software_id": "TTS-1.0, AI-8.0", "vendor_name": "Vendor TTTT", "harm_name": "诈骗语音, 声誉损害", "description": "智能语音合成被滥用进行诈骗。", "prevention_measures": "部署声纹识别，启用滥用检测。", "attack_cause": "语音合成缺乏使用监控。", "defective_device_software": "TTS-1.0", "configuration_solution": "1. 启用声纹识别。 2. 对滥用行为进行封禁。"}, {"event_id": "ea097", "event_name": "无人机数据链路劫持", "maintenance_method": "链路加密, 接入认证", "check_item": "无人机飞控日志, 数据链完整性检测", "device_software_id": "UAV-2.0, IDS-11.0", "vendor_name": "Vendor UUUU", "harm_name": "无人机坠毁, 数据泄露", "description": "无人机数据链路被劫持。", "prevention_measures": "数据链加密，接入认证。", "attack_cause": "链路通信未加密。", "defective_device_software": "UAV-2.0", "configuration_solution": "1. 启用链路加密。 2. 启用接入认证机制。"}, {"event_id": "ea098", "event_name": "智能物流车队调度篡改", "maintenance_method": "调度加密, 异常监控", "check_item": "调度日志, 路线校验", "device_software_id": "LOGI-1.0, SIEM-9.0", "vendor_name": "Vendor VVVV", "harm_name": "货物丢失, 物流中断", "description": "车队调度数据被篡改导致货物错误投递。", "prevention_measures": "调度数据加密，部署异常检测。", "attack_cause": "调度系统缺乏校验机制。", "defective_device_software": "LOGI-1.0", "configuration_solution": "1. 调度数据启用签名。 2. 启用异常调度告警。"}, {"event_id": "ea099", "event_name": "跨境支付系统入侵", "maintenance_method": "支付加密, 风控系统", "check_item": "支付交易日志, 风控规则检查", "device_software_id": "PAY-1.0, AI-9.0", "vendor_name": "Vendor WWWW", "harm_name": "支付欺诈, 资金损失", "description": "跨境支付系统被黑客入侵篡改交易。", "prevention_measures": "支付系统加密，风控实时监测。", "attack_cause": "支付接口防护不足。", "defective_device_software": "PAY-1.0", "configuration_solution": "1. 启用端到端加密。 2. 增设风控AI模型。"}, {"event_id": "ea100", "event_name": "卫星地面站被渗透", "maintenance_method": "物理隔离, 接入控制", "check_item": "地面站日志, 通信链路监测", "device_software_id": "SATGS-1.0, IDS-12.0", "vendor_name": "Vendor XXXX", "harm_name": "卫星控制权丧失, 通信中断", "description": "黑客渗透卫星地面站获取控制权。", "prevention_measures": "地面站物理隔离，接入严格控制。", "attack_cause": "地面站管理接口暴露。", "defective_device_software": "SATGS-1.0", "configuration_solution": "1. 封闭公网接口。 2. 部署强访问认证。"}, {"event_id": "ea101", "event_name": "智能制造产线机器人宕机", "maintenance_method": "冗余部署, 工控隔离", "check_item": "生产日志分析, 机器人运行状态监控", "device_software_id": "MFG-1.0, ICS-11.0", "vendor_name": "Vendor YYYY", "harm_name": "生产停滞, 经济损失", "description": "检测到智能制造机器人被恶意指令导致宕机。", "prevention_measures": "工控隔离，部署冗余机器人。", "attack_cause": "控制系统缺乏防护。", "defective_device_software": "MFG-1.0", "configuration_solution": "1. 工控网络物理隔离。 2. 启用冗余控制。"}, {"event_id": "ea102", "event_name": "无人船舶导航信号欺骗", "maintenance_method": "GPS校验, 多源导航", "check_item": "船舶航行日志, GPS信号完整性检测", "device_software_id": "SHIP-1.0, NAV-2.0", "vendor_name": "Vendor ZZZZ", "harm_name": "航行偏离, 碰撞风险", "description": "无人船舶遭遇伪造导航信号。", "prevention_measures": "启用多源导航，增加信号校验。", "attack_cause": "单一GPS信号依赖。", "defective_device_software": "SHIP-1.0", "configuration_solution": "1. 增设多模态导航。 2. 部署GPS抗欺骗机制。"}, {"event_id": "ea103", "event_name": "卫星遥感数据篡改", "maintenance_method": "数据签名, 加密存储", "check_item": "遥感图像完整性检查, 数据传输日志", "device_software_id": "SATIMG-1.0, DLP-12.0", "vendor_name": "Vendor AAAAA", "harm_name": "地理信息错误, 决策误导", "description": "遥感卫星图像数据被恶意篡改。", "prevention_measures": "数据签名保护，存储加密。", "attack_cause": "数据传输未做签名验证。", "defective_device_software": "SATIMG-1.0", "configuration_solution": "1. 所有图像文件启用签名。 2. 启用端到端加密。"}, {"event_id": "ea104", "event_name": "NFT市场智能合约漏洞", "maintenance_method": "合约审计, 权限控制", "check_item": "NFT交易日志, 合约调用监控", "device_software_id": "NFT-1.0, BC-6.0", "vendor_name": "Vendor BBBBB", "harm_name": "虚拟资产盗窃, 市场混乱", "description": "NFT智能合约存在漏洞被利用。", "prevention_measures": "定期合约审计，启用权限控制。", "attack_cause": "合约未进行安全测试。", "defective_device_software": "NFT-1.0", "configuration_solution": "1. 启用多签合约机制。 2. 进行安全审计。"}, {"event_id": "ea105", "event_name": "AI大模型越权调用", "maintenance_method": "API鉴权, 使用监控", "check_item": "大模型调用日志, 用户权限分析", "device_software_id": "LLM-1.0, API-4.0", "vendor_name": "Vendor CCCCC", "harm_name": "敏感信息外泄, 滥用风险", "description": "AI大模型接口被越权调用。", "prevention_measures": "启用严格鉴权，调用行为监控。", "attack_cause": "接口缺少权限控制。", "defective_device_software": "LLM-1.0", "configuration_solution": "1. 所有调用需带签名Token。 2. 启用速率与权限控制。"}, {"event_id": "ea106", "event_name": "边缘安全网关失陷", "maintenance_method": "补丁更新, 流量审计", "check_item": "网关流量日志, 攻击检测", "device_software_id": "EDGESEC-1.0, FW-7.0", "vendor_name": "Vendor DDDDD", "harm_name": "网络入侵, 数据泄露", "description": "边缘安全网关遭到黑客入侵。", "prevention_measures": "及时更新补丁，启用流量审计。", "attack_cause": "网关存在未修复漏洞。", "defective_device_software": "EDGESEC-1.0", "configuration_solution": "1. 定期漏洞扫描。 2. 启用实时审计。"}, {"event_id": "ea107", "event_name": "脑机接口窃听", "maintenance_method": "神经信号加密, 本地存储", "check_item": "神经接口日志, 数据加密检测", "device_software_id": "BCI-1.0, CRYPTO-5.0", "vendor_name": "Vendor EEEEE", "harm_name": "隐私泄露, 安全风险", "description": "脑机接口信号被窃听。", "prevention_measures": "信号加密，禁止远程传输。", "attack_cause": "接口数据未加密。", "defective_device_software": "BCI-1.0", "configuration_solution": "1. 启用神经信号加密。 2. 启用本地安全存储。"}, {"event_id": "ea108", "event_name": "车联网CAN总线入侵", "maintenance_method": "总线加密, 入侵检测", "check_item": "CAN总线日志, 异常指令检测", "device_software_id": "CAN-1.0, IDS-13.0", "vendor_name": "Vendor FFFFF", "harm_name": "车辆失控, 安全事故", "description": "黑客通过CAN总线入侵车联网系统。", "prevention_measures": "启用总线加密，入侵检测。", "attack_cause": "总线缺乏认证与加密。", "defective_device_software": "CAN-1.0", "configuration_solution": "1. CAN通信加密。 2. 启用异常检测。"}, {"event_id": "ea109", "event_name": "智慧农业传感器欺骗", "maintenance_method": "多源数据校验, 冗余传感器", "check_item": "农业传感器日志, 数据异常分析", "device_software_id": "AGRI-1.0, SENSOR-2.0", "vendor_name": "Vendor GGGGG", "harm_name": "农业生产失效, 产量下降", "description": "农业传感器数据被篡改导致错误灌溉。", "prevention_measures": "引入冗余传感器，多源比对。", "attack_cause": "传感器数据缺乏验证。", "defective_device_software": "AGRI-1.0", "configuration_solution": "1. 部署多源数据对比。 2. 启用异常监测。"}, {"event_id": "ea110", "event_name": "能源物联网节点入侵", "maintenance_method": "节点加固, 网络隔离", "check_item": "能源IoT日志, 节点访问监控", "device_software_id": "ENERGY-IOT-1.0, SIEM-10.0", "vendor_name": "Vendor HHHHH", "harm_name": "能源数据篡改, 系统异常", "description": "能源IoT节点被黑客入侵。", "prevention_measures": "加固节点，实施隔离。", "attack_cause": "节点使用默认口令。", "defective_device_software": "ENERGY-IOT-1.0", "configuration_solution": "1. 强制修改口令。 2. 启用零信任接入。"}, {"event_id": "ea111", "event_name": "智能穿戴设备越权访问", "maintenance_method": "设备加密, 用户认证", "check_item": "穿戴设备日志, 数据访问检查", "device_software_id": "WEAR-1.0, NAC-5.0", "vendor_name": "Vendor IIIII", "harm_name": "隐私泄露, 用户健康风险", "description": "检测到智能穿戴设备数据被越权访问。", "prevention_measures": "启用数据加密与认证机制。", "attack_cause": "设备缺乏访问控制。", "defective_device_software": "WEAR-1.0", "configuration_solution": "1. 启用双因素认证。 2. 启用本地加密存储。"}, {"event_id": "ea112", "event_name": "跨境电商支付欺诈", "maintenance_method": "交易监控, 风险识别", "check_item": "电商交易日志, 支付风控规则", "device_software_id": "ECOM-1.0, FIN-2.0", "vendor_name": "<PERSON>end<PERSON> JJJJ<PERSON>", "harm_name": "财务损失, 用户受骗", "description": "跨境电商平台检测到支付欺诈行为。", "prevention_measures": "加强风控规则，实时监控。", "attack_cause": "支付风控策略缺失。", "defective_device_software": "ECOM-1.0", "configuration_solution": "1. 部署支付反欺诈模型。 2. 增设多层验证。"}, {"event_id": "ea113", "event_name": "数字孪生系统被篡改", "maintenance_method": "数据校验, 权限隔离", "check_item": "孪生系统日志, 模型完整性校验", "device_software_id": "DT-1.0, AI-10.0", "vendor_name": "Vendor KKKKK", "harm_name": "虚拟仿真失真, 决策误导", "description": "数字孪生系统模型被恶意篡改。", "prevention_measures": "部署数据校验，权限隔离。", "attack_cause": "孪生系统缺乏完整性保护。", "defective_device_software": "DT-1.0", "configuration_solution": "1. 启用孪生模型签名校验。 2. 分级权限管理。"}, {"event_id": "ea114", "event_name": "水利调度系统入侵", "maintenance_method": "工控隔离, 日志监控", "check_item": "水利调度日志, 工控流量检测", "device_software_id": "WATER-1.0, ICS-12.0", "vendor_name": "Vendor LLLLL", "harm_name": "水利调度异常, 安全事故", "description": "黑客入侵水利调度系统篡改数据。", "prevention_measures": "工控系统隔离，监控流量。", "attack_cause": "系统接口未隔离。", "defective_device_software": "WATER-1.0", "configuration_solution": "1. 隔离水利调度系统。 2. 启用异常监控。"}, {"event_id": "ea115", "event_name": "智慧城市监控平台入侵", "maintenance_method": "视频加密, 访问控制", "check_item": "监控日志, 摄像头访问检测", "device_software_id": "CITY-1.0, DLP-13.0", "vendor_name": "Vendor MMMMM", "harm_name": "隐私泄露, 城市安防失效", "description": "智慧城市监控平台被黑客入侵。", "prevention_measures": "视频流加密，严格访问控制。", "attack_cause": "摄像头接口缺乏防护。", "defective_device_software": "CITY-1.0", "configuration_solution": "1. 摄像头启用TLS。 2. 部署访问审计。"}, {"event_id": "ea116", "event_name": "跨国能源调度系统异常", "maintenance_method": "跨境流量加密, 审计监控", "check_item": "能源调度日志, 国际链路检查", "device_software_id": "ENERGY-INTL-1.0, IDS-14.0", "vendor_name": "Vendor NNNNN", "harm_name": "大规模停电, 政治风险", "description": "跨国能源调度系统检测到异常流量。", "prevention_measures": "启用加密，跨境链路审计。", "attack_cause": "跨国链路缺乏加密。", "defective_device_software": "ENERGY-INTL-1.0", "configuration_solution": "1. 启用跨国链路加密。 2. 部署审计监控系统。"}, {"event_id": "ea117", "event_name": "AI语音识别模型误导", "maintenance_method": "输入检测, 模型增强", "check_item": "语音识别日志, 对抗样本检测", "device_software_id": "ASR-1.0, AI-11.0", "vendor_name": "Vendor OOOOO", "harm_name": "识别错误, 操作误导", "description": "AI语音识别遭遇对抗样本攻击。", "prevention_measures": "增强模型鲁棒性，部署输入检测。", "attack_cause": "模型缺乏对抗防护。", "defective_device_software": "ASR-1.0", "configuration_solution": "1. 增设对抗训练。 2. 输入流量检测。"}, {"event_id": "ea118", "event_name": "医疗物联网设备篡改", "maintenance_method": "IoMT加固, 加密通信", "check_item": "IoMT日志, 异常指令检测", "device_software_id": "IOMT-1.0, IDS-15.0", "vendor_name": "Vendor PPPPP", "harm_name": "医疗失效, 患者风险", "description": "医疗物联网设备被篡改。", "prevention_measures": "IoMT设备加固，启用加密。", "attack_cause": "设备接口无加密。", "defective_device_software": "IOMT-1.0", "configuration_solution": "1. 所有指令加密传输。 2. 启用IoMT审计。"}, {"event_id": "ea119", "event_name": "智能建筑能源系统入侵", "maintenance_method": "建筑系统隔离, 访问控制", "check_item": "能源管理日志, 系统访问监控", "device_software_id": "BUILD-1.0, NAC-6.0", "vendor_name": "Vendor QQQQQ", "harm_name": "能源浪费, 建筑停运", "description": "智能建筑能源系统被入侵篡改。", "prevention_measures": "建筑系统隔离，实施访问控制。", "attack_cause": "系统权限过宽。", "defective_device_software": "BUILD-1.0", "configuration_solution": "1. 分级权限管理。 2. 启用建筑能源监控。"}, {"event_id": "ea120", "event_name": "SQL 注入攻击", "maintenance_method": "输入验证, 参数化查询", "check_item": "数据库访问日志, 用户输入审查", "device_software_id": "WEBAPP-1.0, DB-5.6", "vendor_name": "Vendor AAA", "harm_name": "数据库泄露, 数据篡改", "description": "攻击者通过未过滤的输入执行恶意 SQL。", "prevention_measures": "使用参数化查询和输入过滤。", "attack_cause": "用户输入未验证或直接拼接 SQL", "defective_device_software": "WEBAPP-1.0", "configuration_solution": "1. 使用 ORM 或预编译语句。 2. 严格输入校验。 3. 限制数据库账户权限。"}, {"event_id": "ea121", "event_name": "跨站脚本攻击（XSS）", "maintenance_method": "输出编码, 内容安全策略", "check_item": "页面请求日志, 用户输入记录", "device_software_id": "WEBAPP-2.1, FRONT-3.0", "vendor_name": "Vendor BBB", "harm_name": "用户信息泄露, 会话劫持", "description": "攻击者在网页注入恶意脚本。", "prevention_measures": "对输出进行编码，启用 CSP。", "attack_cause": "用户输入未转义或缺少内容安全策略", "defective_device_software": "WEBAPP-2.1", "configuration_solution": "1. 对输出进行 HTML/JS 转义。 2. 设置 Content-Security-Policy。 3. 对用户输入进行白名单过滤。"}, {"event_id": "ea122", "event_name": "跨站请求伪造（CSRF）", "maintenance_method": "请求验证, CSRF Token", "check_item": "请求日志, 用户会话审计", "device_software_id": "WEBAPP-3.0, AUTH-1.2", "vendor_name": "Vendor CCC", "harm_name": "用户账户被恶意操作", "description": "攻击者诱导用户发起未授权请求。", "prevention_measures": "在敏感操作中使用 CSRF Token 验证。", "attack_cause": "缺少请求来源验证", "defective_device_software": "WEBAPP-3.0", "configuration_solution": "1. 启用 CSRF Token。 2. 验证 Referer 或 Origin。 3. 对敏感请求二次确认。"}, {"event_id": "ea123", "event_name": "文件上传漏洞", "maintenance_method": "文件类型校验, 存储隔离", "check_item": "上传日志, 文件访问控制", "device_software_id": "WEBAPP-4.0, FILE-1.5", "vendor_name": "Vendor DDD", "harm_name": "恶意文件执行, 服务器被入侵", "description": "攻击者上传恶意文件执行服务器代码。", "prevention_measures": "严格限制上传类型和路径，隔离存储。", "attack_cause": "文件类型和路径未验证", "defective_device_software": "WEBAPP-4.0", "configuration_solution": "1. 限制允许上传的 MIME 类型。 2. 隔离文件存储路径。 3. 执行上传文件扫描。"}, {"event_id": "ea124", "event_name": "远程代码执行（RCE）", "maintenance_method": "输入验证, 最小权限运行", "check_item": "应用日志, 异常请求监控", "device_software_id": "WEBAPP-5.0, RUNTIME-2.0", "vendor_name": "Vendor EEE", "harm_name": "服务器被完全控制", "description": "攻击者通过漏洞执行任意服务器代码。", "prevention_measures": "验证输入并限制执行权限。", "attack_cause": "用户输入未受控，存在执行漏洞", "defective_device_software": "WEBAPP-5.0", "configuration_solution": "1. 输入严格校验。 2. 限制程序权限。 3. 定期更新组件。"}, {"event_id": "ea125", "event_name": "敏感信息泄露", "maintenance_method": "数据加密, 接口鉴权", "check_item": "API日志, 数据访问审计", "device_software_id": "WEBAPP-6.0, DB-5.7", "vendor_name": "Vendor FFF", "harm_name": "用户隐私泄露, 法律风险", "description": "应用未加密存储或传输敏感数据。", "prevention_measures": "使用 HTTPS 与数据加密存储。", "attack_cause": "未加密或访问控制不严格", "defective_device_software": "WEBAPP-6.0", "configuration_solution": "1. 数据库字段加密。 2. HTTPS 全站启用。 3. 强化 API 鉴权。"}, {"event_id": "ea126", "event_name": "点击劫持（Clickjacking）", "maintenance_method": "X-Frame-Options, 内容安全策略", "check_item": "页面访问日志, iframe 调用分析", "device_software_id": "WEBAPP-7.0, FRONT-3.2", "vendor_name": "Vendor GGG", "harm_name": "用户操作被篡改, 功能误触", "description": "攻击者在 iframe 中欺骗用户点击。", "prevention_measures": "设置 X-Frame-Options 或 CSP frame-ancestors。", "attack_cause": "页面可被嵌入 iframe", "defective_device_software": "WEBAPP-7.0", "configuration_solution": "1. 添加 X-Frame-Options DENY/SAMEORIGIN。 2. CSP frame-ancestors 限制。 3. 页面敏感操作二次确认。"}, {"event_id": "ea127", "event_name": "HTTP 响应拆分", "maintenance_method": "输入过滤, HTTP头验证", "check_item": "服务器响应日志, 用户请求审计", "device_software_id": "WEBAPP-8.0, SERVER-2.1", "vendor_name": "Vendor HHH", "harm_name": "HTTP 响应被篡改, 劫持用户会话", "description": "攻击者通过插入换行符操纵响应头。", "prevention_measures": "过滤 CR/LF，验证 HTTP 头", "attack_cause": "用户输入未过滤换行符", "defective_device_software": "WEBAPP-8.0", "configuration_solution": "1. 过滤 HTTP header 中的 CR/LF。 2. 检查重定向参数。 3. 审计响应日志。"}, {"event_id": "ea128", "event_name": "远程文件包含（RFI）", "maintenance_method": "路径验证, 配置安全", "check_item": "文件访问日志, 配置审计", "device_software_id": "WEBAPP-9.0, PHP-7.4", "vendor_name": "Vendor III", "harm_name": "远程代码执行, 服务器被控制", "description": "攻击者通过包含远程文件执行代码。", "prevention_measures": "严格限制包含文件路径", "attack_cause": "可控输入用于文件包含", "defective_device_software": "WEBAPP-9.0", "configuration_solution": "1. 禁止远程文件包含。 2. 输入路径白名单。 3. 最小化文件访问权限。"}, {"event_id": "ea129", "event_name": "本地文件包含（LFI）", "maintenance_method": "路径白名单, 输入校验", "check_item": "文件访问日志, 日志审计", "device_software_id": "WEBAPP-10.0, PHP-7.4", "vendor_name": "Vendor <PERSON>", "harm_name": "信息泄露, 本地代码执行", "description": "攻击者通过本地文件包含获取敏感信息。", "prevention_measures": "严格限制可包含的本地文件路径", "attack_cause": "用户输入未验证即用作文件路径", "defective_device_software": "WEBAPP-10.0", "configuration_solution": "1. 文件路径白名单。 2. 禁止敏感文件访问。 3. 审计访问日志。"}, {"event_id": "ea130", "event_name": "Web 目录遍历", "maintenance_method": "路径过滤, 权限管理", "check_item": "访问日志, 文件系统权限审查", "device_software_id": "WEBAPP-11.0, SERVER-2.2", "vendor_name": "Vendor KKK", "harm_name": "敏感文件泄露, 系统信息暴露", "description": "攻击者通过 ../ 等方式访问非公开文件。", "prevention_measures": "过滤路径并限制文件系统权限", "attack_cause": "路径未过滤或权限过宽", "defective_device_software": "WEBAPP-11.0", "configuration_solution": "1. 路径输入过滤。 2. 文件权限最小化。 3. 日志审计。"}, {"event_id": "ea131", "event_name": "HTTP 请求走私", "maintenance_method": "边界解析验证, 防火墙规则", "check_item": "代理日志, 请求监控", "device_software_id": "WEBAPP-12.0, SERVER-2.3", "vendor_name": "Vendor LLL", "harm_name": "绕过安全策略, 请求劫持", "description": "攻击者构造非法请求包混淆解析", "prevention_measures": "严格验证请求边界和头部", "attack_cause": "服务器解析异常或缺少边界检查", "defective_device_software": "WEBAPP-12.0", "configuration_solution": "1. 验证请求边界。 2. 设置防火墙检测异常请求。 3. 审计日志。"}, {"event_id": "ea132", "event_name": "敏感信息通过 URL 泄露", "maintenance_method": "使用 POST, 参数加密", "check_item": "访问日志, URL参数审查", "device_software_id": "WEBAPP-13.0, FRONT-3.5", "vendor_name": "Vendor M<PERSON>", "harm_name": "账户信息泄露, 会话劫持", "description": "敏感信息出现在 GET 请求 URL 中", "prevention_measures": "使用 POST 提交并加密参数", "attack_cause": "通过 URL 传递敏感数据", "defective_device_software": "WEBAPP-13.0", "configuration_solution": "1. 参数加密。 2. 使用 POST 请求提交敏感信息。 3. URL 审计。"}, {"event_id": "ea133", "event_name": "WebSocket 数据注入", "maintenance_method": "消息验证, 权限控制", "check_item": "WebSocket 日志, 消息审计", "device_software_id": "WEBAPP-14.0, WS-1.2", "vendor_name": "Vendor NNN", "harm_name": "数据篡改, 服务异常", "description": "攻击者向 WebSocket 发送恶意消息修改数据", "prevention_measures": "对消息进行验证并控制权限", "attack_cause": "消息缺乏合法性检查", "defective_device_software": "WEBAPP-14.0", "configuration_solution": "1. 验证消息格式和内容。 2. 实施权限控制。 3. 日志审计。"}, {"event_id": "ea134", "event_name": "JSON 注入", "maintenance_method": "输入校验, 输出编码", "check_item": "API 请求日志, 数据审计", "device_software_id": "WEBAPP-15.0, API-1.5", "vendor_name": "Vendor OOO", "harm_name": "敏感数据泄露, 服务异常", "description": "攻击者向 JSON 接口注入恶意数据", "prevention_measures": "验证输入并对输出进行编码", "attack_cause": "JSON 数据未验证", "defective_device_software": "WEBAPP-15.0", "configuration_solution": "1. 输入校验。 2. 输出编码。 3. API 日志审计。"}, {"event_id": "ea135", "event_name": "HTTP 方法滥用", "maintenance_method": "方法限制, 防火墙规则", "check_item": "服务器访问日志, 请求监控", "device_software_id": "WEBAPP-16.0, SERVER-2.4", "vendor_name": "Vendor PPP", "harm_name": "未授权操作, 服务异常", "description": "攻击者使用 PUT/DELETE 等非法 HTTP 方法", "prevention_measures": "仅允许必要方法并设置防火墙规则", "attack_cause": "服务器未限制 HTTP 方法", "defective_device_software": "WEBAPP-16.0", "configuration_solution": "1. 限制 HTTP 方法。 2. 防火墙过滤。 3. 请求审计。"}, {"event_id": "ea136", "event_name": "XML 外部实体注入（XXE）", "maintenance_method": "禁用外部实体, XML 验证", "check_item": "XML 日志, 数据访问审计", "device_software_id": "WEBAPP-17.0, XML-2.0", "vendor_name": "Vendor QQQ", "harm_name": "敏感数据泄露, SSRF", "description": "攻击者通过 XML 外部实体访问敏感资源", "prevention_measures": "禁用外部实体并验证 XML 数据", "attack_cause": "XML 解析器未禁用外部实体", "defective_device_software": "WEBAPP-17.0", "configuration_solution": "1. 禁用 XML 外部实体。 2. 验证 XML 数据。 3. 日志审计。"}, {"event_id": "ea137", "event_name": "模板注入", "maintenance_method": "模板引擎安全配置, 输入过滤", "check_item": "渲染日志, 用户输入审计", "device_software_id": "WEBAPP-18.0, TEMPLATE-1.1", "vendor_name": "Vendor RRR", "harm_name": "服务器命令执行, 数据泄露", "description": "攻击者在模板中注入恶意表达式执行代码", "prevention_measures": "模板引擎安全配置并过滤输入", "attack_cause": "用户输入直接渲染到模板", "defective_device_software": "WEBAPP-18.0", "configuration_solution": "1. 禁用模板危险语法。 2. 输入过滤。 3. 审计渲染日志。"}, {"event_id": "ea138", "event_name": "服务器端请求伪造（SSRF）", "maintenance_method": "请求目标验证, 白名单控制", "check_item": "服务器请求日志, 外部访问审计", "device_software_id": "WEBAPP-19.0, HTTP-2.0", "vendor_name": "Vendor SSS", "harm_name": "内网访问, 数据泄露", "description": "攻击者诱导服务器请求内部资源", "prevention_measures": "验证请求目标并使用白名单", "attack_cause": "未限制请求目标或端口", "defective_device_software": "WEBAPP-19.0", "configuration_solution": "1. 请求目标白名单。 2. 禁止访问内网敏感地址。 3. 审计请求日志。"}, {"event_id": "ea139", "event_name": "缓存投毒", "maintenance_method": "缓存验证, 输入校验", "check_item": "缓存命中日志, 请求日志", "device_software_id": "WEBAPP-20.0, CACHE-3.0", "vendor_name": "Vendor TTT", "harm_name": "页面内容被篡改, 用户误导", "description": "攻击者向缓存注入恶意内容影响访问者", "prevention_measures": "验证缓存内容并过滤输入", "attack_cause": "缓存内容未校验或未过滤用户输入", "defective_device_software": "WEBAPP-20.0", "configuration_solution": "1. 缓存内容校验。 2. 用户输入过滤。 3. 缓存日志审计。"}]