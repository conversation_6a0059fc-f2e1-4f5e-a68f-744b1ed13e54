#!/usr/bin/env python
# encoding:utf-8
from . import *

@r.route("/login", methods=['GET', 'POST'])
def login():
    """
    用户登录接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 用户登录所需的账号和密码
        schema:
          type: object
          required:
            - account
            - passwd
          properties:
            account:
              type: string
              description: 用户账号或邮箱
            passwd:
              type: string
              description: 用户密码
    responses:
      200:
        description: 登录成功，返回 token、账号、昵称及用户 ID
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                token:
                  type: string
                account:
                  type: string
                nick_name:
                  type: string
                user_id:
                  type: string
      400:
        description: 用户不存在、密码错误或用户状态异常
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        account = request.json.get("account", "")
        passwd = request.json.get("passwd", "")

        user = Users.where('account', account).or_where('email', account).first()

        if user is None:
            return Response.re(err=ErrUserNot)

        if user.status == 1:
            return Response.re(err=ErrUserLoginSwitch)

        md5_password = Random.make_md5_password(string=str(passwd))

        if md5_password == user.passwd:
            token = "W5_TOKEN_" + Random.make_token(string=account)
            redis.set(token, str(user.id), ex=60 * 60 * 24 * 7)

            Users.where('id', user.id).update(
                {
                    "token": token,
                    "update_time": Time.get_date_time()
                }
            )

            return Response.re(data={"token": token, "account": user.account, "nick_name": user.nick_name,
                                     "user_id": user.id})
        else:
            return Response.re(err=ErrUserPassword)


