#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试step8_risk_scoring.py的集成功能
验证是否能正确从step7_behavior_analysis.py获取数据并返回结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step8_risk_scoring_fw import main as step8_main

def test_step8_integration():
    print("=== 测试step8_risk_scoring.py集成功能 ===")
    
    try:
        # 调用step8的main函数
        result_df, statistics_result = step8_main()
        
        if result_df is None or statistics_result is None:
            print("错误: step8_main()返回了None")
            return False
        
        print(f"\n成功获取到风险评分结果:")
        print(f"- 处理记录数: {len(result_df)}")
        print(f"- 结果DataFrame列数: {len(result_df.columns)}")
        
        # 检查关键列是否存在
        key_columns = ['hour_slot', 'srcAddress', 'conclusion', '_adjusted_threat_score']
        missing_cols = [col for col in key_columns if col not in result_df.columns]
        if missing_cols:
            print(f"警告: 缺少关键列: {missing_cols}")
        else:
            print("✓ 所有关键列都存在")
        
        # 显示结论分布
        if 'conclusion' in result_df.columns:
            conclusion_dist = result_df['conclusion'].value_counts()
            print(f"\n结论分布:")
            for conclusion, count in conclusion_dist.items():
                print(f"  {conclusion}: {count}条")
        
        # 显示威胁分数统计
        if '_adjusted_threat_score' in result_df.columns:
            threat_scores = result_df['_adjusted_threat_score']
            print(f"\n调整后威胁分数统计:")
            print(f"  最小值: {threat_scores.min():.1f}")
            print(f"  最大值: {threat_scores.max():.1f}")
            print(f"  平均值: {threat_scores.mean():.1f}")
        
        # 检查统计结果
        print(f"\n统计结果包含的项目:")
        for key in statistics_result.keys():
            print(f"  - {key}: {type(statistics_result[key])}")
        
        # 保存测试结果
        result_df.to_csv("test_step8_integration_result.csv", index=False)
        print(f"\n测试结果已保存到: test_step8_integration_result.csv")
        
        print("\n=== step8集成测试通过 ===")
        return True
        
    except Exception as e:
        print(f"错误: step8集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_step8_integration()