# 绿盟漏洞数据分析流程图

## 整体流程

```mermaid
graph TD
    A[原始数据: sourcedata.txt] --> B[步骤1: 数据解析]
    B --> C[解析后数据: step1_parsed_vuln_data.csv]
    C --> D[步骤2: 风险评分计算]
    D --> E1[详细评分: step2_vuln_detailed_scores.csv]
    D --> E2[IP风险汇总: step2_vuln_ip_risk_summary.csv]
    E1 --> F[步骤3: 报告生成]
    F --> G[简化报告: step3_vuln_detailed_scores_simplified.csv]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style E1 fill:#bbf,stroke:#333,stroke-width:2px
    style E2 fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
```

## 步骤1: 数据解析流程

```mermaid
graph TD
    A[读取sourcedata.txt] --> B[JSON解码器逐个解析]
    B --> C[提取漏洞信息字段]
    C --> D[转换为DataFrame]
    D --> E[保存为CSV文件]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

## 步骤2: 风险评分计算流程

```mermaid
graph TD
    A[读取解析后的漏洞数据] --> B[CVSS评分映射]
    B --> C[CVSS评分计算]
    A --> D[传统评分计算]
    A --> E[资产重要性评估]
    C --> F[综合评分计算]
    D --> F
    E --> F
    F --> G1[生成详细评分数据]
    F --> G2[生成IP风险汇总]
    
    style A fill:#bbf,stroke:#333,stroke-width:2px
    style G1 fill:#bbf,stroke:#333,stroke-width:2px
    style G2 fill:#bbf,stroke:#333,stroke-width:2px
```

### CVSS评分计算详细流程

```mermaid
graph TD
    A[映射CVSS指标] --> B[计算基础评分]
    B --> C[计算时间评分]
    C --> D[计算环境评分]
    D --> E[CVSS最终评分]
```

### 传统评分计算详细流程

```mermaid
graph TD
    A[基础漏洞评分] --> E[传统评分]
    B[漏洞密度评分] --> E
    C[危险漏洞评分] --> E
    D[CVE关联评分] --> E
```

## 步骤3: 报告生成流程

```mermaid
graph TD
    A[读取详细风险评分数据] --> B[选择重要字段]
    B --> C[格式化数值字段]
    C --> D[处理空值]
    D --> E[保存简化CSV文件]
    E --> F[显示数据统计和预览]
    
    style A fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
```

## 评分模型

```mermaid
graph TD
    A[CVSS评分 60%] --> D[综合评分]
    B[传统评分 30%] --> D
    C[环境因素 10%] --> D
    
    style D fill:#f96,stroke:#333,stroke-width:2px
```

## 传统评分组成

```mermaid
graph TD
    A[基础分 30%] --> E[传统评分]
    B[密度分 25%] --> E
    C[危险分 25%] --> E
    D[CVE关联分 20%] --> E
    
    style E fill:#f96,stroke:#333,stroke-width:2px
```