import pandas as pd
import json
import re
import os

def read_topsec_csv(path):
    """尽量稳健地读取 CSV：尝试多种编码，若无表头则自动给列名。"""
    df = None
    errors = []
    for enc in ("utf-8", "gb18030", "gbk"):
        try:
            try:
                df = pd.read_csv(path, encoding=enc)
            except Exception:
                df = pd.read_csv(path, encoding=enc, header=None)
            break
        except Exception as e:
            errors.append(f"{enc}: {e}")
    if df is None:
        raise RuntimeError("读取CSV失败：" + "; ".join(errors))
    return df

def extract_json_from_message(msg_text):
    """从 message 文本中尽量提取 JSON 对象（天融信在 syslog 后面跟 { ... }）。"""
    if msg_text is None:
        return None
    s = str(msg_text)
    m = re.search(r'\{.*\}', s, re.DOTALL)
    if not m:
        return None
    js = m.group(0).replace('""', '"')  # 简单修复双引号
    # 容错：如果缺右大括号，截断补齐
    if not js.endswith('}'):
        last = js.rfind(',')
        js = (js[:last] + '}') if last > 0 else (js + '}')
    try:
        return json.loads(js)
    except Exception:
        return None

def pick_message_column(df):
    """猜测 message 列：优先常见名，否则取最后一列。"""
    candidates = ["message", "msg", "log", "logmsg", "日志", "syslog test send message"]
    cols_lower = {str(c).strip().lower(): c for c in df.columns}
    for name in candidates:
        key = name.lower()
        if key in cols_lower:
            return cols_lower[key]
    return df.columns[-1]

def summarize_fw(path_in, path_out="fw_summary.csv"):
    df = read_topsec_csv(path_in)
    msg_col = pick_message_column(df)

    rows = []
    for _, row in df.iterrows():
        raw = row.get(msg_col, "")
        data = extract_json_from_message(raw)
        if not isinstance(data, dict):
            continue

        # 天融信字段多在 parsedInfos 里；若没有则尝试顶层
        info = data.get("parsedInfos", data)

        src = str(info.get("srcAddress", "")).strip()
        dst = str(info.get("destAddress", "")).strip()
        act = str(info.get("dvcAction", "")).strip().lower()  # accept/deny/drop/…
        if act == "":
            # 有些会话日志没有 dvcAction，用 name 推断（可选）
            name = str(info.get("name", "")).lower()
            act = "accept" if "通过" in name or "会话" in name else "unknown"

        if src == "" and dst == "" and act == "":
            continue

        rows.append({"srcAddress": src, "destAddress": dst, "action": act})

    if not rows:
        print("没有可用的日志记录。")
        return

    g = (pd.DataFrame(rows)
         .fillna("")
         .groupby(["srcAddress", "destAddress", "action"], dropna=False)
         .size()
         .reset_index(name="count")
         .sort_values(["count", "srcAddress", "destAddress"], ascending=[False, True, True]))

    g.to_csv(path_out, index=False, encoding="utf-8")
    print(f"已生成汇总：{path_out}")
    print("\n前20条预览：")
    print(g.head(20).to_string(index=False))

if __name__ == "__main__":
    input_file = "天融信.csv"  # 如有需要，改成你的路径
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"未找到输入文件：{input_file}")
    summarize_fw(input_file, "fw_summary.csv")
