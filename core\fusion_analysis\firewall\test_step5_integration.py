#!/usr/bin/env python
# encoding:utf-8
"""
测试step5_ip_threat_score.py与step4_work_time.py的集成
"""

import pandas as pd
from step5_ip_threat_score_fw import main as get_threat_score_data

def test_step5_integration():
    """
    测试step5能否正确调用step4的方法并处理数据
    """
    print("开始测试step5_ip_threat_score.py集成...")
    
    try:
        # 调用step5的main函数
        result_df = get_threat_score_data()
        
        if result_df is not None and not result_df.empty:
            print(f"✓ step5成功处理了 {len(result_df)} 条记录")
            
            # 检查是否包含威胁分数列
            if 'threat_score' in result_df.columns:
                threat_score_stats = result_df['threat_score'].describe()
                print(f"✓ 威胁分数统计: 最小值={threat_score_stats['min']:.1f}, 最大值={threat_score_stats['max']:.1f}, 平均值={threat_score_stats['mean']:.1f}")
            else:
                print("✗ 缺少威胁分数列")
                return False
            
            # 检查是否包含威胁等级列
            if 'threat_level' in result_df.columns:
                threat_level_stats = result_df['threat_level'].value_counts()
                print(f"✓ 威胁等级统计: {threat_level_stats.to_dict()}")
            else:
                print("✗ 缺少威胁等级列")
                return False
            
            # 检查是否包含攻击次数列
            if 'attack_count' in result_df.columns:
                attack_count_stats = result_df['attack_count'].describe()
                print(f"✓ 攻击次数统计: 最小值={attack_count_stats['min']:.0f}, 最大值={attack_count_stats['max']:.0f}, 平均值={attack_count_stats['mean']:.1f}")
            else:
                print("✗ 缺少攻击次数列")
                return False
            
            # 检查必需的基础列
            required_columns = ['hour_slot', 'srcAddress']
            missing_columns = [col for col in required_columns if col not in result_df.columns]
            if missing_columns:
                print(f"✗ 缺少必需列: {missing_columns}")
                return False
            else:
                print(f"✓ 包含所有必需列: {required_columns}")
            
            # 保存测试结果
            result_df.to_csv('test_step5_integration_result.csv', index=False)
            print("✓ 测试结果已保存至 test_step5_integration_result.csv")
            
            return True
        else:
            print("✗ step5未获取到数据")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_step5_integration()
    if success:
        print("\n🎉 step5集成测试通过！")
    else:
        print("\n❌ step5集成测试失败！")