import re

# 输入输出文件
INPUT_FILE = "schema_only.sql"
OUTPUT_FILE = "clean_schema.sql"

def extract_create_tables():
    with open(INPUT_FILE, 'r', encoding='utf-8') as f:
        content = f.read()

    # 移除所有 /*! ... */ 注释
    content = re.sub(r'/\*\!.*?\*/', '', content, flags=re.DOTALL)
    # 移除 -- 和 # 注释
    content = re.sub(r'--.*?$', '', content, flags=re.MULTILINE)
    content = re.sub(r'#.*?$', '', content, flags=re.MULTILINE)

    # 匹配完整的 CREATE TABLE ... );
    # 支持跨行、ENGINE、COLLATE 等
    pattern = re.compile(
        r'(CREATE\s+TABLE\s+'
        r'(?:IF\s+NOT\s+EXISTS\s+)?'
        r'`?(\w+)`?\s*$$.*?^$$\s*'
        r'(?:ENGINE[^;]*?;))',
        re.DOTALL | re.IGNORECASE | re.MULTILINE
    )

    matches = pattern.findall(content)
    if not matches:
        print("❌ 未找到任何 CREATE TABLE 语句")
        return

    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        f.write("-- 提取自 mysqldump 的纯 DDL 结构\n")
        f.write("-- 仅包含 CREATE TABLE 语句\n\n")
        for full_sql, table_name in matches:
            f.write(full_sql.strip() + "\n\n")
            print(f"✅ 提取表: {table_name}")

    print(f"\n✅ 所有表结构已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    extract_create_tables()