from datetime import datetime
import requests
import uuid
#服务器
BASE_URL = 'https://**************:443/'
#本机
# BASE_URL = 'https://*************:12601/'
MESSAGE_ENDPOINT = 'rest/rules/es/'
CONTENT = 'alerts_tail/'
COUNT = 'alerts_count/'
SOURCE_ENDPOINT = '/rules/source/add_public'
TOKEN = '4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Referer': 'https://*************:12601/evebox/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Content-Type' : 'application/json',
    # 'Authorization' : 'Token 4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
}

COOKIE = {
    'csrftoken': 'kTBdzm8fBf6yBr8KCJnG5t3EHBilXRdzP3QAKeAp69GLFIcvvPlcDMNTh9pIX3VH',
    'sessionid': '0i7272936g7x27k0bivsvv0jtp5moxuj'
}

def get_selks_info(start_time_str=None, end_time_str=None):
    url = f"{BASE_URL}evebox/api/1/alerts?tags=-evebox.archived"
    print(start_time_str, end_time_str)
    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False)
        original_data = response.json()
        filtered_items = []
        low_cnt = 0
        medium_cnt = 0
        high_cnt = 0


        for event in original_data.get("events", []):
            event_id = event.get("_id")
            if not event_id:
                continue

            # 处理时间
            detect_time = event.get("_source", {}).get("timestamp")
            if not detect_time:
                continue

            # 解析事件时间（带时区）
            detect_datetime = datetime.strptime(detect_time, "%Y-%m-%dT%H:%M:%S.%f%z")
            
            # 时间范围过滤
            if start_time_str:
                start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
                if detect_datetime.replace(tzinfo=None) < start_time:
                    continue
            
            if end_time_str:
                end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M")
                if detect_datetime.replace(tzinfo=None) > end_time:
                    continue

            # 构建事件项
            item = {
                "dst_ip": event.get("_source", {}).get("dest_ip"),
                "detect_time": detect_datetime.strftime("%Y-%m-%d %H:%M:%S"),  # 格式化输出
                "src_ip": event.get("_source", {}).get("src_ip"),
                "dst_port": "",
                "src_port": "",
                "事件类型": "",
                "级别": "",
                "事件描述": "",
                "detail": "",
                "detection_system" : "ids",
                "alert_id" : str(uuid.uuid4())
                # "中危攻击数": "",
                # "低危攻击数": "",
                # "高危攻击数": "",
            }

            # 获取详情
            detail_url = f'{BASE_URL}evebox/api/1/event/{event_id}'
            try:
                detail_response = requests.get(detail_url, headers=HEADERS, cookies=COOKIE, verify=False)
                if detail_response.status_code == 200:
                    detail_json = detail_response.json()
                    item.update({
                        '事件类型': detail_json.get("_source", {}).get("alert", {}).get("category"),
                        '级别': detail_json.get("_source", {}).get("alert", {}).get("metadata", {}).get("confidence", [None])[0],
                        '事件描述': detail_json.get("_source", {}).get("alert", {}).get("signature"),
                        'dst_port': detail_json.get("_source", {}).get("dest_port"),
                        'src_port': detail_json.get("_source", {}).get("flow", {}).get("src_port"),
                        'detail' : detail_json
                    })
                if item['级别'] == "Medium":
                    medium_cnt += 1
                elif item['级别'] == "Low":
                    low_cnt += 1
                elif item['级别'] == "High":
                    high_cnt += 1
            except Exception as e:
                item["事件描述"] = f"Detail fetch error: {str(e)}"

            filtered_items.append(item)

        print("yes,selks is good")
        return {
            #"code": "200",
            "data": filtered_items,  
            "count":{
                "中危攻击数": medium_cnt,
                "低危攻击数": low_cnt,
                "高危攻击数": high_cnt,
                "总攻击数": medium_cnt+low_cnt+high_cnt
            }
            #"msg": "success"
        }

    except Exception as err:
        print("selks_error")
        return {
            #"code": "500",
            "data": [],
            "count":{
                "中危攻击数": 0,
                "低危攻击数": 0,
                "高危攻击数": 0,
                "总攻击数": 0
            }
            #"msg": str(err)
        }

def get_events_number_day_list(day_number=7):
    response_data = {
        "code": "200",
        "data": [],
        "msg": "success"
    }

    # 计算过去N天的日期范围
    today = datetime.utcnow().date()
    date_list = [today - timedelta(days=i) for i in range(int(day_number))]

    try:
        daily_counts = []
        for date in date_list:
            # 计算当天的起止时间（UTC）
            start_time = datetime.combine(date, datetime.min.time())
            end_time = datetime.combine(date, datetime.max.time())

            # 转换为时间戳（毫秒）
            from_timestamp_ms = int(start_time.timestamp() * 1000)
            to_timestamp_ms = int(end_time.timestamp() * 1000)

            # 构建请求URL
            url = f'{BASE_URL}{MESSAGE_ENDPOINT}{COUNT}?prev=1&from_date={from_timestamp_ms}&to_date={to_timestamp_ms}'

            # 发起请求
            response = requests.get(
                url,
                headers=HEADERS,
                cookies=COOKIE,
                verify=False,
                timeout=10
            )

            if response.status_code != 200:
                raise Exception(f"外部接口错误：{response.text[:100]}")

            # 解析响应
            response_json = response.json()
            daily_counts.append({
                "date": date.strftime("%Y-%m-%d"),  # 格式化日期
                "count": response_json.get("doc_count", 0)  # 默认0，避免KeyError
            })

        response_data["data"] = daily_counts
        return response_data

    except requests.Timeout:
        return {
            "code": "504",
            "data": [],
            "msg": "请求外部接口超时"
        }
    except requests.RequestException as e:
        return {
            "code": "500",
            "data": [],
            "msg": f"请求失败：{str(e)}"
        }
    except Exception as e:
        return {
            "code": "500",
            "data": [],
            "msg": f"服务器内部错误：{str(e)}"
        }

