#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

import time
import datetime
from loguru import logger

# 导入tianyan模块
try:
    from .query_list import create_query_manager
except ImportError:
    # 尝试绝对导入
    try:
        from query_list import create_query_manager
    except ImportError as e:
        logger.error(f"[天眼告警查询] 导入tianyan模块失败: {e}")
        create_query_manager = None


def unix_timestamp_to_beijing_time(timestamp):
    """
    将unix时间戳转换为北京时间格式
    
    Args:
        timestamp (int): unix时间戳（秒或毫秒）
        
    Returns:
        str: 格式化的北京时间字符串，如"7月21日11:30"
    """
    try:
        # 判断是秒还是毫秒时间戳
        if timestamp > 10**10:  # 毫秒时间戳
            timestamp = timestamp / 1000
        
        # 转换为北京时间 (UTC+8)
        dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone(datetime.timedelta(hours=8)))
        
        # 格式化为"x月x日xx:xx"
        return dt.strftime("%m月%d日%H:%M").lstrip('0').replace('月0', '月')
    except Exception as e:
        logger.error(f"[天眼告警查询] 时间转换失败: {e}")
        return str(timestamp)


def map_table_name_to_chinese(table_name):
    """
    将告警类型映射为中文
    
    Args:
        table_name (str): 原始表名
        
    Returns:
        str: 中文告警类型
    """
    mapping = {
        'webids_alert': '网页漏洞利用',
        'ips_alert': '网络攻击'
    }
    return mapping.get(table_name, '其他攻击')


def format_alert_data(alerts_data):
    """
    格式化告警数据

    Args:
        alerts_data (dict): 原始告警数据

    Returns:
        dict: 格式化后的告警数据
    """
    if not alerts_data or not alerts_data.get('items'):
        return {
            'total': 0,
            'alerts': []
        }

    formatted_alerts = []

    for item in alerts_data['items']:
        try:
            # 提取所需字段
            access_time = item.get('access_time')
            alarm_sip = item.get('alarm_sip', item.get('dip', 'Unknown'))  # 受害IP (目标IP)
            attack_sip = item.get('attack_sip', item.get('sip', 'Unknown'))  # 攻击IP (源IP)
            table_name = item.get('table_name', 'unknown')
            rule_name = item.get('rule_name', item.get('threat_name', 'Unknown'))
            host_state = item.get('host_state', 'Unknown')  # 攻击结果
            hazard_level = item.get('hazard_level', 'Unknown')  # 威胁级别
            repeat_count = item.get('repeat_count', item.get('count', 1))  # 次数

            # 格式化数据
            formatted_alert = {
                'access_time': unix_timestamp_to_beijing_time(access_time) if access_time else 'Unknown',  # 最近发生时间
                'alarm_sip': alarm_sip,  # 受害IP
                'attack_sip': attack_sip,  # 攻击IP
                'table_name': map_table_name_to_chinese(table_name),  # 告警类型
                'rule_name': rule_name,  # 威胁名称
                'host_state': host_state,  # 攻击结果
                'hazard_level': hazard_level,  # 威胁级别
                'repeat_count': repeat_count  # 次数
            }

            formatted_alerts.append(formatted_alert)

        except Exception as e:
            logger.error(f"[天眼告警查询] 格式化告警数据失败: {e}")
            continue

    return {
        'total': len(formatted_alerts),
        'alerts': formatted_alerts
    }


async def query_alerts(base_url, username, password, minutes_ago=5, limit=50):
    """
    查询天眼告警信息

    Args:
        base_url (str): 天眼平台地址
        username (str): 登录用户名
        password (str): 登录密码
        minutes_ago (int): 查询多少分钟前的告警，默认5分钟
        limit (int): 返回告警数量限制，默认50条

    Returns:
        dict: 包含状态和结果的字典
    """
    logger.info(f"[天眼告警查询] 开始查询告警，参数: base_url={base_url}, username={username}, minutes_ago={minutes_ago}, limit={limit}")
    
    try:
        # 检查模块导入
        if create_query_manager is None:
            logger.error("[天眼告警查询] 核心模块导入失败")
            return {
                "status": 2,
                "result": "核心模块导入失败，请检查tianyan模块是否正确安装"
            }

        # 检查依赖
        try:
            import requests
            import ddddocr
        except ImportError as e:
            logger.error(f"[天眼告警查询] 缺少依赖模块: {e}")
            return {
                "status": 2,
                "result": f"缺少依赖模块，请安装: pip install requests ddddocr"
            }
        
        # 创建查询管理器（自动登录）
        logger.info("[天眼告警查询] 正在登录天眼平台...")
        query_manager = create_query_manager(
            base_url=base_url,
            username=username,
            password=password,
            max_retries=5,
            verbose=False
        )
        
        if not query_manager:
            logger.error("[天眼告警查询] 登录失败")
            return {
                "status": 2,
                "result": "登录天眼平台失败，请检查地址、用户名和密码"
            }
        
        logger.info("[天眼告警查询] 登录成功，开始查询告警...")

        # 确保参数类型正确
        try:
            minutes_ago = int(minutes_ago) if minutes_ago is not None else 5
            limit = int(limit) if limit is not None else 50
        except (ValueError, TypeError) as e:
            logger.error(f"[天眼告警查询] 参数类型转换失败: {e}")
            return {
                "status": 2,
                "result": f"参数类型错误: minutes_ago和limit必须是数字类型"
            }

        # 查询指定时间范围内的告警
        alerts_data = query_manager.get_alerts_by_minutes_range(
            minutes_ago=minutes_ago,
            limit=limit
        )
        
        if not alerts_data:
            logger.warning("[天眼告警查询] 查询告警失败")
            return {
                "status": 2,
                "result": "查询告警失败，请检查网络连接和权限"
            }
        
        # 格式化告警数据
        formatted_result = format_alert_data(alerts_data)
        
        logger.info(f"[天眼告警查询] 查询完成，共获取到 {formatted_result['total']} 条告警")
        
        return {
            "status": 0,
            "result": formatted_result
        }
        
    except Exception as e:
        logger.error(f"[天眼告警查询] 执行异常: {e}")
        return {
            "status": 2,
            "result": f"查询告警异常: {str(e)}"
        }
