import os
import sys
import pandas as pd
import numpy as np
import time

def run_step(step_name, script_name):
    """
    运行指定步骤的脚本
    """
    print(f"\n{'='*50}")
    print(f"开始执行 {step_name}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        # 导入并执行脚本的main函数
        module_name = script_name.replace('.py', '')
        result = __import__(module_name).main()
        
        end_time = time.time()
        print(f"\n{step_name} 执行完成，耗时 {end_time - start_time:.2f} 秒")
        return True, result
    except Exception as e:
        print(f"\n{step_name} 执行失败: {str(e)}")
        return False, None

def main():
    print("开始执行IP流向和误报判断数据分析流程...\n")
    
    # 定义步骤和对应的脚本
    steps = [
        ("步骤2: IP流向判定", "step2_flow_direction_fw.py"),
        ("步骤3: 业务相关性分析", "step3_business_analysis_fw.py"),
        ("步骤4: 工作时间标记", "step4_work_time_fw.py"),
        ("步骤5: IP威胁分数生成", "step5_ip_threat_score_fw.py"),
        ("步骤6: 误报处理", "step6_false_positive_fw.py"),
        ("步骤7: 行为分析", "step7_behavior_analysis_fw.py"),
        ("步骤8: 风险打分与结论", "step8_risk_scoring_fw.py"),
        ("步骤9: 告警类型和攻击结果分析", "step9_alert_analysis_fw.py")
    ]
    
    # 注意：现在step2_flow_direction.py直接从数据库获取数据，不再依赖CSV文件
    print("注意：数据将直接从数据库获取，无需预先准备CSV文件")
    
    # 依次执行各步骤
    step9_result = None
    for step_name, script_name in steps:
        success, result = run_step(step_name, script_name)
        if not success:
            print(f"\n{step_name} 失败，流程中断")
            return
        
        # 保存step9的结果用于后续处理
        if script_name == "step9_alert_analysis_fw.py":
            step9_result = result
    
    print("\n所有步骤执行完成！")
    
    # 处理step9的结果
    if step9_result and len(step9_result) == 2:
        df, statistics = step9_result
        
        # 保存最终结果到CSV文件
        if df is not None and not df.empty:
            output_file = "step9_alert_analysis_result.csv"
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n最终结果已保存到: {output_file}")
        
        # 显示统计信息
        if statistics and isinstance(statistics, dict):
            print("\n=== 攻击结果和置信度统计 ===")
            
            # 攻击结果统计
            if 'attack_result_counts' in statistics:
                attack_stats = statistics['attack_result_counts']
                print("\n攻击结果统计:")
                for result, count in attack_stats.items():
                    print(f"{result}: {count}条记录")
            
            # 置信度统计
            if 'confidence_counts' in statistics:
                confidence_stats = statistics['confidence_counts']
                print("\n置信度统计:")
                for conf, count in confidence_stats.items():
                    print(f"{conf}: {count}条记录")
    else:
        print("\n警告: step9未返回有效结果")


if __name__ == "__main__":
    main()