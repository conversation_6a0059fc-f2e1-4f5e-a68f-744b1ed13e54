# 奇安信安全分析算法流程整合文档

## 1. 系统概述

本文档整合了奇安信安全分析系统的完整算法流程，该系统基于多维度分析框架，通过IP流向、时间特征、业务相关性、行为模式和威胁评分等多个维度，实现对安全事件的精准分类和误报过滤。

## 2. 数据处理流程

整个系统按照以下步骤顺序处理数据：

```mermaid
graph TD
    A[原始日志] --> B(step1: 日志解析)
    B --> C(step2: IP流向分析)
    C --> D(step3: 业务相关性分析)
    D --> E(step4: 工作时间标记)
    E --> F(step5: 威胁评分)
    F --> G(step6: 误报过滤)
    G --> H(step7: 行为分析)
    H --> I(step8: 风险定级)
    I --> J(step9: 综合研判)
```

## 3. 各步骤详细说明

### 3.1 日志解析 (step1_parse_security_logs.py)

**功能**：解析原始安全日志，提取关键字段和特征。

**主要处理**：
- 提取时间、IP地址、端口等关键字段
- 映射事件类型和严重性等级
- 标准化日志格式，为后续分析做准备

**输出**：enhanced_security_logs.csv

### 3.2 IP流向分析 (step2_flow_direction.py)

**功能**：判定网络流量的方向特征。

**主要处理**：
- 区分内网和外网IP地址
- 将流量分类为：内对内、内到外、外到内和外对外四种模式
- 以 `['hour_slot','srcAddress']` 分组，得到**该小时内该源IP**的画像特征

**输出**：step2_flow_direction.csv

### 3.3 业务相关性分析 (step3_business_analysis.py)

**功能**：分析流量与业务系统的相关性。

**主要处理**：
- HTTP协议深度解析
- 识别业务系统访问特征
- 标记业务相关和非业务相关流量

**输出**：step3_business_analysis.csv

### 3.4 工作时间标记 (step4_work_time.py)

**功能**：标记事件是否发生在正常工作时间。

**主要处理**：
- 判断事件是否发生在工作日8:30-17:30
- 非工作时间访问检测
- 异常时段活动标记

**输出**：step4_work_time.csv

### 3.5 威胁评分 (step5_temp.py)

**功能**：生成IP威胁分数。

**主要处理**：
- 基于多维特征计算基础威胁分数
- 考虑事件严重性和频率
- 生成初步威胁评分

**理论依据**：
威胁评分系统基于**风险评估理论**和**威胁情报分析**方法，通过综合多种因素为IP地址分配风险分数。相关学术研究包括：

1. Fachkha, C., & Debbabi, M. (2016). "Darknet as a Source of Cyber Intelligence: Survey, Taxonomy, and Characterization". IEEE Communications Surveys & Tutorials, 18(2), 1197-1227. 
   - 该研究第3章详细分析了暗网情报源的特性和价值，提出了基于多维度特征的IP信誉评分框架
   - 第4.2节提出了基于时间序列的异常检测方法，证明了非工作时间的异常活动具有更高的威胁性
   - 第5.3节讨论了IP地址行为模式分析方法，为我们的持续性因子和成簇因子提供了理论基础

2. Soldo, F., Le, A., & Markopoulou, A. (2011). "Predictive Blacklisting as an Implicit Recommendation System". IEEE INFOCOM. 
   - 该论文第2节提出了一种预测性黑名单方法，将IP信誉评分视为隐式推荐系统问题
   - 第3.1节详细阐述了基于历史攻击模式的IP风险评估方法，证明了攻击频率与未来风险的相关性
   - 第4.2节验证了将流量方向作为关键特征的有效性，特别是外到内流量的高风险特性

3. Collins, M. P., Shimeall, T. J., Faber, S., Janies, J., Weaver, R., De Shon, M., & Kadane, J. (2007). "Using Uncleanliness to Predict Future Botnet Addresses". 
   - 该研究第2章提出了基于历史行为预测未来威胁的方法，引入了"不洁度"(uncleanliness)概念
   - 第3.3节详细描述了IP地址行为特征提取方法，包括活动持续时间、交互IP数量等关键指标
   - 第4.1节验证了多因素加权模型在预测未来威胁方面的有效性，为我们的多因素调整提供了理论支持

**计算模型**：
威胁分数计算采用多层次加权模型，包括基础分值计算和多因素调整两个主要阶段：

1. **基础分值计算**：
   基于事件严重性、事件类型、流量方向和时间特征计算初始威胁分数：
   ```python
   # 基础分值 - 基于严重性平均值
   base_score = group['severity'].astype(float).mean() * 10
   
   # 事件类型调整
   event_type_factor = 1.0
   if 'eventType' in group.columns:
       # 事件类型为1(成功)的权重更高
       event_type_1_count = (group['eventType'] == 1).sum()
       if event_type_1_count > 0:
           event_type_factor = 1.2
   
   # 流向调整
   flow_factor = 1.0
   if 'flow_direction' in group.columns:
       # 外到内流量权重更高
       external_to_internal = (group['flow_direction'] == "外到内").sum()
       if external_to_internal > 0:
           flow_factor = 1.3
   
   # 非工作时间调整
   time_factor = 1.0
   if 'is_work_time' in group.columns:
       # 非工作时间的事件权重更高
       non_work_time = (~group['is_work_time']).sum()
       if non_work_time > group.shape[0] * 0.7:  # 如果70%以上是非工作时间
           time_factor = 1.2
   
   # 获取该IP的攻击次数
   attack_count = ip_attack_counts.get(src_ip, 0)
   
   # 根据攻击次数增加评分
   attack_count_factor = 1.0
   if attack_count > 10:
       attack_count_factor = 1.3
   elif attack_count > 5:
       attack_count_factor = 1.15
   elif attack_count > 2:
       attack_count_factor = 1.05
   
   # 计算威胁分数
   threat_score = base_score * event_type_factor * flow_factor * time_factor * attack_count_factor
   ```

2. **多因素调整**：
   在基础分值的基础上，通过提取IP行为特征，应用多个调整因子进一步优化威胁分数：

   a. **行为特征提取**：
   ```python
   def _derive_group_metrics(group):
       # 事件频次（分组内记录数）
       event_frequency = int(group.shape[0])
       
       # 工作时间占比
       work_time_ratio = _safe_get_ratio((group['is_work_time'] == True).sum(), event_frequency, 0.5)
       
       # 连续运行小时数（基于hour_slot连续性推断）
       current_run_hours = _calculate_continuous_hours(group['hour_slot'])
       
       # 72小时内活跃小时数
       active_hours_72h = len(set(group['hour_slot'].astype(str)))
       
       # 判断未来24小时是否活跃
       has_next_24h = _check_future_activity(group['srcAddress'])
       
       # 每小时交互的最多目标IP数量
       hour_peer_ip_count = group.groupby(['hour_slot'])['dstAddress'].nunique().max()
       
       # 流量方向的多样性
       mixed_flow_modes = int(group['flow_direction'].nunique())
       
       return {
           'work_time_ratio': float(work_time_ratio),
           'event_frequency': int(event_frequency),
           'current_run_hours': int(current_run_hours),
           'active_hours_72h': int(active_hours_72h),
           'has_next_24h': bool(has_next_24h),
           'hour_peer_ip_count': int(hour_peer_ip_count),
           'mixed_flow_modes': int(mixed_flow_modes),
       }
   ```

   b. **调整因子计算**：
   ```python
   # 时间因子：工作时间占比高降低分数，占比低提高分数
   def _calc_time_factor(work_time_ratio):
       if work_time_ratio > 0.7:
           return -10  # 工作时间为主，降低10分
       if work_time_ratio < 0.3:
           return +10  # 非工作时间为主，提高10分
       return 0
   
   # 频率因子：事件频次越高分数提升越多，频次极低降低分数
   def _calc_frequency_factor(event_frequency):
       if event_frequency >= 10:
           return +15  # 高频事件，提高15分
       if event_frequency >= 5:
           return +10  # 中频事件，提高10分
       if event_frequency <= 1:
           return -5   # 低频事件，降低5分
       return 0
   
   # 持续性因子：连续时间长、活跃时间多、未来仍活跃的IP分数更高
   def _calc_persistence_factor(current_run_hours, active_hours_72h, has_next_24h, event_frequency):
       adj = 0
       if current_run_hours >= 3:        # 连续≥3小时
           adj += 10
       if active_hours_72h >= 12:        # 72h内活跃≥12小时
           adj += 10
       if has_next_24h:                  # 后24h仍有告警
           adj += 5
       if event_frequency <= 1 and not has_next_24h:  # 单次且没后续
           adj -= 5
       return adj
   
   # 成簇/混合因子：交互IP多、流量方向多样的IP分数更高
   def _calc_cluster_factor(hour_peer_ip_count, mixed_flow_modes):
       adj = 0
       if hour_peer_ip_count >= 5:       # 同小时交互≥5个IP
           adj += 5
       if mixed_flow_modes >= 2:         # 存在≥2种流向
           adj += 5
       return adj
   ```

   c. **调整因子应用**：
   ```python
   def _compose_adjusted_score(threat_score, metrics):
       # 计算各调整因子
       time_factor = _calc_time_factor(metrics['work_time_ratio'])
       frequency_factor = _calc_frequency_factor(metrics['event_frequency'])
       persistence_factor = _calc_persistence_factor(
           metrics['current_run_hours'],
           metrics['active_hours_72h'],
           metrics['has_next_24h'],
           metrics['event_frequency'],
       )
       cluster_factor = _calc_cluster_factor(
           metrics['hour_peer_ip_count'],
           metrics['mixed_flow_modes'],
       )
       
       # 应用调整因子到原始威胁分数
       adjusted = float(threat_score) + time_factor + frequency_factor + persistence_factor + cluster_factor
       
       # 确保分数在0-100范围内
       return {
           'time_factor': time_factor,
           'frequency_factor': frequency_factor,
           'persistence_factor': persistence_factor,
           'cluster_factor': cluster_factor,
           '_adjusted_threat_score': round(max(0, min(100, adjusted)), 1)
       }
   ```

3. **威胁等级划分**：
   根据计算得到的威胁分数，将IP风险划分为不同等级：
   ```python
   bins = [0, 30.0, 60.0, 75.0, 80.0, 90.0]
   labels = ['误报', '正常', '低风险', '中风险', '高风险']
   result_df['threat_level'] = pd.cut(result_df['threat_score'], bins=bins, labels=labels, include_lowest=True)
   ```

**输出**：step5_ip_threat_score.csv

### 3.6 误报过滤 (step6_false_positive.py)

**功能**：识别和过滤可能的误报。

**主要处理**：
- 基于IP信誉库过滤
- 低风险业务流量排除
- 可信来源白名单机制
- 生成已知误报IP列表
- 基于IP流向的结论规则应用

**理论依据**：
误报处理系统基于**多维度误报降低理论**、**上下文感知安全分析**和**行为模式识别**，整合了多个安全领域的前沿研究成果：

1. Sommer, R., & Paxson, V. (2010). "Outside the Closed World: On Using Machine Learning for Network Intrusion Detection". IEEE Symposium on Security and Privacy. 该研究分析了网络入侵检测中的误报问题，特别指出了"封闭世界假设"的局限性，强调了将业务上下文纳入安全分析的重要性，为我们的业务相关性规则和环境上下文规则集提供了理论框架。

2. Paxson, V. (2001). "The Power of Statistics in Network Anomaly Detection". IEEE Network. 该研究详细探讨了统计方法在网络异常检测中的应用，提出了基于时间序列分析的异常检测方法，证明了周期性行为模式识别的有效性，为我们的内网周期性通信规则和季节性业务活动规则提供了理论支持。

3. Julisch, K. (2003). "Using Root Cause Analysis to Handle Intrusion Detection Alarms". PhD Thesis, University of Dortmund. 该研究提出了基于根本原因分析处理入侵检测警报的方法，特别是警报聚类和根因分析技术，为我们的混合流量规则集和历史行为分析规则集提供了方法论基础。

4. Perdisci, R., Ariu, D., Fogla, P., Giacinto, G., & Lee, W. (2009). "McPAD: A Multiple Classifier System for Accurate Payload-based Anomaly Detection". Computer Networks, 53(6), 864-881. 该研究提出了多分类器系统用于精确的负载异常检测，证明了多层次、多维度分析在减少误报方面的优势，为我们的多层次误报识别模型提供了技术支持。

5. Kruegel, C., & Vigna, G. (2003). "Anomaly Detection of Web-based Attacks". ACM Conference on Computer and Communications Security. 该研究专注于Web攻击的异常检测，提出了基于HTTP协议特征的误报过滤方法，为我们的软件更新外联规则和HTTP协议业务相关性识别提供了理论依据。

6. Valeur, F., Vigna, G., Kruegel, C., & Kemmerer, R. A. (2004). "A Comprehensive Approach to Intrusion Detection Alert Correlation". IEEE Transactions on Dependable and Secure Computing, 1(3), 146-169. 该研究提出了全面的入侵检测警报关联方法，特别是上下文关联和多步攻击识别技术，为我们的环境上下文规则集和混合流量规则集提供了理论支持。

7. Chandola, V., Banerjee, A., & Kumar, V. (2009). "Anomaly Detection: A Survey". ACM Computing Surveys, 41(3), 1-58. 该综述研究全面分析了异常检测领域的各种方法，特别是基于统计、基于距离和基于密度的方法在网络安全中的应用，为我们的多维度误报识别模型提供了理论基础。

**误报识别模型**：
系统采用多层次、多维度的基于规则的误报识别模型，通过综合分析流量特征、时间模式、业务相关性和历史行为，精确识别各类误报场景。主要规则集包括：

1. **内网流量规则集**：
   - **内网单次事件规则**：工作时间内的单次内网事件（频率=1，工作时间占比>60%，持续时间=1小时）
   - **内网低风险流量规则**：纯内网低风险流量（威胁分数<40）
   - **内网中等风险流量规则**：纯内网中等风险流量（威胁分数<60），但无异常行为特征
   - **内网周期性通信规则**：定时执行的内网通信（每天固定时间段出现，持续时间稳定）
   - **内网广播流量规则**：识别网络发现和服务广播（目标IP为广播地址，协议为标准发现协议）

2. **外联流量规则集**：
   - **业务相关正常外联规则**：工作时间内的业务相关正常外联请求（业务相关占比>70%，工作时间占比>60%，威胁分数<65，持续时间<3小时）
   - **非工作时间业务外联规则**：非工作时间的业务相关请求（业务相关占比>70%，事件频率<5，持续时间<3小时）
   - **低风险监控外联规则**：低威胁分数的外联流量（威胁分数<50），可能为监控或巡检
   - **定期数据同步规则**：固定时间段的数据同步行为（每天固定时间出现，流量模式一致）
   - **软件更新外联规则**：识别软件更新和补丁下载（目标为已知更新服务器，HTTP/HTTPS协议）

3. **外部访问规则集**：
   - **可信来源访问规则**：来自可信来源的流量（源IP在白名单中，威胁分数<70）
   - **工作时间可信来源规则**：工作时间内来自可信来源的流量（工作时间占比>60%）
   - **高分值可信来源规则**：来自可信来源但分值较高的流量（威胁分数≥70，事件频率≤5，持续时间<3小时）
   - **单次外部访问规则**：单次外部访问事件（频率=1，持续时间=1小时，威胁分数<60）
   - **已知合作伙伴访问规则**：来自合作伙伴的访问（源IP属于合作伙伴网段，访问特定业务系统）

4. **混合流量规则集**：
   - **工作时间单次混合流量规则**：工作时间内的单次低风险混合流量事件（工作时间占比>60%，频率=1，持续时间=1小时，威胁分数<55）
   - **正常业务混合流量规则**：正常的业务相关混合流量（业务相关占比>60%，威胁分数<55）
   - **已知应用行为模式规则**：符合已知应用行为特征的混合流量（流量模式匹配已知应用特征库）

5. **历史行为分析规则集**：
   - **重复历史模式规则**：与历史良性行为高度匹配的事件（行为特征相似度>85%）
   - **季节性业务活动规则**：识别周期性业务活动（如月末报表生成、季度数据处理）
   - **已知误报IP规则**：历史上已确认为误报的IP地址（在已知误报IP列表中）

6. **可信来源白名单**：
   - **内部服务器白名单**：关键内部服务器（监控服务器、应用服务器、数据库服务器等）
   - **外部可信服务白名单**：可信的外部服务（云服务提供商、合作伙伴API、更新服务器等）
   - **安全设备白名单**：内部安全设备（防火墙、IDS/IPS、EDR等）
   - **管理网段白名单**：网络管理和运维专用网段

7. **环境上下文规则集**：
   - **维护窗口规则**：计划内维护时间的异常流量（在已定义的维护窗口内）
   - **已知网络变更规则**：网络架构变更后的适应性流量（与变更计划相关的异常流量）
   - **新应用部署规则**：新应用上线期间的学习期流量（与应用部署时间相关）

**误报判断实现方法**：
系统通过以下步骤实现误报判断：

1. **特征提取与聚合**：
   ```python
   # 按小时和源IP分组获取聚合特征
   grouped = df.groupby(['hour_slot', 'srcAddress'])
   
   # 计算每个组的特征
   agg_data = []
   for (hour_slot, src_ip), group in grouped:
       # 计算工作时间占比
       work_time_ratio = group['is_work_time'].mean() if 'is_work_time' in group.columns else 0
       
       # 计算业务相关性占比
       business_related_ratio = group['is_business_related'].mean() if 'is_business_related' in group.columns else 0
       
       # 计算事件频率
       event_frequency = len(group)
       
       # 获取流向类型
       flow_directions = group['flow_direction'].unique() if 'flow_direction' in group.columns else []
       is_internal_only = all(fd == "内对内" for fd in flow_directions)
       is_internal_to_external = any(fd == "内到外" for fd in flow_directions)
       is_external_to_internal = any(fd == "外到内" for fd in flow_directions)
       is_mixed_flow = len(flow_directions) >= 2
       
       # 估计连续小时数
       current_run_hours = 1  # 默认为1，实际应该从时间序列计算
       
       # 估计同小时同IP数量
       hour_peer_ip_count = 1  # 默认为1，实际应该计算
       
       agg_data.append({
           'hour_slot': hour_slot,
           'srcAddress': src_ip,
           'work_time_ratio': work_time_ratio,
           'business_related_ratio': business_related_ratio,
           'event_frequency': event_frequency,
           'is_internal_only': is_internal_only,
           'is_internal_to_external': is_internal_to_external,
           'is_external_to_internal': is_external_to_internal,
           'is_mixed_flow': is_mixed_flow,
           'current_run_hours': current_run_hours,
           'hour_peer_ip_count': hour_peer_ip_count
       })
   ```

2. **基于IP流向的误报判断规则**：
   ```python
   # 创建可信来源白名单
   trusted_sources = set([
       '**********',  # 假设这是可信的外部服务
       # 可以添加更多可信来源
   ])
   
   # 基于规则判断可能的误报
   def determine_false_positive(row):
       # A) 仅内对内
       if row['is_internal_only']:
           # 若单次且工作时间占比高且无连续
           if row['event_frequency'] == 1 and row['work_time_ratio'] > 0.6 and row['current_run_hours'] == 1:
               return True  # "误报 - 工作时间内的单次内网事件"
           # 否则看调整分
           elif row['threat_score'] < 40:
               return True  # "误报 - 纯内网低风险流量"
           elif row['threat_score'] < 60:
               return True  # "可能误报 - 内网中等风险流量，建议复核"
       
       # B) 内到外
       elif row['is_internal_to_external'] and not row['is_external_to_internal']:
           # 若业务相关性高且工作时间为主
           if row['business_related_ratio'] > 0.7 and row['work_time_ratio'] > 0.6:
               if row['threat_score'] < 65 and row['current_run_hours'] < 3:
                   return True  # "误报 - 工作时间内的业务相关正常外联请求"
               else:
                   return True  # "可能误报 - 业务相关但分值/持续性较高，建议复核"
           # 若业务相关性高且非工作时间为主
           elif row['business_related_ratio'] > 0.7 and row['work_time_ratio'] <= 0.6:
               if not (row['event_frequency'] > 5 or row['current_run_hours'] >= 3):
                   return True  # "可能误报 - 非工作时间的业务相关请求，建议抽查"
           # 其他情况（看分）
           elif row['threat_score'] < 50:
               return True  # "低危 - 可能为监控/巡检等正常外联"
       
       # C) 外到内
       elif row['is_external_to_internal'] and not row['is_internal_to_external']:
           # 若源IP在可信来源白名单
           if row['srcAddress'] in trusted_sources:
               if row['threat_score'] < 70:
                   if row['work_time_ratio'] > 0.6:
                       return True  # "误报 - 工作时间内来自可信来源的流量"
                   else:
                       return True  # "误报 - 来自可信来源(省公司/电科院)的流量"
               else:
                   if not (row['event_frequency'] > 5 or row['current_run_hours'] >= 3):
                       return True  # "可能误报 - 可信来源但分值较高，建议复核"
           # 否则（非白名单）
           else:
               if row['threat_score'] < 60:
                   if row['event_frequency'] == 1 and row['current_run_hours'] == 1:
                       return True  # "低危 - 单次外部访问，疑似偶发，建议观察"
       
       # D) 混合流量
       elif row['is_mixed_flow']:
           if row['threat_score'] < 55:
               if row['work_time_ratio'] > 0.6 and row['event_frequency'] == 1 and row['current_run_hours'] == 1:
                   return True  # "低危 - 工作时间内单次低风险事件"
               else:
                   return True  # "低危 - 正常混合流量，可能为业务请求"
       
       # 默认不是误报
       return False
   ```

3. **已知误报IP处理**：
   ```python
   # 创建已知误报IP列表
   known_false_positive_ips = set([
       # 内部服务器和监控系统
       '***********',  # 假设这是一个内部监控服务器
       '**************',  # 假设这是一个内部应用服务器
       '**************',  # 假设这是一个内部数据库服务器
       # 可信外部源
       '**********'  # 假设这是一个可信的外部服务
   ])
   
   # 将已知误报IP的记录标记为误报
   merged_df.loc[merged_df['srcAddress'].isin(known_false_positive_ips), 'false_positive_flag'] = True
   ```

4. **误报统计与输出**：
   ```python
   # 统计误报情况
   fp_counts = fp_df['false_positive_flag'].value_counts()
   print("\n误报统计:")
   print(f"误报: {fp_counts.get(True, 0)}条记录")
   print(f"非误报: {fp_counts.get(False, 0)}条记录")
   
   # 计算误报率
   false_positive_rate = fp_counts.get(True, 0) / len(fp_df) * 100 if len(fp_df) > 0 else 0
   print(f"误报率: {false_positive_rate:.2f}%")
   ```

**IP流向判断实现**：
系统通过以下步骤实现IP流向判断，为误报过滤提供基础：

1. **内外网IP判断**：
   ```python
   def is_private_ip(ip):
       """判断IP是否为内网IP"""
       try:
           return ipaddress.ip_address(ip).is_private
       except:
           return False
   ```

2. **流向分类**：
   ```python
   # 判断源IP和目的IP是否为内网
   df['src_is_internal'] = df['srcAddress'].apply(is_private_ip)
   df['dest_is_internal'] = df['destAddress'].apply(is_private_ip)
   
   # 确定流向
   conditions = [
       (df['src_is_internal'] & df['dest_is_internal']),  # 内对内
       (df['src_is_internal'] & ~df['dest_is_internal']),  # 内到外
       (~df['src_is_internal'] & df['dest_is_internal']),  # 外到内
       (~df['src_is_internal'] & ~df['dest_is_internal'])   # 外对外
   ]
   choices = ["内对内", "内到外", "外到内", "外对外"]
   
   df['flow_direction'] = pd.Series(np.select(conditions, choices, default="未知"), index=df.index)
   ```

**输出**：
- step6_fp_filtered.csv - 包含误报标记的数据
- step6_fp_ip_list2.csv - 已知误报IP列表

### 3.7 行为分析 (step7_behavior_analysis.py)

**功能**：分析IP的行为模式和活动特征。

**主要处理**：
- 计算IP活跃时间特征
- 分析24/72小时活动模式
- 识别混合流量模式
- 合并外部信息（威胁分数、误报标记）

**输出**：step7_behavior_analysis.csv

### 3.8 风险评分与结论生成 (step8_risk_scoring.py)

**功能**：计算调整后的威胁分数并生成结论。

**主要处理**：
- 动态风险评分计算
- 结合业务相关性(0.4)、工作时间(0.3)、频率(0.3)加权
- 生成风险结论（高危、中危、低危、需关注、可能误报）

**理论依据**：
风险评分与结论生成系统基于**多因素风险评估框架**和**安全事件分类理论**，结合了多种安全领域的研究成果：

1. Debar, H., Dacier, M., & Wespi, A. (2000). "A Revised Taxonomy for Intrusion-Detection Systems". Annales Des Télécommunications, 55(7-8), 361-378. 
   - 该研究第2章提出了入侵检测系统的分类法，为风险评分提供了理论框架
   - 第3.1节详细阐述了基于行为和基于知识的检测方法的结合，为多维度风险评估提供了理论基础
   - 第4.2节讨论了实时响应与离线分析的结合策略，为我们的风险等级划分和结论生成提供了方法论支持

2. Axelsson, S. (2000). "The Base-Rate Fallacy and the Difficulty of Intrusion Detection". ACM Transactions on Information and System Security, 3(3), 186-205. 
   - 该研究第2节分析了入侵检测中的基础率谬误问题，证明了误报识别在安全分析中的重要性
   - 第3.2节提出了基于贝叶斯理论的风险评估方法，为我们的多因素加权模型提供了理论支持
   - 第4.1节验证了将上下文信息纳入风险评估的有效性，特别是业务相关性和时间特征的重要性

3. Julisch, K. (2003). "Clustering Intrusion Detection Alarms to Support Root Cause Analysis". ACM Transactions on Information and System Security, 6(4), 443-471. 
   - 该研究第2章提出了警报聚类方法，为风险评分和结论生成提供了方法论基础
   - 第3.3节详细描述了基于根本原因的警报分类方法，为我们的结论生成逻辑提供了理论支持
   - 第4.2节验证了将警报聚类与风险评分结合的有效性，证明了这种方法可以显著减少安全分析师的工作负担

**计算模型**：
风险评分采用综合调整模型，包括调整威胁分数计算、风险等级划分和结论生成逻辑三个主要部分：

1. **调整威胁分数计算**：
   在step5生成的基础威胁分数上，进一步应用多个调整因子，优化威胁评分：
   ```python
   def calculate_adjusted_threat_score(df):
       # 初始化调整分为原始威胁分
       df['_adjusted_threat_score'] = df['threat_score']
       
       # 1. 时间因子
       df['time_factor'] = 0
       df.loc[df['work_time_ratio'] > 0.7, 'time_factor'] = -10  # 工作时间为主，倾向正常
       df.loc[df['work_time_ratio'] < 0.3, 'time_factor'] = 10   # 非工作时间为主，偏异常
       
       # 2. 频率因子
       df['frequency_factor'] = 0
       df.loc[df['event_frequency'] >= 10, 'frequency_factor'] = 15
       df.loc[(df['event_frequency'] >= 5) & (df['event_frequency'] < 10), 'frequency_factor'] = 10
       df.loc[df['event_frequency'] <= 1, 'frequency_factor'] = -5
       
       # 3. 持续性因子
       df['persistence_factor'] = 0
       df.loc[df['current_run_hours'] >= 3, 'persistence_factor'] += 10  # 连续≥3小时
       df.loc[df['active_hours_72h'] >= 12, 'persistence_factor'] += 10  # 72h内活跃≥12小时
       df.loc[df['has_next_24h'] == True, 'persistence_factor'] += 5    # 后24h仍有告警
       df.loc[(df['event_frequency'] <= 1) & (df['has_next_24h'] == False), 'persistence_factor'] -= 5  # 单次且没后续
       
       # 4. 成簇/混合因子
       df['cluster_factor'] = 0
       df.loc[df['hour_peer_ip_count'] >= 5, 'cluster_factor'] += 5  # 同小时群体异常
       df.loc[df['mixed_flow_modes'] >= 2, 'cluster_factor'] += 5    # 同小时同IP多种流向
       
       # 计算最终调整分
       df['_adjusted_threat_score'] = df['_adjusted_threat_score'] + \
                                    df['time_factor'] + \
                                    df['frequency_factor'] + \
                                    df['persistence_factor'] + \
                                    df['cluster_factor']
       
       # 限制在0-100范围内
       df['_adjusted_threat_score'] = df['_adjusted_threat_score'].clip(0, 100)
       
       return df
   ```

2. **风险等级划分**：
   基于调整后的威胁分数和其他关键特征，将安全事件划分为不同风险等级：
   
   - **高危**：调整分数≥75，且满足以下条件之一：
     - 高频次：事件频率≥5次
     - 持续时间长：连续运行小时数≥3小时
     - 目标IP多：同一小时内与≥5个不同IP交互
   
   - **中危**：调整分数≥60，但不满足高危条件
   
   - **低危**：调整分数<60，且不满足误报条件
   
   - **需关注**：满足特定条件的边缘案例，如：
     - 内网高风险活动但可能为误报
     - 非工作时间的高频业务请求
     - 来自可信来源的高风险流量
   
   - **可能误报**：满足特定误报条件，如：
     - 内网中等风险流量
     - 业务相关但分值较高的外联
     - 工作时间内的单次低风险事件

3. **结论生成逻辑**：
   系统根据流量方向、调整后的威胁分数和其他关键特征，生成详细的安全事件结论：
   
   ```python
   def generate_conclusion(df):
       # 初始化结论列
       df['conclusion'] = ""
       
       # 可信来源白名单
       trusted_sources = set(['**********', /* 其他可信来源 */])
       
       # 按流向细则处理
       for idx, row in df.iterrows():
           flow_directions = row['flow_directions']
           adjusted_score = row['_adjusted_threat_score']
           
           # A) 仅内对内流量
           if flow_directions == ['内对内']:
               if row['event_frequency'] <= 1 and row['work_time_ratio'] > 0.7 and row['current_run_hours'] < 3:
                   df.loc[idx, 'conclusion'] = "误报 - 工作时间内的单次内网事件"
               elif adjusted_score < 40:
                   df.loc[idx, 'conclusion'] = "误报 - 纯内网低风险流量"
               elif adjusted_score < 60:
                   df.loc[idx, 'conclusion'] = "可能误报 - 内网中等风险流量，建议复核"
               else:  # >= 60
                   if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3:
                       df.loc[idx, 'conclusion'] = "需关注 - 内网高频/持续高风险活动，建议人工审核"
                   else:
                       df.loc[idx, 'conclusion'] = "需关注 - 内网高风险活动，虽可能误报但需人工审核"
           
           # B) 内到外流量
           elif '内到外' in flow_directions and len(flow_directions) == 1:
               if row['business_related_ratio'] > 0.7 and row['work_time_ratio'] > 0.6:
                   if adjusted_score < 65 and row['current_run_hours'] < 3:
                       df.loc[idx, 'conclusion'] = "误报 - 工作时间内的业务相关正常外联请求"
                   else:
                       df.loc[idx, 'conclusion'] = "可能误报 - 业务相关但分值/持续性较高，建议复核"
               elif row['business_related_ratio'] > 0.7 and row['work_time_ratio'] <= 0.6:
                   if row['event_frequency'] > 5 or row['current_run_hours'] >= 3:
                       df.loc[idx, 'conclusion'] = "需关注 - 非工作时间的高频/持续业务请求，建议复核"
                   else:
                       df.loc[idx, 'conclusion'] = "可能误报 - 非工作时间的业务相关请求，建议抽查"
               else:  # 其他情况（看分）
                   if adjusted_score >= 75:
                       if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3 or row['hour_peer_ip_count'] >= 5:
                           df.loc[idx, 'conclusion'] = "高危 - 持续性/成簇的数据外联风险，需立即处理"
                       else:
                           df.loc[idx, 'conclusion'] = "高危 - 可能的数据外泄，需检查请求头与目的地"
                   elif adjusted_score >= 50:
                       df.loc[idx, 'conclusion'] = "中危 - 外联行为需审核，检查 host/referer/origin/UA"
                   else:
                       df.loc[idx, 'conclusion'] = "低危 - 可能为监控/巡检等正常外联"
           
           # C) 外到内流量
           elif '外到内' in flow_directions and len(flow_directions) == 1:
               # 处理外到内流量的逻辑...
           
           # D) 混合流量
           elif len(flow_directions) >= 2:
               # 处理混合流量的逻辑...
           
           # 其他情况
           else:
               # 处理其他情况的逻辑...
       
       return df
   ```

**输出**：step8_test_result.csv

### 3.9 综合研判 (step9_alert_analysis2.py)

**功能**：最终分析攻击结果和置信度。

**主要处理**：
- 攻击结果分析（失陷、攻击成功、企图、失败）
- 置信度计算（高：>80分，中：60-80，低：<60）
- 生成最终结论和解决方案建议

**输出**：step9_alert_analysis_result2.csv

## 4. 多维度分析框架

系统基于以下五个维度进行综合分析：

1. **IP流向**：区分内对内、内到外、外到内和外对外四种流量模式
2. **时间**：判断事件是否发生在工作时间（工作日8:30-17:30）
3. **告警级别**：将告警分为高、中、低三个级别
4. **攻击结果**：将攻击结果分为失陷、攻击成功、企图和失败四个层级
5. **置信度**：基于多因素计算的高、中、低三级置信度

## 5. 误报识别能力

系统能够识别多种类型的误报场景：

- 低级别内网失败攻击且置信度低的事件
- 工作时间内的单次内网事件
- 纯内网低风险流量
- 工作时间内的低风险外联活动
- 来自可信来源（如省公司、电科院）的流量
- 低级别失败的外部访问尝试

## 6. 风险分级

系统将安全事件分为以下几个风险等级：

- **高危**：需要立即处理的严重安全事件
- **中危**：需要审核的可疑安全事件
- **低危**：可能为正常业务活动
- **需关注**：虽可能误报但需人工审核的事件
- **可能误报**：建议复核的低风险事件

## 7. 可自定义设置项

系统提供以下可自定义设置项，以适应不同的业务环境和安全需求：

###  内网IP判断规则（step2_flow_direction.py）

```python
def is_private_ip(ip):
    """
    判断IP是否为内网IP
    使用ipaddress库判断是否为私有IP地址
    异常情况返回False（按外网处理）
    """
    try:
        return ipaddress.ip_address(ip).is_private
    except:
        return False
```

### 7.2 HTTP协议业务相关性识别（step3_business_analysis.py）

```python
# 预置业务相关池 - 用于识别业务相关的HTTP请求
business_related_patterns = [
    "Prometheus", "电力监控", "monitor", "power", "energy", "dashboard", 
    "panel", "control", "system", "internal", "业务", "监控", "电力", "能源"
]

# 预置常见互联网池 - 用于识别非业务相关的HTTP请求
common_internet_patterns = [
    "Mozilla", "Chrome", "Firefox", "Safari", "Edge", "Google", "Baidu", 
    "Bing", "Yahoo", "public", "cdn", "api", "www", "http", "https"
]
```

### 7.3 工作时间定义（step4_work_time.py）

```python
# 工作日范围：目前设置为周一至周五
is_weekday = time.dayofweek < 5  # 0-4 表示周一至周五

# 工作时间段：目前设置为08:30-17:30
is_work_hours = (hour > 6 or (hour == 6 and minute >= 30)) and 
               (hour < 20 or (hour == 20 and minute <= 30))
```

### 可信来源白名单（step6_false_positive.py）

```python
trusted_sources = set([
    '**********',  # 假设这是可信的外部服务
    # 可以添加更多可信来源
])
```

###  误报判断规则（step6_false_positive.py）

```python
# 内网单次事件规则
if row['is_internal_only']:
    if row['event_frequency'] == 1 and row['work_time_ratio'] > 0.6 and row['current_run_hours'] == 1:
        return True  # "误报 - 工作时间内的单次内网事件"
    elif row['threat_score'] < 40:
        return True  # "误报 - 纯内网低风险流量"

# 业务相关性阈值
if row['business_related_ratio'] > 0.7 and row['work_time_ratio'] > 0.6:
    if row['threat_score'] < 65 and row['current_run_hours'] < 3:
        return True  # "误报 - 工作时间内的业务相关正常外联请求"
```

### 风险等级划分阈值（step8_risk_scoring.py）

```python
# 高危阈值
if adjusted_score >= 75:
    if row['event_frequency'] >= 5 or row['current_run_hours'] >= 3 or row['hour_peer_ip_count'] >= 5:
        df.loc[idx, 'conclusion'] = "高危 - 持续性/成簇的数据外联风险，需立即处理"

# 中危阈值
elif adjusted_score >= 60:
    df.loc[idx, 'conclusion'] = "中危 - 可疑的数据外联，需审核目的地和内容"

# 低危阈值
else:  # < 60
    df.loc[idx, 'conclusion'] = "低危 - 可能为正常业务活动，建议抽查"
```



## 8. 总结

该安全分析系统通过多维度分析和多层次过滤，实现了对安全事件的精准分类和误报识别。系统从原始日志解析开始，经过IP流向分析、业务相关性分析、工作时间标记、威胁评分、误报过滤、行为分析、风险定级，最终完成综合研判，输出攻击结果和置信度评估，为安全运维人员提供了高效准确的决策支持。

通过调整上述自定义设置项，可以根据实际业务环境和安全需求优化系统性能，提高检测准确率和减少误报率。