import pandas as pd
import numpy as np
from datetime import datetime
import ipaddress
import math
import json
import os

def is_internal_ip(ip):
    """
    判断IP是否为内网IP
    """
    try:
        ip_obj = ipaddress.ip_address(ip)
        return ip_obj.is_private
    except ValueError:
        return False

def asset_importance(ip):
    """
    根据IP地址判断资产重要性
    返回权重：3-核心业务网段，2-重要业务网段，1-一般网段
    """
    try:
        ip_obj = ipaddress.ip_address(ip)
        
        # 动态加载核心业务网段（权重3）
        core_network_strings = load_core_business_networks()
        core_networks = []
        for network_str in core_network_strings:
            try:
                core_networks.append(ipaddress.ip_network(network_str, strict=False))
            except ValueError:
                continue
        
        # 动态加载重要业务网段（权重2）
        important_network_strings = load_important_business_networks()
        important_networks = []
        for network_str in important_network_strings:
            try:
                important_networks.append(ipaddress.ip_network(network_str, strict=False))
            except ValueError:
                continue
        
        for network in core_networks:
            if ip_obj in network:
                return 3
        
        for network in important_networks:
            if ip_obj in network:
                return 2
        
        return 1  # 一般网段
    except ValueError:
        return 1

def map_attack_vector(row):
    """
    映射攻击向量
    """
    # 基于扫描方法和漏洞描述判断
    scan_method = str(row.get('scan_method', '')).lower()
    exp_desc = str(row.get('exp_desc', '')).lower()
    
    if 'remote' in exp_desc or 'network' in scan_method or '远程' in exp_desc:
        return 'network'
    elif 'local' in exp_desc or '本地' in exp_desc:
        return 'local'
    elif 'adjacent' in exp_desc or '邻接' in exp_desc:
        return 'adjacent'
    else:
        return 'network'  # 默认网络攻击

def map_attack_complexity(row):
    """
    映射攻击复杂度
    """
    exp_desc = str(row.get('exp_desc', '')).lower()
    vuln_level = str(row.get('vuln_level', '')).lower()
    
    # 高危漏洞通常攻击复杂度较低
    if vuln_level == 'high':
        return 'low'
    elif 'complex' in exp_desc or '复杂' in exp_desc:
        return 'high'
    else:
        return 'low'  # 默认低复杂度

def map_privileges_required(row, attack_vector):
    """
    映射所需权限
    """
    exp_desc = str(row.get('exp_desc', '')).lower()
    vuln_level = str(row.get('vuln_level', '')).lower()
    
    if 'admin' in exp_desc or '管理员' in exp_desc or 'root' in exp_desc:
        return 'high'
    elif 'user' in exp_desc or '用户' in exp_desc or vuln_level == 'medium':
        return 'low'
    else:
        return 'none'  # 默认无需权限

def map_user_interaction(row):
    """
    映射用户交互要求
    """
    exp_desc = str(row.get('exp_desc', '')).lower()
    scan_method = str(row.get('scan_method', '')).lower()
    
    if 'click' in exp_desc or 'user' in exp_desc or 'interact' in exp_desc or '用户' in exp_desc:
        return 'required'
    elif 'automatic' in scan_method or '自动' in scan_method:
        return 'none'
    else:
        return 'none'  # 默认无需交互

def map_scope(row, attack_vector):
    """
    映射影响范围
    """
    exp_desc = str(row.get('exp_desc', '')).lower()
    vuln_level = str(row.get('vuln_level', '')).lower()
    
    # 高危漏洞或包含提权、跨域等关键词时影响范围可能改变
    if (vuln_level == 'high' or 
        'privilege' in exp_desc or '提权' in exp_desc or 
        'cross' in exp_desc or '跨域' in exp_desc or
        'escape' in exp_desc or '逃逸' in exp_desc):
        return 'changed'
    else:
        return 'unchanged'

def map_impact_metrics(row):
    """
    映射CIA影响指标
    """
    exp_desc = str(row.get('exp_desc', '')).lower()
    vuln_level = str(row.get('vuln_level', '')).lower()
    
    # 根据漏洞级别和描述判断影响
    if vuln_level == 'high':
        return {'confidentiality': 'high', 'integrity': 'high', 'availability': 'high'}
    elif vuln_level == 'medium':
        return {'confidentiality': 'low', 'integrity': 'low', 'availability': 'low'}
    else:
        return {'confidentiality': 'none', 'integrity': 'none', 'availability': 'low'}

def map_vuln_to_cvss_metrics(row):
    """
    将绿盟漏洞数据映射到CVSS v3.1指标
    """
    attack_vector = map_attack_vector(row)
    
    return {
        'attack_vector': attack_vector,
        'attack_complexity': map_attack_complexity(row),
        'privileges_required': map_privileges_required(row, attack_vector),
        'user_interaction': map_user_interaction(row),
        'scope': map_scope(row, attack_vector),
        **map_impact_metrics(row)
    }

def calculate_base_score(cvss_metrics):
    """
    计算CVSS v3.1基础评分
    """
    # CVSS v3.1指标值映射
    av_values = {'network': 0.85, 'adjacent': 0.62, 'local': 0.55, 'physical': 0.2}
    ac_values = {'low': 0.77, 'high': 0.44}
    pr_values = {
        'none': {'unchanged': 0.85, 'changed': 0.85},
        'low': {'unchanged': 0.62, 'changed': 0.68},
        'high': {'unchanged': 0.27, 'changed': 0.50}
    }
    ui_values = {'none': 0.85, 'required': 0.62}
    impact_values = {'none': 0.0, 'low': 0.22, 'high': 0.56}
    
    # 获取指标值
    av = av_values.get(cvss_metrics['attack_vector'], 0.85)
    ac = ac_values.get(cvss_metrics['attack_complexity'], 0.77)
    scope = cvss_metrics['scope']
    pr = pr_values.get(cvss_metrics['privileges_required'], {'unchanged': 0.85, 'changed': 0.85})[scope]
    ui = ui_values.get(cvss_metrics['user_interaction'], 0.85)
    c = impact_values.get(cvss_metrics['confidentiality'], 0.0)
    i = impact_values.get(cvss_metrics['integrity'], 0.0)
    a = impact_values.get(cvss_metrics['availability'], 0.0)
    
    # 计算可利用性评分
    exploitability = 8.22 * av * ac * pr * ui
    
    # 计算影响评分
    impact = 1 - ((1 - c) * (1 - i) * (1 - a))
    
    # 计算基础评分
    if impact <= 0:
        base_score = 0.0
    elif scope == 'unchanged':
        base_score = min(impact + exploitability, 10.0)
    else:  # scope == 'changed'
        base_score = min(1.08 * (impact + exploitability), 10.0)
    
    # 向上取整到一位小数
    base_score = math.ceil(base_score * 10) / 10
    
    return base_score, exploitability, impact

def calculate_temporal_score(base_score, cvss_metrics):
    """
    计算CVSS v3.1时间评分
    """
    # 时间评分指标值（默认值）
    e_values = {'not_defined': 1.0, 'unproven': 0.91, 'proof_of_concept': 0.94, 'functional': 0.97, 'high': 1.0}
    rl_values = {'not_defined': 1.0, 'official_fix': 0.87, 'temporary_fix': 0.90, 'workaround': 0.95, 'unavailable': 1.0}
    rc_values = {'not_defined': 1.0, 'unknown': 0.92, 'reasonable': 0.96, 'confirmed': 1.0}
    
    # 获取时间评分指标（如果没有提供则使用默认值）
    e = e_values.get(cvss_metrics.get('exploit_code_maturity', 'not_defined'), 1.0)
    rl = rl_values.get(cvss_metrics.get('remediation_level', 'not_defined'), 1.0)
    rc = rc_values.get(cvss_metrics.get('report_confidence', 'not_defined'), 1.0)
    
    # 计算时间评分
    temporal_score = base_score * e * rl * rc
    
    # 向上取整到一位小数
    temporal_score = math.ceil(temporal_score * 10) / 10
    
    return temporal_score

def calculate_environmental_score(base_score, cvss_metrics, env_factors=None):
    """
    计算CVSS v3.1环境评分
    """
    if env_factors is None:
        env_factors = {}
    
    # 环境评分指标值（默认值与基础评分相同）
    cr = env_factors.get('confidentiality_requirement', 1.0)
    ir = env_factors.get('integrity_requirement', 1.0)
    ar = env_factors.get('availability_requirement', 1.0)
    
    # 修改后的影响指标（默认与基础评分相同）
    modified_av = env_factors.get('modified_attack_vector', cvss_metrics['attack_vector'])
    modified_ac = env_factors.get('modified_attack_complexity', cvss_metrics['attack_complexity'])
    modified_pr = env_factors.get('modified_privileges_required', cvss_metrics['privileges_required'])
    modified_ui = env_factors.get('modified_user_interaction', cvss_metrics['user_interaction'])
    modified_scope = env_factors.get('modified_scope', cvss_metrics['scope'])
    modified_c = env_factors.get('modified_confidentiality', cvss_metrics['confidentiality'])
    modified_i = env_factors.get('modified_integrity', cvss_metrics['integrity'])
    modified_a = env_factors.get('modified_availability', cvss_metrics['availability'])
    
    # 使用修改后的指标重新计算（简化版本，直接使用基础评分）
    environmental_score = base_score * min(cr, ir, ar)
    
    # 向上取整到一位小数
    environmental_score = math.ceil(environmental_score * 10) / 10
    
    return environmental_score

def score_vuln_basic(df):
    """
    基础漏洞评分：基于漏洞级别和严重程度
    根据sourcedata2.txt的分布调整权重：high:11092, medium:20477, low:4023
    """
    # 调整权重以匹配sourcedata2的分布
    level_weights = {'high': 10, 'medium': 6, 'middle': 6, 'low': 2}
    df['level_score'] = df['vuln_level'].map(level_weights).fillna(1)
    
    # 初始化severity_score字段（如果不存在）
    if 'severity_score' not in df.columns:
        df['severity_score'] = df['severity'] if 'severity' in df.columns else 5
    df['severity_score'] = df['severity_score'].fillna(5)
    
    # 根据sourcedata2的实际分布调整评分逻辑
    # high级别漏洞占31.1%，medium级别占57.5%，low级别占11.3%
    df['basic_score'] = df['level_score'] * 0.6 + df['severity_score'] * 0.4
    return df

def score_vuln_density(df):
    """
    漏洞密度评分：基于IP地址的漏洞数量
    """
    ip_vuln_count = df.groupby('target_ip').size().to_dict()
    df['ip_vuln_count'] = df['target_ip'].map(ip_vuln_count)
    
    # 根据漏洞数量计算密度评分
    df['density_score'] = np.minimum(df['ip_vuln_count'] * 0.5, 10)
    return df

def score_dangerous_vuln(df):
    """
    危险漏洞评分：基于漏洞描述关键词
    """
    dangerous_keywords = ['远程代码执行', '命令执行', 'RCE', '提权', '缓冲区溢出', 'SQL注入']
    
    def check_dangerous(desc):
        if pd.isna(desc):
            return 0
        desc_str = str(desc).lower()
        for keyword in dangerous_keywords:
            if keyword.lower() in desc_str:
                return 10
        return 0
    
    # 检查是否存在exp_desc字段，如果不存在则使用vuln_name或创建默认值
    if 'exp_desc' not in df.columns:
        if 'vuln_name' in df.columns:
            df['exp_desc'] = df['vuln_name']
        else:
            df['exp_desc'] = ''
    
    df['dangerous_score'] = df['exp_desc'].apply(check_dangerous)
    return df

def score_cve_relevance(df):
    """
    CVE相关性评分：基于CVE编号的存在和年份
    """
    def calculate_cve_score(cve_id):
        if pd.isna(cve_id) or cve_id == '':
            return 5  # 无CVE编号，中等评分
        
        cve_str = str(cve_id).upper()
        if 'CVE-' in cve_str:
            return 10  # 有CVE编号，高评分
        return 5
    
    df['cve_score'] = df['cve_id'].apply(calculate_cve_score)
    return df

def generate_risk_reason(row):
    """
    生成风险原因描述
    """
    reasons = []
    
    # 基于漏洞级别
    if row['vuln_level'] == 'high':
        reasons.append(f"高危漏洞(级别:{row['vuln_level']})")
    elif row['vuln_level'] in ['medium', 'middle']:
        reasons.append(f"中危漏洞(级别:{row['vuln_level']})")
    elif row['vuln_level'] == 'low':
        reasons.append(f"低危漏洞(级别:{row['vuln_level']})")
    
    # 基于严重程度评分
    if 'severity_score' in row.index and pd.notna(row['severity_score']) and row['severity_score'] >= 8:
        reasons.append(f"严重程度评分高({row['severity_score']}分)")
    
    # 基于漏洞数量（仅在IP汇总时存在该字段）
    if 'vuln_count' in row.index and pd.notna(row['vuln_count']):
        if row['vuln_count'] >= 10:
            reasons.append(f"大量漏洞({row['vuln_count']}个)")
        elif row['vuln_count'] >= 5:
            reasons.append(f"较多漏洞({row['vuln_count']}个)")
    
    # 基于危险关键词
    if pd.notna(row['exp_desc']):
        dangerous_keywords = ['远程代码执行', '命令执行', 'RCE', '提权', '缓冲区溢出', 'SQL注入']
        exp_desc_lower = str(row['exp_desc']).lower()
        for keyword in dangerous_keywords:
            if keyword.lower() in exp_desc_lower:
                reasons.append(f"包含危险操作关键词: {keyword}")
                break
    
    # 基于资产重要性
    if row['asset_weight'] == 3:
        reasons.append("核心业务网段资产")
    elif row['asset_weight'] == 2:
        reasons.append("重要业务网段资产")
    
    # 基于漏洞描述的具体原因
    if pd.notna(row['exp_desc']):
        exp_desc = str(row['exp_desc'])
        if len(exp_desc) > 50:
            reasons.append(f"漏洞详情: {exp_desc[:100]}...")
        else:
            reasons.append(f"漏洞详情: {exp_desc}")
    
    return '; '.join(reasons) if reasons else '一般风险'

def generate_false_positive_reason(row):
    """
    生成误报原因分析
    """
    reasons = []
    
    # 基于扫描方法
    if pd.notna(row.get('scan_method')):
        scan_method = str(row['scan_method']).lower()
        if 'principle' in scan_method or '原理' in scan_method:
            reasons.append("基于原理扫描，可能存在误报")
        elif 'banner' in scan_method or '横幅' in scan_method:
            reasons.append("基于横幅识别，可能存在版本误判")
    
    # 基于漏洞确认状态
    if pd.notna(row.get('vul_confirmed')):
        if not row['vul_confirmed']:
            reasons.append("漏洞未经确认验证")
    
    # 基于时间因素
    if pd.notna(row.get('date_found')):
        try:
            found_date = pd.to_datetime(row['date_found'])
            days_old = (datetime.now() - found_date).days
            if days_old > 365:
                reasons.append(f"漏洞发现时间较久远({row['date_found']})，可能已修复")
        except:
            pass
    
    # 基于漏洞级别和评分不匹配
    if row['vuln_level'] == 'low' and row.get('final_score', 0) > 60:
        reasons.append("低危漏洞评分异常偏高，需人工复核")
    
    return '; '.join(reasons) if reasons else '风险评估正常'

def calculate_comprehensive_score(df):
    """
    计算综合风险评分
    使用混合评分模型：60% CVSS标准评分 + 30% 传统评分 + 10% 环境调整
    """
    # 应用各项评分函数
    df = score_vuln_basic(df)
    df = score_vuln_density(df)
    df = score_dangerous_vuln(df)
    df = score_cve_relevance(df)
    
    # 添加资产重要性权重
    df['asset_weight'] = df['target_ip'].apply(asset_importance)
    
    # 计算CVSS评分
    cvss_scores = []
    for _, row in df.iterrows():
        try:
            # 获取CVSS指标
            cvss_metrics = map_vuln_to_cvss_metrics(row)
            
            # 计算基础评分
            base_score, exploitability, impact = calculate_base_score(cvss_metrics)
            
            # 计算时间评分（使用默认值）
            temporal_score = calculate_temporal_score(base_score, cvss_metrics)
            
            # 计算环境评分（考虑资产重要性）
            env_factors = {
                'confidentiality_requirement': min(row['asset_weight'] / 2.0, 1.5),
                'integrity_requirement': min(row['asset_weight'] / 2.0, 1.5),
                'availability_requirement': min(row['asset_weight'] / 2.0, 1.5)
            }
            environmental_score = calculate_environmental_score(temporal_score, cvss_metrics, env_factors)
            
            cvss_scores.append({
                'cvss_base_score': base_score,
                'cvss_temporal_score': temporal_score,
                'cvss_environmental_score': environmental_score
            })
        except Exception as e:
            # 如果CVSS计算失败，使用默认值
            cvss_scores.append({
                'cvss_base_score': 5.0,
                'cvss_temporal_score': 5.0,
                'cvss_environmental_score': 5.0
            })
    
    # 将CVSS评分添加到DataFrame
    cvss_df = pd.DataFrame(cvss_scores)
    df = pd.concat([df, cvss_df], axis=1)
    
    # 计算传统评分（原有的四维度评分）
    traditional_score = (
        df['basic_score'] * 0.4 +      # 基础评分权重
        df['density_score'] * 0.2 +    # 密度评分权重
        df['dangerous_score'] * 0.2 +  # 危险评分权重
        df['cve_score'] * 0.2          # CVE评分权重
    )
    
    # 混合评分模型：60% CVSS + 30% 传统 + 10% 环境调整
    cvss_component = df['cvss_environmental_score'] * 0.6
    traditional_component = traditional_score * 0.3
    environment_component = df['asset_weight'] * 0.1
    
    df['comprehensive_score'] = (cvss_component + traditional_component + environment_component) * df['asset_weight']
    
    # 调整标准化逻辑以匹配sourcedata2的评分范围
    # 根据观察到的数据，final_score范围应该在7-100之间
    max_possible_score = 10 * 3  # 最高分 * 最高权重
    df['final_score'] = np.minimum(df['comprehensive_score'] / max_possible_score * 100, 100)
    
    # 调整评分范围：high为70-95分，medium为50-70分，low为8-45分
    def adjust_score_by_level(row):
        base_score = row['final_score']
        normalized_score = base_score / 100.0  # 将0-100分标准化为0-1
        
        if row['vuln_level'] == 'high':
            # 高危漏洞70-95分范围
            return 70 + normalized_score * 25
        elif row['vuln_level'] in ['medium', 'middle']:
            # 中危漏洞50-70分范围
            return 50 + normalized_score * 20
        elif row['vuln_level'] == 'low':
            # 低危漏洞8-45分范围
            return 8 + normalized_score * 37
        return base_score
    
    df['final_score'] = df.apply(adjust_score_by_level, axis=1)
    
    # 生成风险原因和误报原因
    df['risk_reason'] = df.apply(generate_risk_reason, axis=1)
    df['false_positive_reason'] = df.apply(generate_false_positive_reason, axis=1)
    
    return df

def generate_ip_risk_summary(df):
    """
    生成IP风险汇总
    """
    ip_summary = df.groupby('target_ip').agg({
        'final_score': 'max',
        'vuln_level': lambda x: list(x),
        'asset_weight': 'first'
    }).reset_index()
    
    # 计算每个IP的漏洞数量
    vuln_counts = df.groupby('target_ip').size().reset_index(name='vuln_count')
    ip_summary = ip_summary.merge(vuln_counts, on='target_ip')
    
    # 风险等级分类
    def risk_level(score):
        if score >= 80:
            return '极高风险'
        elif score >= 60:
            return '高风险'
        elif score >= 40:
            return '中风险'
        else:
            return '低风险'
    
    ip_summary['risk_level'] = ip_summary['final_score'].apply(risk_level)
    ip_summary = ip_summary.sort_values('final_score', ascending=False)
    
    return ip_summary

def step2():
    # 示例使用
    from step1_parse_vuln_data import get_security_logs_from_db, parse_vuln_data

    # 从数据库获取原始数据
    print("正在从数据库获取漏洞数据...")
    raw_data = get_security_logs_from_db()

    # 解析漏洞数据
    print("正在解析漏洞数据...")
    df = parse_vuln_data(raw_data)

    # 计算风险评分
    df_scored = calculate_comprehensive_score(df)

    # 生成IP风险汇总
    ip_risk_summary = generate_ip_risk_summary(df_scored)

    # 保存结果
    # df_scored.to_csv('step2_vuln_detailed_scores.csv', index=False, encoding='utf-8-sig')
    # ip_risk_summary.to_csv('step2_vuln_ip_risk_summary.csv', index=False, encoding='utf-8-sig')
    print(f"共处理 {len(df_scored)} 条漏洞记录")
    print(f"涉及 {len(ip_risk_summary)} 个IP地址")
    print("\n前10个高风险IP:")
    # print(ip_risk_summary.head(10))
    return df_scored, ip_risk_summary
    # 验证评分范围
    # print("\n=== 评分范围验证 ===")
    # for level in ['high', 'medium', 'low']:
    #     level_data = df_scored[df_scored['vuln_level'] == level]
    #     if len(level_data) == 0:
    #         level_data = df_scored[df_scored['vuln_level'] == 'middle']
    #         if len(level_data) > 0 and level == 'medium':
    #             print(f"{level}级别 (标记为middle): {len(level_data)} 条记录")
    #         else:
    #             continue
    #     else:
    #         print(f"{level}级别: {len(level_data)} 条记录")
    #
    #     scores = level_data['final_score']
    #     print(f"  评分范围: {scores.min():.1f} - {scores.max():.1f}")
    #     print(f"  平均评分: {scores.mean():.1f}")
# 网段配置文件路径
CORE_NETWORKS_FILE = os.path.join(os.path.dirname(__file__), 'core_business_networks.json')
IMPORTANT_NETWORKS_FILE = os.path.join(os.path.dirname(__file__), 'important_business_networks.json')

def load_core_business_networks():
    """
    加载核心业务网段列表
    """
    try:
        if os.path.exists(CORE_NETWORKS_FILE):
            with open(CORE_NETWORKS_FILE, 'r', encoding='utf-8') as f:
                networks = json.load(f)
                return networks if isinstance(networks, list) else []
        else:
            # 默认核心业务网段
            default_networks = [
                '************/24',  # 核心服务器网段
                '************/24',  # 数据库网段
            ]
            save_core_business_networks(default_networks)
            return default_networks
    except Exception as e:
        print(f"加载核心业务网段失败: {e}")
        return [
            '************/24',  # 核心服务器网段
            '************/24',  # 数据库网段
        ]

def save_core_business_networks(networks):
    """
    保存核心业务网段列表
    """
    try:
        with open(CORE_NETWORKS_FILE, 'w', encoding='utf-8') as f:
            json.dump(networks, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存核心业务网段失败: {e}")
        return False

def update_core_business_networks(ip_addresses):
    """
    更新核心业务网段列表
    参数: ip_addresses - IP地址列表
    返回: 是否成功
    """
    try:
        # 验证IP地址格式并转换为网段格式
        valid_networks = []
        for ip in ip_addresses:
            try:
                # 验证IP地址格式
                ipaddress.ip_address(ip)
                # 将单个IP转换为/32网段或保持原有格式
                if '/' not in ip:
                    valid_networks.append(f"{ip}/32")
                else:
                    # 验证网段格式
                    ipaddress.ip_network(ip, strict=False)
                    valid_networks.append(ip)
            except ValueError:
                print(f"无效的IP地址或网段: {ip}")
                continue
        
        if valid_networks:
            # 获取现有网段
            existing_networks = load_core_business_networks()
            # 合并网段（去重）
            all_networks = list(set(existing_networks + valid_networks))
            # 保存更新后的网段
            return save_core_business_networks(all_networks)
        return False
    except Exception as e:
        print(f"更新核心业务网段失败: {e}")
        return False

def load_important_business_networks():
    """
    加载重要业务网段列表
    """
    try:
        if os.path.exists(IMPORTANT_NETWORKS_FILE):
            with open(IMPORTANT_NETWORKS_FILE, 'r', encoding='utf-8') as f:
                networks = json.load(f)
                return networks if isinstance(networks, list) else []
        else:
            # 默认重要业务网段
            default_networks = [
                '**********/16',    # 内网主要网段
                '**********/16',    # 办公网段
            ]
            save_important_business_networks(default_networks)
            return default_networks
    except Exception as e:
        print(f"加载重要业务网段失败: {e}")
        return [
            '**********/16',    # 内网主要网段
            '**********/16',    # 办公网段
        ]

def save_important_business_networks(networks):
    """
    保存重要业务网段列表
    """
    try:
        with open(IMPORTANT_NETWORKS_FILE, 'w', encoding='utf-8') as f:
            json.dump(networks, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存重要业务网段失败: {e}")
        return False

def update_important_business_networks(ip_addresses):
    """
    更新重要业务网段列表
    参数: ip_addresses - IP地址列表
    返回: 是否成功
    """
    try:
        # 验证IP地址格式并转换为网段格式
        valid_networks = []
        for ip in ip_addresses:
            try:
                # 验证IP地址格式
                ipaddress.ip_address(ip)
                # 将单个IP转换为/32网段或保持原有格式
                if '/' not in ip:
                    valid_networks.append(f"{ip}/32")
                else:
                    # 验证网段格式
                    ipaddress.ip_network(ip, strict=False)
                    valid_networks.append(ip)
            except ValueError:
                print(f"无效的IP地址或网段: {ip}")
                continue
        
        if valid_networks:
            # 获取现有网段
            existing_networks = load_important_business_networks()
            # 合并网段（去重）
            all_networks = list(set(existing_networks + valid_networks))
            # 保存更新后的网段
            return save_important_business_networks(all_networks)
        return False
    except Exception as e:
        print(f"更新重要业务网段失败: {e}")
        return False

def get_core_business_networks():
    """
    获取核心业务网段列表
    
    Returns:
        list: 核心业务网段列表
    """
    try:
        return load_core_business_networks()
    except Exception as e:
        print(f"获取核心业务网段列表失败: {str(e)}")
        return []

def get_important_business_networks():
    """
    获取重要业务网段列表
    
    Returns:
        list: 重要业务网段列表
    """
    try:
        return load_important_business_networks()
    except Exception as e:
        print(f"获取重要业务网段列表失败: {str(e)}")
        return []

def delete_core_business_network(ip_address):
    """
    根据IP地址删除核心业务网段列表中的对应信息
    
    Args:
        ip_address (str): 要删除的IP地址
    
    Returns:
        bool: 删除是否成功
    """
    try:
        # 验证IP地址格式
        try:
            ipaddress.ip_address(ip_address)
        except ValueError:
            print(f"无效的IP地址: {ip_address}")
            return False
        
        # 获取现有网段列表
        existing_networks = load_core_business_networks()
        
        # 查找并删除匹配的网段
        networks_to_remove = []
        for network in existing_networks:
            try:
                # 检查IP是否在该网段中
                if '/' in network:
                    net = ipaddress.ip_network(network, strict=False)
                    if ipaddress.ip_address(ip_address) in net:
                        networks_to_remove.append(network)
                else:
                    # 单个IP地址的情况
                    if network == ip_address or network == f"{ip_address}/32":
                        networks_to_remove.append(network)
            except ValueError:
                continue
        
        if networks_to_remove:
            # 删除匹配的网段
            for network in networks_to_remove:
                existing_networks.remove(network)
            
            # 保存更新后的网段列表
            return save_core_business_networks(existing_networks)
        else:
            print(f"未找到包含IP地址 {ip_address} 的核心业务网段")
            return False
            
    except Exception as e:
        print(f"删除核心业务网段失败: {e}")
        return False

def delete_important_business_network(ip_address):
    """
    根据IP地址删除重要业务网段列表中的对应信息
    
    Args:
        ip_address (str): 要删除的IP地址
    
    Returns:
        bool: 删除是否成功
    """
    try:
        # 验证IP地址格式
        try:
            ipaddress.ip_address(ip_address)
        except ValueError:
            print(f"无效的IP地址: {ip_address}")
            return False
        
        # 获取现有网段列表
        existing_networks = load_important_business_networks()
        
        # 查找并删除匹配的网段
        networks_to_remove = []
        for network in existing_networks:
            try:
                # 检查IP是否在该网段中
                if '/' in network:
                    net = ipaddress.ip_network(network, strict=False)
                    if ipaddress.ip_address(ip_address) in net:
                        networks_to_remove.append(network)
                else:
                    # 单个IP地址的情况
                    if network == ip_address or network == f"{ip_address}/32":
                        networks_to_remove.append(network)
            except ValueError:
                continue
        
        if networks_to_remove:
            # 删除匹配的网段
            for network in networks_to_remove:
                existing_networks.remove(network)
            
            # 保存更新后的网段列表
            return save_important_business_networks(existing_networks)
        else:
            print(f"未找到包含IP地址 {ip_address} 的重要业务网段")
            return False
            
    except Exception as e:
        print(f"删除重要业务网段失败: {e}")
        return False

if __name__ == "__main__":
    df_scored, ip_risk_summary=step2()
    print(df_scored.head(10))
    print(ip_risk_summary.head(10))
