#!/usr/bin/env python
# encoding:utf-8
"""
Step5 威胁评分分析集成测试
测试 step5_temp.py 与 step4_work_time.py 之间的集成
"""

import pandas as pd
from step5_temp import get_work_time_data, generate_threat_scores

def test_step5_integration():
    """
    测试 step5 与 step4 的集成
    """
    print("=== Step5 威胁评分分析集成测试 ===")
    
    # 1. 获取工作时间分析数据
    print("\n1. 获取工作时间分析数据...")
    df = get_work_time_data()
    
    print(f"获取到数据: {len(df)} 条记录")
    print(f"数据列: {list(df.columns)}")
    
    # 2. 检查数据结构
    print("\n2. 检查数据结构...")
    required_columns = ['hour_slot', 'srcAddress', 'severity', 'is_work_time', 'is_business_related', 'flow_direction']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必需列: {missing_columns}")
        return False
    else:
        print("✅ 数据结构检查通过")
    
    # 3. 检查时间格式
    print("\n3. 检查时间格式...")
    if 'time' in df.columns:
        if pd.api.types.is_datetime64_any_dtype(df['time']):
            print("✅ 时间格式正确")
        else:
            print("❌ 时间格式不正确")
            return False
    
    # 4. 生成威胁分数
    print("\n4. 生成威胁分数...")
    result_df = generate_threat_scores(df)
    
    print(f"威胁评分结果: {len(result_df)} 条记录")
    print(f"结果列: {list(result_df.columns)}")
    
    # 5. 统计分析结果
    print("\n5. 统计分析结果...")
    
    # 流向统计
    if 'flow_direction' in df.columns:
        flow_stats = df['flow_direction'].value_counts()
        print("\n流向分布:")
        for direction, count in flow_stats.items():
            print(f"  {direction}: {count}条")
    
    # 业务相关性统计
    if 'is_business_related' in df.columns:
        business_stats = df['is_business_related'].value_counts()
        print("\n业务相关性分布:")
        for is_business, count in business_stats.items():
            status = "业务相关" if is_business else "非业务相关"
            print(f"  {status}: {count}条")
    
    # 工作时间统计
    if 'is_work_time' in df.columns:
        work_time_stats = df['is_work_time'].value_counts()
        print("\n工作时间分布:")
        for is_work_time, count in work_time_stats.items():
            status = "工作时间" if is_work_time else "非工作时间"
            print(f"  {status}: {count}条")
    
    # 威胁等级统计
    if 'threat_level' in result_df.columns:
        threat_stats = result_df['threat_level'].value_counts()
        print("\n威胁等级分布:")
        for level, count in threat_stats.items():
            print(f"  {level}: {count}条")
    
    # 威胁分数统计
    if 'threat_score' in result_df.columns:
        print(f"\n威胁分数统计:")
        print(f"  平均分: {result_df['threat_score'].mean():.2f}")
        print(f"  最高分: {result_df['threat_score'].max():.2f}")
        print(f"  最低分: {result_df['threat_score'].min():.2f}")
    
    print("\n✅ 集成测试成功！")
    return True

if __name__ == "__main__":
    test_step5_integration()