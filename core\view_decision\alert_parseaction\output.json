{"name": "Web组件信息泄露漏洞", "remarks": "发现时间: 8月28日14:54 | 攻击IP: ************* | 被攻击IP: ************** | 端口: 18080 | ", "node_info": {"node1": {"app_id": "start", "app_type": 0}, "node2": {"app_id": "end", "app_type": 0}, "node3": {"app_id": "nmap", "app_type": 1, "information": {"action": [{"name": "端口扫描", "func": "scan"}], "app_dir": "nmap", "args": {"scan": [{"key": "target", "type": "text", "required": true}, {"key": "ports", "type": "text", "required": true}, {"key": "protocol", "type": "select", "required": true, "default": "tcp", "data": ["tcp", "udp"]}]}, "description": "一款端口扫描工具", "icon": "nmap/icon.png", "identification": "w5soar", "is_public": true, "name": "nmap", "type": "安全扫描", "version": "0.1", "data": {"node_name": "nmap", "action": "scan", "action_name": "端口扫描", "description": "一款端口扫描工具", "target": "@(node3.result)", "ports": "80,443", "protocol": "tcp"}}}, "node4": {"app_id": "linux", "app_type": 1, "information": {"action": [{"name": "执行命令", "func": "execute"}], "app_dir": "linux", "args": {"execute": [{"key": "host", "type": "text", "required": true}, {"key": "port", "type": "number", "required": true, "default": 22}, {"key": "user", "type": "text", "required": true, "default": "root"}, {"key": "passwd", "type": "text", "required": true}, {"key": "shell", "type": "textarea", "required": true}]}, "description": "Linux SSH 客户端，执行远程命令", "icon": "linux/icon.png", "identification": "w5soar", "is_public": true, "name": "Linux远程命令", "type": "命令执行", "version": "0.1", "data": {"node_name": "Linux远程命令", "action": "execute", "action_name": "执行命令", "description": "Linux SSH 客户端，执行远程命令", "host": "@(node4.result)", "port": "@(node4.result)", "user": "root", "passwd": "password", "shell": "ls"}}}, "node5": {"app_id": "mysql", "app_type": 1, "information": {"action": [{"name": "查询", "func": "query"}, {"name": "增删改", "func": "update"}], "app_dir": "mysql", "args": {"query": [{"key": "host", "type": "text", "required": true, "default": "localhost"}, {"key": "port", "type": "number", "required": true, "default": 3306}, {"key": "user", "type": "text", "required": true}, {"key": "passwd", "type": "text", "required": true}, {"key": "db", "type": "text", "required": true}, {"key": "sql", "type": "textarea", "required": true}], "update": [{"key": "host", "type": "text", "required": true, "default": "localhost"}, {"key": "port", "type": "number", "required": true, "default": 3306}, {"key": "user", "type": "text", "required": true}, {"key": "passwd", "type": "text", "required": true}, {"key": "db", "type": "text", "required": true}, {"key": "sql", "type": "textarea", "required": true}]}, "description": "一个常用的关系型数据库管理系统", "icon": "mysql/icon.png", "identification": "w5soar", "is_public": true, "name": "Mysql", "type": "数据引擎", "version": "0.1", "data": {"node_name": "Mysql", "action": "query", "action_name": "查询", "description": "一个常用的关系型数据库管理系统", "host": "@(node5.result)", "port": "@(node5.result)", "user": "root", "passwd": "password", "db": "testdb", "sql": "SELECT 1"}}}, "node6": {"app_id": "linux", "app_type": 1, "information": {"action": [{"name": "执行命令", "func": "execute"}], "app_dir": "linux", "args": {"execute": [{"key": "host", "type": "text", "required": true}, {"key": "port", "type": "number", "required": true, "default": 22}, {"key": "user", "type": "text", "required": true, "default": "root"}, {"key": "passwd", "type": "text", "required": true}, {"key": "shell", "type": "textarea", "required": true}]}, "description": "Linux SSH 客户端，执行远程命令", "icon": "linux/icon.png", "identification": "w5soar", "is_public": true, "name": "Linux远程命令", "type": "命令执行", "version": "0.1", "data": {"node_name": "Linux远程命令", "action": "execute", "action_name": "执行命令", "description": "Linux SSH 客户端，执行远程命令", "host": "@(node6.result)", "port": "@(node6.result)", "user": "root", "passwd": "password", "shell": "ls"}}}, "node7": {"app_id": "email", "app_type": 1, "information": {"action": [{"name": "邮件发送", "func": "send"}], "app_dir": "email", "args": {"send": [{"key": "host", "type": "text", "required": true}, {"key": "port", "type": "number", "required": true, "default": 25}, {"key": "user", "type": "text", "required": true}, {"key": "passwd", "type": "text", "required": true}, {"key": "encrypt", "type": "select", "required": true, "default": "none", "data": ["none", "tsl", "ssl"]}, {"key": "sender", "type": "text", "required": true}, {"key": "to", "type": "text", "required": true}, {"key": "title", "type": "text", "required": true}, {"key": "type", "type": "select", "required": true, "default": "text", "data": ["text", "html"]}, {"key": "text", "type": "textarea", "required": true}]}, "description": "可以发送邮件的APP", "icon": "email/icon.png", "identification": "w5soar", "is_public": true, "name": "E-Mail", "type": "消息通知", "version": "0.1", "data": {"node_name": "E-Mail", "action": "send", "action_name": "邮件发送", "description": "可以发送邮件的APP", "host": "smtp.163.com", "port": 465, "user": "<EMAIL>", "passwd": "UTJAgddhdBDsaU7m", "encrypt": "ssl", "sender": "<EMAIL>", "to": "<EMAIL>", "title": "@(node7.result)", "type": "plain", "text": "1. 本次决策处理效果简述：自动化完成Web组件信息泄露漏洞的端口扫描、系统命令执行、数据库查询及多通道告警通知  \n2. 处理涉及的关键参数：目标端口80/443（TCP）、SSH凭据（root/password）、MySQL查询（testdb）、邮件/SMTP配置、飞书webhook  \n3. 处理方式简介：通过nmap扫描检测开放端口，Linux远程执行基础命令验证访问，MySQL进行数据库连通性检查，最终通过邮件和飞书同步发送告警通知  \n4. 运维友好通知内容：Web组件信息泄露漏洞已自动处置，完成端口扫描与系统检查，请及时登录确认详情。"}}}, "node8": {"app_id": "feishu", "app_type": 1, "information": {"action": [{"name": "飞书通知", "func": "send"}], "app_dir": "feishu", "args": {"send": [{"key": "hook_uuid", "type": "text", "required": true}, {"key": "msg", "type": "text", "required": true}]}, "description": "飞书消息通知", "icon": "feishu/icon.png", "identification": "w5soar", "is_public": true, "name": "飞书通知", "type": "消息通知", "version": "0.1", "data": {"node_name": "飞书通知", "action": "send", "action_name": "飞书通知", "description": "飞书消息通知", "hook_uuid": "27bfbd32-aea8-4496-8f4c-ac4edc829a2f", "msg": "1. 本次决策处理效果简述：自动化完成Web组件信息泄露漏洞的端口扫描、系统命令执行、数据库查询及多通道告警通知  \n2. 处理涉及的关键参数：目标端口80/443（TCP）、SSH凭据（root/password）、MySQL查询（testdb）、邮件/SMTP配置、飞书webhook  \n3. 处理方式简介：通过nmap扫描检测开放端口，Linux远程执行基础命令验证访问，MySQL进行数据库连通性检查，最终通过邮件和飞书同步发送告警通知  \n4. 运维友好通知内容：Web组件信息泄露漏洞已自动处置，完成端口扫描与系统检查，请及时登录确认详情。"}}}}, "edge_info": [{"source": {"cell": "node1", "port": "right"}, "target": {"cell": "node3", "port": "left"}}, {"source": {"cell": "node3", "port": "right"}, "target": {"cell": "node4", "port": "left"}}, {"source": {"cell": "node4", "port": "right"}, "target": {"cell": "node5", "port": "left"}}, {"source": {"cell": "node5", "port": "right"}, "target": {"cell": "node6", "port": "left"}}, {"source": {"cell": "node6", "port": "right"}, "target": {"cell": "node7", "port": "left"}}, {"source": {"cell": "node7", "port": "right"}, "target": {"cell": "node8", "port": "left"}}, {"source": {"cell": "node8", "port": "right"}, "target": {"cell": "node2", "port": "left"}}], "local_var_data": [], "controller_data": {}}